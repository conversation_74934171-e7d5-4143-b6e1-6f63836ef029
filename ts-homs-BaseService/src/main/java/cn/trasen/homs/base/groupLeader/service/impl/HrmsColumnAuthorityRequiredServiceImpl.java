package cn.trasen.homs.base.groupLeader.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.base.groupLeader.mapper.HrmsColumnAuthorityRequiredMapper;
import cn.trasen.homs.base.groupLeader.model.HrmsColumnAuthorityRequired;
import cn.trasen.homs.base.groupLeader.service.HrmsColumnAuthorityRequiredService;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import tk.mybatis.mapper.util.StringUtil;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName HrmsColumnAuthorityRequiredServiceImpl
 * @Description TODO
 * @date 2023��12��4�� ����10:36:37
 */
@Primary
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsColumnAuthorityRequiredServiceImpl implements HrmsColumnAuthorityRequiredService {

	@Autowired
	private HrmsColumnAuthorityRequiredMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsColumnAuthorityRequired record) {

		if (!StringUtil.isEmpty(record.getColumnId())) {   //新增编辑
			HrmsColumnAuthorityRequired del = new HrmsColumnAuthorityRequired();
			del.setColumnId(record.getColumnId());
			mapper.delete(del);
			if (!StringUtil.isEmpty(record.getPersonalIdentity())) {//删除岗位的所有授权
				List<String> personalIdentity = Arrays.asList(record.getPersonalIdentity().split(","));
				List<String> personalIdentityName = Arrays.asList(record.getPersonalIdentityName().split(","));
				for (int i = 0; i < personalIdentity.size(); i++) {
					HrmsColumnAuthorityRequired _newBean = new HrmsColumnAuthorityRequired();
					_newBean.setColumnId(record.getColumnId());
					_newBean.setPersonalIdentity(personalIdentity.get(i));
					_newBean.setPersonalIdentityName(personalIdentityName.get(i));
					_newBean.setId(String.valueOf(IdWork.id.nextId()));
					_newBean.setCreateDate(new Date());
					_newBean.setUpdateDate(new Date());
					_newBean.setIsDeleted("N");
					ThpsUser user = UserInfoHolder.getCurrentUserInfo();
					if (user != null) {
						_newBean.setCreateUser(user.getUsercode());
						_newBean.setCreateUserName(user.getUsername());
						_newBean.setUpdateUser(user.getUsercode());
						_newBean.setUpdateUserName(user.getUsername());
					}
					mapper.insertSelective(_newBean);

				}
				return personalIdentity.size();
			}
		}
		return 0;
	}


	@Override
	public List<HrmsColumnAuthorityRequired> selectById(String id) {
		
		//如果是管理员进来 返回空
		Boolean right = UserInfoHolder.getRight("SYS_ARCHIVIST");
		Boolean isadmin = UserInfoHolder.ISADMIN();

		if(right  || isadmin){
			List<HrmsColumnAuthorityRequired> ret = new ArrayList<>();
			return ret;
		}

		Assert.hasText(id, "ID不能为空.");
		HrmsColumnAuthorityRequired sel = new HrmsColumnAuthorityRequired();
		sel.setPersonalIdentity(id);
		sel.setIsDeleted("N");
		return mapper.select(sel);
	}


	@Override
	public List<HrmsColumnAuthorityRequired> bycolumnId(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsColumnAuthorityRequired sel = new HrmsColumnAuthorityRequired();
		sel.setColumnId(id);
		sel.setIsDeleted("N");
		return mapper.select(sel);
	}
}
