import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取工资条详情**/
  getSalaryDetailsList(type, dateStr) {
    return request.get(
      `${apiConfig.oa()}/salary/salarySet/salaryList/getSalaryDetailsList${type}/${dateStr}`
    );
  },
  /**@desc 获取工资条配置**/
  getSalaryTypeMenu() {
    return request.get(`${apiConfig.oa()}/salary/salarySet/salaryTypeMenu`);
  },
  /**@desc 获取工资条**/
  getSalaryDataList() {
    return request.post(`${apiConfig.oa()}/salary/salarySet/list`);
  },
  /**@desc 获取工资条**/
  getPayrollByEmployee(params) {
    return request.get(
      `${apiConfig.hrms()}/api/sendPaySheet/getPayrollByEmployee`,
      { params }
    );
  },
  /**@desc 获取工资条详情**/
  getPayrollByEmployeeDetails(data) {
    return request.post(
      `${apiConfig.hrms()}/api/salaryPayroll/getPayrollByEmployeeDetails`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
