import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取人员 */
  getWorksheetPersonList(api, params) {
    return request.post(`${apiConfig.worksheet()}${api}`, params);
  },
  getPersonListFullPathPost(api, method = 'GET', params) {
    return request.request({
      method: method.toUpperCase(), // 请求方法必须大写
      url: api || `${apiConfig.oa()}/employee/getEmployeeList`,
      data: params
    });
  }
};
