import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取日程详情**/
  getScheduleDetails(datas) {
    return request.post(
      `${apiConfig.oa()}/schedule/selectScheduleDetails`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 删除日程**/
  deleteSchedule(datas) {
    return request.post(`${apiConfig.oa()}/schedule/delete`, datas);
  },
  /**@desc 更新日程列表**/
  insertOrUpdateSchedule(datas) {
    return request.post(`${apiConfig.oa()}/schedule/insertOrUpdate`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取日程列表**/
  getSelectedDateSchedule(datas) {
    return request.get(`${apiConfig.oa()}/schedule/selectScheduleDate`, {
      params: datas
    });
  },
  /**@desc 获取日程列表**/
  getScheduleDetailsList(datas) {
    return request.post(
      `${apiConfig.oa()}/schedule/selectScheduleDetailsList`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
