import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取问卷列表**/
  getReleaselist(params) {
    return request.get(`${apiConfig.oa()}/api/EvaluationRelease/getlist`, {
      params
    });
  },
  /**@desc 问卷详情**/
  getPreviewQuestionnaire(params) {
    return request.get(
      `${apiConfig.oa()}/api/EvaluationSet/previewQuestionnaire?masterId=${
        params.masterId
      }`,
      params
    );
  },
  /**@desc 问卷列表计数**/
  selectReleaseCount(params) {
    return request.get(
      `${apiConfig.oa()}/api/EvaluationRelease/selectReleaseCount`,
      {
        params
      }
    );
  },
  /**@desc 提交问卷**/
  saveEvaluationResult(datas) {
    return request.post(
      `${apiConfig.oa()}/api/EvaluationRelease/saveEvaluationResult`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 问卷详情 **/
  selectEvaluationResult(datas) {
    return request.post(
      `${apiConfig.oa()}/api/EvaluationRelease/selectEvaluationResult`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
