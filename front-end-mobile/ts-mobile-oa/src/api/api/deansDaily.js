import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  LeaderDailyPageList(params) {
    return request.get(`${apiConfig.oa()}/api/LeaderDaily/PageList`, {
      params,
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  LeaderDailyDetails(id) {
    return request.get(`${apiConfig.oa()}/api/LeaderDaily/${id}`);
  },

  hrindexGetDayDyna(datas) {
    return request.post(`${apiConfig.hrms()}/hrindex/getDayDyna`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  personnelTransactionList(datas) {
    return request.post(`${apiConfig.hrms()}/personnelTransaction/list`, datas);
  },

  deansDailySearchOutRecordList(params) {
    return request.get(`${apiConfig.hrms()}/api/outRecord/list`, {
      params
    });
  },

  getLeaveReportStatistics(params) {
    return request.get(
      `${apiConfig.hrms()}/api/leaveStatistics/getLeaveReportStatistics`,
      {
        params
      }
    );
  },

  LeaderDailyReadClickSaveUpdate(datas) {
    return request.post(
      `${apiConfig.oa()}/api/LeaderDailyRead/clickSaveUpdate`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  hrindexGetPsnTrns(datas) {
    return request.post(`${apiConfig.hrms()}/hrindex/getPsnTrns`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  hrindexGetPsnDstn(datas) {
    return request.post(`${apiConfig.hrms()}/hrindex/getPsnDstn`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  gatewayGetLeaderDailyAllSingleZb(params) {
    return request.get(
      `${apiConfig.external()}/gateway/getLeaderDailyAllSingleZb`,
      {
        params,
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  LeaderDailyGetIncomeTrendByDate(params) {
    return request.get(`${apiConfig.external()}/gateway/getIncomeTrendByDate`, {
      params,
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  LeaderDailyGetDeptZbPageTy(params) {
    return request.get(`${apiConfig.external()}/gateway/getDeptZbPageTy`, {
      params,
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  LeaderDailyGetLeaderweeklyReport(params) {
    return request.get(
      `${apiConfig.external()}/gateway/getLeaderweeklyReport`,
      {
        params,
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  getBedOverviewDate() {
    return request.get(
      `${apiConfig.external()}/BedOverview/getBedOverviewDate`,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
