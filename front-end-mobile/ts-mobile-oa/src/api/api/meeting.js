import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取会议室申请信息**/
  getMeetingDatas(id) {
    return request.post(
      `${apiConfig.resource()}/boardroom/apply/selectBusinessIdDetail/${id}`
    );
  },
  /**@desc 获取会议类型**/
  getMeetingType() {
    return request.post(`${apiConfig.resource()}/boardroom/apptype/all`);
  },

  /**@desc 获取会议类型**/
  boardRoomApplyGet(datas) {
    return request.post(`${apiConfig.oa()}/boardRoomApply/get`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  boardRoomGet(datas) {
    return request.post(`${apiConfig.oa()}/boardRoom/get`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  getDictItemByTypeCode(data) {
    return request.get(
      `${apiConfig.basics()}/dictItem/getDictItemByTypeCode`,
      data
    );
  },

  getTaskHisList(data) {
    return request.get(`${apiConfig.workflow()}/workflow/task/his/list`, data);
  },

  /**@desc 获取会议室（地点）**/
  getMeetingAddress(datas) {
    return request.post(
      `${apiConfig.resource()}/boardroom/place/select`,
      datas
    );
  },
  /**@desc 提交会议申请**/
  saveMeetingApply(datas) {
    return request.post(`${apiConfig.resource()}/boardroom/apply/save`, datas);
  },
  /**@desc 获取签到列表**/
  getSignedGroupCount(datas) {
    return request.post(
      `${apiConfig.resource()}/boardroom/signin/getSignedGroupCount`,
      datas
    );
  },
  /**@desc 获取签到统计数据**/
  getSignedDetailsList(datas) {
    return request.post(
      `${apiConfig.resource()}/boardroom/signin/getSignedDetailsList`,
      datas
    );
  },
  /**@desc 获取会议室使用情况列表**/
  getBoardroomApplyBylist(datas) {
    return request.post(
      `${apiConfig.resource()}/boardroom/place/selectBoardroomApplyBylist`,
      datas
    );
  },
  /**@desc 获取会议室申请列表**/
  getBoardroomApplyList(datas) {
    return request.post(`${apiConfig.resource()}/boardroom/apply/list`, datas);
  },
  /**@desc 获取会议详情**/
  getMeetingDetail(id) {
    return request.post(
      `${apiConfig.resource()}/boardroom/apply/selectApplyDetail/${id}`
    );
  },
  /**@desc 更新会议室状态**/
  updateMeeting(datas) {
    return request.post(
      `${apiConfig.resource()}/boardroomadmin/meetingtime/update`,
      datas
    );
  },
  getMeetingConsulte(datas) {
    return request.post(
      `${apiConfig.resource()}/boardroom/apply/meetingConsult`,
      datas
    );
  },
  /**@desc 获取我的会议**/
  getMyBoardroomMeeting(datas) {
    return request.get(
      `${apiConfig.resource()}/boardroom/apply/getMyBoardroomMeeting`,
      {
        params: datas
      }
    );
  },
  /**@desc 会议操作**/
  confirmBoardroomMeeting(datas) {
    return request.post(
      `${apiConfig.resource()}/boardroom/signin/boardroomMeetingConfirm`,
      datas
    );
  },
  /**@desc 会议室扫码**/
  boardroomQrCodeSign(datas) {
    return request.post(
      `${apiConfig.resource()}/boardroom/signin/boardroomQrCodeSign`,
      datas
    );
  }
};
