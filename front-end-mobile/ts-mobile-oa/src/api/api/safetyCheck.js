import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取路线列表**/
  getSafetyRouteList(datas) {
    return request.post(`${apiConfig.oa()}/api/checkroute/list`, datas);
  },
  /**@desc 获取路线地点**/
  getRouteAdressList(id) {
    return request.get(`${apiConfig.oa()}/api/checkroute/findById/${id}`);
  },
  /**@desc 获取路线地点详情**/
  getRouteDetail(id) {
    return request.get(`${apiConfig.oa()}/api/startCheck/findById/${id}`);
  },
  /**@desc 保存路线地点详情**/
  saveSafetyContent(type, datas) {
    return request.post(`${apiConfig.oa()}/api/startCheck/${type}`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 完成整改**/
  finishRectification(datas) {
    return request.post(
      `${apiConfig.oa()}/api/checkRectification/finishRectification`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 复查不通过**/
  batchCheckRectification(datas) {
    return request.post(
      `${apiConfig.oa()}/api/checkRectification/batchCheckRectification`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取安全整改列表**/
  getRectificationList(datas) {
    return request.post(`${apiConfig.oa()}/api/checkRectification/list`, datas);
  },
  /**@desc 安全整改操作（通过/不通过）**/
  examineRectification(datas) {
    return request.post(
      `${apiConfig.oa()}/api/checkRectification/checkRectification`,
      datas
    );
  },
  /**@desc 获取安全检查详情**/
  getCheckRectificationDetailById(id) {
    return request.get(
      `${apiConfig.oa()}/api/checkRectification/findCheckRectificationDetailById/${id}`
    );
  },
  /**@desc 安全检查操作**/
  confirmSafety(datas) {
    return request.post(`${apiConfig.oa()}/api/startCheck/confirm`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 获取安全检查列表**/
  getSafetyList(datas) {
    return request.post(`${apiConfig.oa()}/api/startCheck/list`, datas);
  },
  /**@desc 获取安全检查操作日志**/
  getCheckOperationLogsByCheckId(id) {
    return request.get(
      `${apiConfig.oa()}/api/getCheckOperationLogsByCheckId/${id}`
    );
  },
  /**@desc 删除安全检查**/
  deleteSafety(id) {
    return request.post(`${apiConfig.oa()}/api/startCheck/delete/${id}`);
  }
};
