export default {
  formatDateText(dateVal) {
    if (dateVal) {
      let date = new Date(dateVal),
        YY = date.getFullYear(),
        MM = String(date.getMonth() + 1).replace(/(^\d{1}$)/, '0$1'),
        DD = String(date.getDate()).replace(/(^\d{1}$)/, '0$1'),
        time = `${YY}年${MM}月${DD}日`;
      return time;
    }
    return '';
  },
  // 校验两位小数
  inputTowDecimalPlaces(value) {
    let matchList = value.match(/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2}|\.{1})?/);
    if (!matchList) {
      return null;
    }
    return matchList[0];
  },
  /**
   * 字符串隐藏（*）
   * @param {Object} str：字符串，frontLen：前面保留位数，endLen：后面保留位数。
   * @return
   */
  plusxing: function(str, frontLen, endLen) {
    var len = str.length - frontLen - endLen;
    var xing = '';
    for (var i = 0; i < len; i++) {
      xing += '*';
    }
    return (
      str.substring(0, frontLen) + xing + str.substring(str.length - endLen)
    );
  },

  /**
   * 日期自动补0
   * @param {String | Number} num 日期
   * @return {String}
   */
  formatDateNum(num) {
    return String(num).replace(/(^\d{1}$)/, '0$1');
  },

  //获取时间对象
  getDate: function(type = 'date', paramVal = '') {
    var time = {},
      dDate;
    /* 如果日期框内为空的话就获取系统的时间为输入框初始化赋值，如果有值（用户自己选择的时间），那就获取用户自己选择的时间 */
    if (typeof paramVal == 'string')
      dDate = paramVal ? new Date(paramVal.replace(/-/g, '/')) : new Date();
    else dDate = new Date(paramVal);
    time.year = dDate.getFullYear();
    time.month = dDate.getMonth() + 1;
    time.day = dDate.getDate();
    time.hour = dDate.getHours();
    time.minute = dDate.getMinutes();
    time.seconds = dDate.getSeconds();
    time.week = this.getWeek(dDate);
    var Y = dDate.getFullYear(),
      M = this.formatDateNum(dDate.getMonth() + 1),
      D = this.formatDateNum(dDate.getDate()),
      h = this.formatDateNum(dDate.getHours()),
      m = this.formatDateNum(dDate.getMinutes()),
      s = this.formatDateNum(dDate.getSeconds());
    switch (type) {
      case 'date':
        time.dateStr = time.year + '-' + time.month + '-' + time.day;
        time.timeStr = Y + '-' + M + '-' + D;
        break;
      case 'month':
        time.dateStr = time.year + '-' + time.month;
        time.timeStr = Y + '-' + M;
        break;
      case 'hour':
        time.dateStr =
          time.year +
          '-' +
          time.month +
          '-' +
          time.day +
          ' ' +
          time.hour +
          ':' +
          time.minute;
        time.timeStr = Y + '-' + M + '-' + D + ' ' + h + ':' + m;
        break;
      case 'time':
        time.dateStr =
          time.year +
          '-' +
          time.month +
          '-' +
          time.day +
          ' ' +
          time.hour +
          ':' +
          time.minute +
          ':' +
          time.seconds;
        time.timeStr = Y + '-' + M + '-' + D + ' ' + h + ':' + m + ':' + s;
        break;
      case 'dateStr':
        time.dateStr = time.year + '年' + time.month + '月' + time.day + '日';
        time.timeStr = Y + '年' + M + '月' + D + '日';
        break;
      case 'monthStr':
        time.dateStr = time.year + '年' + time.month + '月';
        time.timeStr = Y + '年' + M + '月';
        break;
      case 'hourStr':
        time.dateStr =
          time.year +
          '年' +
          time.month +
          '月' +
          time.day +
          '日' +
          ' ' +
          time.hour +
          ':' +
          time.minute;
        time.timeStr = Y + '年' + M + '月' + D + '日 ' + h + ':' + m;
        break;
      case 'timeStr':
        time.dateStr =
          time.year +
          '年' +
          time.month +
          '月' +
          time.day +
          '日' +
          ' ' +
          time.hour +
          ':' +
          time.minute +
          ':' +
          time.seconds;
        time.timeStr = Y + '年' + M + '月' + D + '日 ' + h + ':' + m + ':' + s;
        break;
    }
    return time;
  },
  getWeek: function(dateObj) {
    var week;
    switch (dateObj.getDay()) {
      case 1:
        week = '星期一';
        break;
      case 2:
        week = '星期二';
        break;
      case 3:
        week = '星期三';
        break;
      case 4:
        week = '星期四';
        break;
      case 5:
        week = '星期五';
        break;
      case 6:
        week = '星期六';
        break;
      default:
        week = '星期天';
    }
    return week;
  },
  /**
   * 获取前多少月
   * @param {Object} dateVal 时间
   * @param {Object} preNum 前多少月
   * @return {Object} time
   */
  getMonth(dateVal = '', preNum = 0) {
    var dDate = dateVal ? new Date(dateVal.replace(/-/g, '/')) : new Date(),
      date = new Date(
        dDate.getFullYear(),
        dDate.getMonth() - Number(preNum),
        1
      ),
      time = this.getDate('month', date);
    return time;
  },

  //设置cookie
  setCookie: function(key, value, t) {
    if (t) {
      var tDate = new Date();
      tDate.setDate(tDate.getDate() + t);
      document.cookie = key + '=' + value + ';expries =' + oDate.toGMTString();
    } else {
      document.cookie = key + '=' + value;
    }
  },
  //排序
  compare: function(property, desc) {
    return function(a, b) {
      var value1 = a[property];
      var value2 = b[property];
      if (desc == true) {
        // 升序排列
        return value1 - value2;
      } else {
        // 降序排列
        return value2 - value1;
      }
    };
  },
  //获取路由及参数
  getCurPage: function() {
    var routes = getCurrentPages(); // 获取当前打开过的页面路由数组
    var curRoute = routes[routes.length - 1].route; //获取当前页面路由
    //在微信小程序或是app中，通过curPage.options；如果是H5，则需要curPage.$route.query（H5中的curPage.options为undefined，所以刚好就不需要条件编译了）
    var curParam = routes[routes.length - 1].options; //获取路由参数
    // 拼接参数
    var param = '';
    if (curParam) {
      for (var key in curParam) {
        param += `${key}=${curParam[key]}&`;
      }
      param = `?${param.substring(0, param.length - 1)}`;
    }
    return `/${curRoute}${param}`;
  },
  //根据身份证获取出生日期
  getBirthdayFromIdCard: function(idCard) {
    var birthday = '';
    if (idCard != null && idCard != '') {
      if (idCard.length == 15) {
        birthday = '19' + idCard.substr(6, 6);
      } else if (idCard.length == 18) {
        birthday = idCard.substr(6, 8);
      }
      birthday = birthday.replace(/(.{4})(.{2})/, '$1-$2-');
    }
    return birthday;
  },
  //身份证校验
  checkIDCard(idcode) {
    // 加权因子
    var weight_factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    // 校验码
    var check_code = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

    var code = idcode + '';
    var last = idcode[17]; //最后一位

    var seventeen = code.substring(0, 17);

    // ISO 7064:1983.MOD 11-2
    // 判断最后一位校验码是否正确
    var arr = seventeen.split('');
    var len = arr.length;
    var num = 0;
    for (var i = 0; i < len; i++) {
      num = num + arr[i] * weight_factor[i];
    }

    // 获取余数
    var resisue = num % 11;
    var last_no = check_code[resisue];

    // 格式的正则
    // 正则思路
    /*
    第一位不可能是0
    第二位到第六位可以是0-9
    第七位到第十位是年份，所以七八位为19或者20
    十一位和十二位是月份，这两位是01-12之间的数值
    十三位和十四位是日期，是从01-31之间的数值
    十五，十六，十七都是数字0-9
    十八位可能是数字0-9，也可能是X
    */
    var idcard_patter = /^[1-9][0-9]{5}([1][9][0-9]{2}|[2][0][0|1][0-9])([0][1-9]|[1][0|1|2])([0][1-9]|[1|2][0-9]|[3][0|1])[0-9]{3}([0-9]|[X])$/;

    // 判断格式是否正确
    var format = idcard_patter.test(idcode);

    // 返回验证结果，校验码和格式同时正确才算是合法的身份证号码
    return last === last_no && format ? true : false;
  },
  //获取年龄
  getAgeFromIdCard: function(idCard) {
    var age = 0;
    if (idCard != null && idCard != '') {
      var birthday = '';
      if (idCard.length == 15) {
        birthday = '19' + idCard.substr(6, 6);
      } else if (idCard.length == 18) {
        birthday = idCard.substr(6, 8);
      }
      birthday = birthday.replace(/(.{4})(.{2})/, '$1-$2-');
      var myDate = new Date();
      var month = myDate.getMonth() + 1;
      var day = myDate.getDate();
      age = myDate.getFullYear() - Number(birthday.substring(0, 4)) - 1;
      if (
        Number(birthday.substring(5, 7)) < month ||
        (Number(birthday.substring(5, 7)) == month &&
          Number(birthday.substring(8, 10)) <= day)
      ) {
        age++;
      }
    }
    return age;
  },
  /**
   * 提示框二次封装 confirm
   * @param {String} content 提示框内容
   * @param {String} title 提示框标题
   * @param {String} confirmText 确定按钮的文字,最多 4 个字符
   * @param {String} showCancel 是否显示取消按钮
   * @param {String} cancelText 取消按钮的文字
   */
  confirm: function(
    content,
    title = '提示',
    confirmText = '确定',
    showCancel = false,
    cancelText = ''
  ) {
    return new Promise((resolve, reject) => {
      uni.showModal({
        title,
        content,
        confirmText,
        confirmColor: '#005BAC',
        showCancel,
        cancelText,
        success: res => {
          if (res.confirm) {
            resolve(true);
          } else if (res.cancel) {
            resolve(false);
          }
        }
      });
    });
  },
  /**
   * 提示框二次封装 toast
   * @param {String} title 提示框内容
   * @param {String} duration 提示的延迟时间
   * @param {String} icon 图标
   */
  toast: function(title, duration = 1500, icon = 'none') {
    return new Promise((resolve, reject) => {
      uni.showToast({
        title,
        icon,
        success: () => {
          setTimeout(() => {
            resolve();
          }, duration);
        }
      });
    });
  },
  /**
   * loading 提示框二次封装 loadingToast
   * @param {String} title 提示框内容
   * @param {String} mask 是否显示透明蒙层，防止触摸穿透
   */
  loadingToast: function(title, mask = true) {
    return new Promise((resolve, reject) => {
      uni.showLoading({
        title,
        mask,
        success: () => {
          resolve();
        }
      });
    });
  },
  randomString(len) {
    len = len || 32;
    var $chars = '-abcdefhijkmnprstwxyz123456789';
    var maxPos = $chars.length;
    var pwd = '';
    for (var i = 0; i < len; i++) {
      pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
  },
  /**
   * 生成随机数
   */
  guid() {
    function S4() {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }
    return (
      S4() +
      S4() +
      '-' +
      S4() +
      '-' +
      S4() +
      '-' +
      S4() +
      '-' +
      S4() +
      S4() +
      S4()
    );
  },

  //对象转换为URL参数
  convertObj(data) {
    var _result = [];
    for (var key in data) {
      var value = data[key];
      if (value.constructor == Array) {
        value.forEach(function(_value) {
          _result.push(key + '=' + _value);
        });
      } else {
        _result.push(key + '=' + value);
      }
    }
    return _result.join('&');
  },

  deepClone: function(source) {
    if (!source && typeof source !== 'object') {
      throw new Error('error arguments', 'deepClone');
    }
    const targetObj = source.constructor === Array ? [] : {};
    Object.keys(source).forEach(keys => {
      if (source[keys] && typeof source[keys] === 'object') {
        targetObj[keys] = this.deepClone(source[keys]);
      } else {
        targetObj[keys] = source[keys];
      }
    });
    return targetObj;
  },

  isDoc: function(str) {
    return /\.(doc|docx|xls|xlsx|pdf|ppt|pptx|txt|mp4|zip|rar|7z|)$/.test(
      str.toLowerCase()
    );
  },
  isImg: function(str) {
    return /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(str.toLowerCase());
  },
  random4: function() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  },
  createUUID: function() {
    return (
      this.random4() +
      this.random4() +
      this.random4() +
      this.random4() +
      this.random4() +
      this.random4()
    );
  },
  measureText: function(text, fontSize = 14) {
    var width = 0;
    var text = text.split('');
    for (let i = 0; i < text.length; i++) {
      let item = text[i];
      if (/[a-zA-Z]/.test(item)) {
        width += 7;
      } else if (/[0-9]/.test(item)) {
        width += 5.5;
      } else if (/\./.test(item)) {
        width += 2.7;
      } else if (/-/.test(item)) {
        width += 3.25;
      } else if (/:/.test(item)) {
        width += 2.5;
      } else if (/[\u4e00-\u9fa5]/.test(item)) {
        width += 10;
      } else if (/\(|\)/.test(item)) {
        width += 3.73;
      } else if (/\s/.test(item)) {
        width += 2.5;
      } else if (/%/.test(item)) {
        width += 8;
      } else {
        width += 10;
      }
    }
    return (width * fontSize) / 10;
  }
};
