import host from '@/common/js/setting.js';
import store from '@/store';

//异步请求
const syncRequest = (param, backpage) => {
  let option = {};
  //完善请求地址
  option.url =
    (param.baseurl ? param.baseurl : host.BASE_HOST) +
    param.api +
    (param.api.indexOf('?') === -1 ? '?source=mobile' : '&source=mobile');
  //传参
  option.data = param.data || {};
  //请求方式
  option.method = param.method || 'GET';
  //携带cookies
  //option.withCredentials = true;
  //请求头
  option.header =
    param.method == 'GET'
      ? {
          'X-Requested-With': 'XMLHttpRequest',
          Accept: 'application/json',
          'Content-Type': 'application/json; charset=UTF-8'
        }
      : {
          'X-Requested-With': 'XMLHttpRequest',
          Accept: 'application/json',
          //'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
          'Content-Type': param.contentType
            ? param.contentType
            : 'application/json; charset=UTF-8'
        };
  //是否显示加载中(true:不显示，false:显示)
  const showLoading = param.showLoading || false;
  //是否隐藏加载中(true:隐藏，false:不隐藏)
  const hideLoading = param.hideLoading || false;
  const loadingTitle = param.loadingTitle || '';
  //用户交互:加载圈
  if (showLoading) {
    uni.showLoading({ title: loadingTitle, mask: true });
  }
  //是否为分页接口
  const isPagination = param.isPagination || false;
  //请求成功
  option.success = res => {
    let statusCode = null,
      msg = null;
    setTimeout(uni.hideLoading, 100);
    if (res.statusCode && res.statusCode != 200) {
      //api错误
      uni.showModal({
        title: '提示',
        showCancel: false,
        confirmColor: '#005BAC',
        content: '网络异常,请稍后重试'
      });
      return;
    }
    //判断是否为分页接口
    if (isPagination && res.data.statusCode === undefined) {
      statusCode = res.statusCode;
      msg = res.errMsg;
    } else {
      statusCode = res.data.statusCode;
      msg = res.data.message;
    }
    //返回状态码statusCode判断:200成功,21000登录失效,0:SSO修改密码成功
    if (statusCode == 21000) {
      store.dispatch('goToLogin');
    } else if (
      (res.data.success != undefined && !res.data.success) ||
      (statusCode != 200 && statusCode != 0)
    ) {
      setTimeout(uni.hideLoading, 100);
      uni.showModal({
        title: '提示',
        showCancel: false,
        confirmColor: '#005BAC',
        content: msg
      });
      return;
    }
    if (hideLoading) setTimeout(uni.hideLoading, 100);
    typeof param.success == 'function' && param.success(res.data);
  };
  //请求失败
  option.fail = err => {
    if (showLoading) setTimeout(uni.hideLoading, 100);
    uni.showToast({
      icon: 'none',
      title: '网络异常,请稍后重试'
    });
    typeof param.fail == 'function' && param.fail(err);
  };
  //请求完成
  option.complete = () => {
    typeof param.complete == 'function' && param.complete();
    return;
  };
  uni.request(option);
};
//同步请求
const asynRequest = (param, backpage) => {
  return new Promise((resolve, reject) => {
    //是否显示加载中(true:不显示，false:显示)
    const showLoading = param.showLoading || false;
    //是否隐藏加载中(true:隐藏，false:不隐藏)
    const hideLoading = param.hideLoading || false;
    const loadingTitle = param.loadingTitle || '';
    //用户交互:加载圈
    if (showLoading) {
      uni.showLoading({ title: loadingTitle, mask: true });
    }
    //是否为分页接口
    const isPagination = param.isPagination || false;
    uni.request({
      url:
        (param.baseurl ? param.baseurl : host.BASE_HOST) +
        param.api +
        (param.api.indexOf('?') === -1 ? '?source=mobile' : '&source=mobile'),
      header:
        param.method == 'GET'
          ? {
              'X-Requested-With': 'XMLHttpRequest',
              Accept: 'application/json',
              'Content-Type': 'application/json; charset=UTF-8'
            }
          : {
              'X-Requested-With': 'XMLHttpRequest',
              Accept: 'application/json',
              'Content-Type': param.contentType
                ? param.contentType
                : 'application/json; charset=UTF-8'
            },
      method: param.method || 'GET',
      //携带cookies
      //withCredentials: true,
      data: param.data || {},
      success: res => {
        let statusCode = null,
          msg = null;
        if (res.statusCode && res.statusCode != 200) {
          //api错误
          setTimeout(uni.hideLoading, 100);
          uni.showModal({
            title: '提示',
            showCancel: false,
            confirmColor: '#005BAC',
            content: '网络异常,请稍后重试'
          });
          return;
        }
        //判断是否为分页接口
        if (isPagination && res.data.statusCode === undefined) {
          statusCode = res.statusCode;
          msg = res.errMsg;
        } else {
          statusCode = res.data.statusCode;
          msg = res.data.message;
        }
        //返回结果码statusCode判断:200成功,21000登录失效
        if (statusCode == 21000) {
          store.dispatch('goToLogin');
        } else if (
          (res.data.success != undefined && !res.data.success) ||
          (statusCode != 200 && statusCode != 0)
        ) {
          setTimeout(uni.hideLoading, 100);
          uni.showModal({
            title: '提示',
            showCancel: false,
            confirmColor: '#005BAC',
            content: msg
          });
          return;
        }
        if (hideLoading) setTimeout(uni.hideLoading, 100);
        resolve(res.data);
      },
      fail: err => {
        setTimeout(uni.hideLoading, 100);
        uni.showToast({
          icon: 'none',
          title: '网络异常,请稍后重试'
        });
        reject(err);
      }
    });
  });
};

export default {
  syncRequest,
  asynRequest
};
