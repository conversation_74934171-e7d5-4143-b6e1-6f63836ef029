export default {
  sexClassFilter(val) {
    return val == 1 ? 'sex-woman' : 'sex-man';
  },
  firstNameFilter(val = '') {
    return val.substring(val.length - 2);
  },
  indexTimeFilter: function(time) {
    if (time) {
      let current = new Date(),
        cY = current.getFullYear(),
        cM = current.getMonth() + 1,
        cD = current.getDate(),
        currentStr = `${cY}年${cM}月${cD}日`,
        getDate = new Date(time.replace(/-/g, '/')),
        gY = getDate.getFullYear(),
        gM = getDate.getMonth() + 1,
        gD = getDate.getDate(),
        ghh = getDate.getHours(),
        gmm =
          getDate.getMinutes() < 10
            ? `0${getDate.getMinutes()}`
            : getDate.getMinutes(),
        getDateStr = `${gY}年${gM}月${gD}日`;
      if (currentStr === getDateStr) {
        return `今天 ${ghh}:${gmm}`;
      } else if (gY === cY) {
        return `${gM}月${gD}日 ${ghh}:${gmm}`;
      } else {
        return `${gY}年${gM}月${gD}日 ${ghh}:${gmm}`;
      }
    }
    return '';
  },
  fileSizeFilter: function(size) {
    let newSize = 0;
    if (size < 1048576) {
      newSize = (size / 1024).toFixed(2);
      return `${newSize} KB`;
    } else if (size >= 1073741824) {
      newSize = (size / 1073741824).toFixed(2);
      return `${newSize} GB`;
    } else {
      newSize = (size / 1048576).toFixed(2);
      return `${newSize} MB`;
    }
    return '';
  },
  formatTime: function(time) {
    return time ? time.substring(0, 16) : '';
  },
  nameFilter: function(name) {
    return name ? name.substring(name.length - 2) : '';
  },
  personFilter: function(personVal) {
    let personStr = '';
    if (personVal) {
      if (Array.isArray(personVal)) {
        let arr = [];
        personVal.forEach((item, index) => {
          if (index < 3) {
            arr.push(item['name']);
          }
        });
        personStr = arr.join('、');
        if (personVal.length > 3) {
          personStr = `${personStr}等${personVal.length}人`;
        }
      } else {
        personStr = personVal['name'];
      }
    }
    return personStr;
  }
};
