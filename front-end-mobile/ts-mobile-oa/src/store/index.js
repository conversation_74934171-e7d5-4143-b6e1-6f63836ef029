import Vue from 'vue';
import Vuex from 'vuex';
import common from '@/common/js/common.js';
Vue.use(Vuex);
const store = new Vuex.Store({
  state: {
    hasbind: false, //是否已绑定
    usercode: '', //账号
    empcode: '', //工号
    empsex: '',
    password: '',
    token: '',
    username: '',
    headimg: '',
    deptname: '',
    dutyname: '',
    empid: '', //员工id
    globalSetting: {
      passwordLength: 6,
      passwordPreset: '123456',
      passwordRule: '1,2,3',
      recordLinkUrl: '',
      recordNumber: '',
      originalContent: '',
      originalUrl: 'http://www.trasen.cn',
      mobilePlatform: '',
      salaryType: ''
    },
    systemCustomCode: {},
    personalSortData: {}
  },
  actions: {
    /**@desc 去登录**/
    goToLogin({ state }) {
      const getCurrentPagePath = location.href;
      let hrefPath = '',
        toastIcon = '',
        toastTitle = '';
      const isMustAccountLogin = state.globalSetting.accountLogin;
      if (isMustAccountLogin) {
        hrefPath = `./login?returnURL=${getCurrentPagePath}`;
        toastIcon = 'none';
        toastTitle = '登录失效，请重新登录';
      } else {
        hrefPath = `/ts-information/cp/weixin/wxOAuth2Url?url=${getCurrentPagePath}`;
        toastIcon = 'loading';
        toastTitle = '登录失效，正在重新登录';
      }
      uni.showToast({
        title: toastTitle,
        icon: toastIcon,
        duration: 2000,
        complete: function() {
          setTimeout(function() {
            location.href = hrefPath;
          });
        }
      });
    }
  },
  mutations: {
    changeState(state, param) {
      for (let key in param) {
        state[key] = param[key];
      }
    }
  }
});

export default store;
