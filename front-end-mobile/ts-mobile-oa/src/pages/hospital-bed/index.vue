<template>
  <view class="train-detail-container">
    <page-head title="全院一张床" @clickLeft="returnBack"> </page-head>
    <!-- <view class="title">“全院一张床”跨病区收治情况一览表</view> -->
    <view class="inpatient-department-business-data">
      <fixed-table-dept
        :data="tableList"
        :header="columns"
        :fixed="true"
        :fixedFirstAndSecond="true"
        :border="true"
        :stripe="true"
        :showActions="true"
      />
    </view>
  </view>
</template>

<script>
import FixedTableDept from './fixed-table-dept.vue';
export default {
  components: { FixedTableDept },
  data() {
    return {
      tableList: [],
      columns: [
        {
          prop: '病区',
          title: '病区',
          align: 'center',
          overflow: true
        },
        {
          prop: '科室名称',
          title: '科室名称',
          align: 'center',
          overflow: true
        },
        {
          prop: '设置床位',
          title: '设置床位',
          align: 'center',
          width: 160
        },
        {
          prop: '本科室病人总数',
          title: '本科室病人总数',
          align: 'center',
          width: 250
        },
        {
          prop: '预留床位数',
          title: '预留床位数',
          align: 'center',
          width: 180
        },
        {
          prop: '临时挂床位数',
          title: '临时挂床位数',
          align: 'center',
          width: 210
        },
        {
          prop: '剩余床位',
          title: '剩余床位',
          align: 'center',
          width: 160
        },
        {
          prop: '跨病区可使用床位数',
          title: '跨病区可使用床位数',
          align: 'center',
          width: 310
        },
        {
          prop: '接受跨科收治病人数',
          title: '接受跨科收治病人数',
          align: 'center',
          width: 310
        },
        {
          prop: '收治到其他护理单元数',
          title: '收治到其他护理单元数',
          align: 'center',
          width: 340
        }
      ]
    };
  },
  onLoad() {
    this.refresh();
  },
  methods: {
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        this.$parentTypeFun({
          type: 'jumpPage',
          path: '/workbench'
        });
      } else {
        uni.navigateBack();
      }
    },
    refresh() {
      this.getListData();
    },
    getListData() {
      this.ajax
        .getBedOverviewDate()
        .then(res => {
          let list = res.object;
          let rowLabel = '';
          let rowIndex = 0;
          list.forEach((item, index) => {
            item.rowSpan = 0;
            if (item['病区'] != rowLabel) {
              item.rowSpan = 1;
              rowIndex = index;
              rowLabel = item['病区'];
            } else {
              list[rowIndex].rowSpan++;
            }
          });
          this.tableList = list;
        })
        .catch(() => {});
    }
  }
};
</script>

<style lang="scss" scoped>
.train-detail-container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #fff;
  .title {
    line-height: 60rpx;
    text-align: center;
    font-size: 36rpx;
    font-weight: bold;
  }
  //住院科室业务数据
  .inpatient-department-business-data {
    width: 100%;
    border: 1px solid #e7e7e7;
    border-radius: 16rpx;
    padding: 16rpx;
    overflow: auto;
  }
  .view-scroll-container {
    margin-top: 20rpx;
    border-radius: 12rpx;
    height: calc(100vh - 152rpx);
    padding-bottom: 32rpx;

    .td-styles {
      overflow: hidden !important;
      white-space: nowrap !important;
      text-overflow: ellipsis !important;
      display: inline-block;
    }
  }
}
</style>
