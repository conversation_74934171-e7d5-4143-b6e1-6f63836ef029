<script>
import Base64 from '@/common/js/base64.min.js';

export default {
  props: {
    fields: {
      type: Array,
      default: () => []
    }
  },
  render(h) {
    let { fields } = this.$props;

    let groups = [];
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];

      if (
        (field.isHide == 0 && field.isAllowDeleted == 0) ||
        (field.isDisabled == 0 && field.isAllowDeleted == 1)
      ) {
        if (field.fieldType === 'file' && field.value instanceof Array) {
          groups.push(this.renderFileItem(field));
        } else {
          groups.push(this.renderValueItem(field));
        }
      } else {
        continue;
      }
    }

    return h('view', [groups]);
  },
  methods: {
    renderFileItem({ showName, value }) {
      let _self = this;
      let { $createElement: h } = _self;

      let fileItem = value.map(({ id, originalName }) => {
        return h(
          'view',
          {
            class: 'file-item',
            on: {
              click: () => {
                _self.handleFileClcik(id, originalName);
              }
            }
          },
          originalName
        );
      });

      return h(
        'view',
        {
          class: 'file-form-container'
        },
        [
          h('view', [showName]),
          h(
            'view',
            {
              class: 'file-container'
            },
            [...fileItem]
          )
        ]
      );
    },
    renderValueItem({ showName, config = {}, value, fieldType, optionValue }) {
      let { $createElement: h } = this;

      switch (fieldType) {
        case 'checkbox':
        case 'radio':
          let values = (value || '').split(',');
          let options = (optionValue || '').split('|');

          let dir = {};
          if (options && options.length > 0) {
            options.forEach(item => {
              let [k, v] = item.split(':');
              dir[k] = v;
            });
          }

          value = values.length && values.map(k => dir[k]).join(',');
          break;
      }

      return h(
        'view',
        {
          class: 'fields-item'
        },
        [h('view', showName), h('view', { ...config }, value)]
      );
    },
    // 预览
    handleFileClcik(id, fileName) {
      let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .file-form-container {
    padding: 4px 0;
    display: flex;
    border-bottom: 1px solid #eee;
    .file-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;

      .file-item {
        font-size: 14px;
        color: #005bac;
        z-index: 99999;
        text-align: right;
      }
    }
  }
  .fields-item {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
    padding: 4px 0;
    font-size: 14px;
  }
}
</style>
