<template>
  <view class="person-list-item-info" @click="toggle(person)">
    <view class="left">
      <img
        class="person-head-image"
        v-if="person.avatar"
        :src="person.avatar"
      />
      <view
        v-else
        class="person-head-image"
        :class="person.gender | sexClassFilter"
      >
        {{ person.empName | firstNameFilter }}
      </view>
    </view>
    <view class="right">
      <view class="person-name">{{ person.empName }}</view>
      <view class="person-description">
        {{ person.orgName }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    person: {
      type: Object,
      default: () => ({})
    },
    clickItemFunc: {
      type: Function,
      default: () => {}
    }
  },
  filters: {
    sexClassFilter(val) {
      return val === '0' ? 'sex-man' : 'sex-woman';
    },
    firstNameFilter(val) {
      return val.substring(val.length - 2);
    }
  },
  methods: {
    toggle(e) {
      this.clickItemFunc(e);
    }
  }
};
</script>

<style lang="scss" scoped>
.person-list-item-info {
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
  .left {
    .person-head-image {
      margin-right: 16rpx;
      width: $uni-img-size-lg;
      height: $uni-img-size-lg;
      border-radius: 50%;
      background-color: $u-bg-color;
      text-align: center;
      line-height: $uni-img-size-lg;
      font-size: $uni-font-size-base;
      color: $uni-text-color-inverse;
      &.sex-man {
        background-color: $sexman-color;
      }
      &.sex-woman {
        background-color: $sexwoman-color;
      }
    }
  }
  .right {
    flex: 1;
    .person-name {
      font-size: $uni-font-size-base;
      color: $u-main-color;
    }
    .person-description {
      color: $u-content-color;
      font-size: $uni-font-size-sm;
    }
  }
}
</style>
