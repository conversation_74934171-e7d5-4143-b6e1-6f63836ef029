<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="人才画像"></page-head>
    <view class="search-container">
      <view>
        科室查询：
        <i @click="handleShowPopup" class="oa-icon oa-icon-shaixuan"></i>
      </view>
      <uni-search-bar
        radius="100"
        placeholder="请输入姓名、科室"
        borderColor="transparent"
        bgColor="#F4F4F4"
        cancelButton="none"
        @confirm="search"
      />
    </view>

    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        placeholder="请输入姓名、科室"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view class="person-list">
          <view class="person-item" v-for="row in dataList" :key="row.id">
            <image
              v-if="row.avatar"
              :src="row.avatar ? $config.BASE_HOST + row.avatar : ''"
              mode="aspectFit"
              class="avata_img"
              @click="handleReviewAvatar(row)"
            />
            <view
              v-else
              class="person-head-image"
              :class="row.empSex | sexClassFilter"
              @tap="chooseItem(row)"
            >
              {{ row.empName | firstNameFilter }}
            </view>
            <view class="item-info-container" @tap="chooseItem(row)">
              <view class="person-item-row">
                <view class="person-item-title">
                  <text class="emp-name title">{{ row.empName }}</text>
                  <text class="person-item-status">
                    {{ row.employeeStatusName }}
                  </text>
                </view>
              </view>
              <view class="person-item-arow">
                <text class="person-item-node post">
                  <text class="label">岗位：</text>
                  <text class="value ">
                    {{ row.personalIdentityName || '' }}
                  </text>
                </text>
                <text class="person-item-node">
                  <text class="label">科室：</text>
                  <text class="value">{{ row.empDeptName }}</text>
                </text>
              </view>
            </view>
          </view>
        </view>
      </mescroll>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import { ImagePreview } from 'vant';

export default {
  components: {
    mescroll
  },
  data() {
    return {
      dataList: [], //列表数据
      type: '',
      keywords: ''
    };
  },
  onLoad() {},
  filters: {
    sexClassFilter(val) {
      return val === '0' ? 'sex-man' : 'sex-woman';
    },
    firstNameFilter(val) {
      return val.substring(val.length - 2);
    }
  },
  methods: {
    handleReviewAvatar(row) {
      if (!row.avatar) return;
      ImagePreview({
        images: [row.avatar],
        closeable: true
      });
    },
    search(e) {
      this.keywords = e ? e.value : '';
      this.datasInit();
      this.$nextTick(() => {
        this.$refs['mescroll'].downCallback();
      });
    },
    async getListData(page, successCallback, errorCallback, keywords) {
      let _self = this;
      await _self.ajax
        .getInnerContractList({
          pageSize: page.size,
          pageNo: page.num,
          employeeName: this.keywords
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(row) {
      let _self = this;
      _self.dataList = _self.dataList.concat(row);
    },
    datasInit() {
      let _self = this;
      _self.dataList = [];
    },
    handleShowPopup() {
      let _self = this,
        pagePath = '/pages/talent-portrait/dept-person-view';
      _self.$nextTick(() => {
        uni.navigateTo({
          url: `${pagePath}`
        });
      });
    },
    chooseItem(row) {
      let _self = this,
        pagePath = '/pages/talent-portrait/talent-details';
      _self.$nextTick(() => {
        uni.navigateTo({
          url: `${pagePath}?employeeId=${row.employeeId}`
        });
      });
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, SimSun, sans-serif;
  .search-container {
    padding-left: 8px;
    overflow: hidden;
    margin: 8px 0;
    background-color: #fff;
    display: flex;
    align-items: center;
    .uni-searchbar {
      flex: 1;
    }
    .oa-icon-shaixuan {
      padding-left: 0px;
      padding-right: 8px;
      font-size: 18px;
    }
  }
  .mescroll-content {
    position: absolute;
    width: 100%;
    top: 108px;
    bottom: 0;

    .person-item {
      padding: 22rpx 30rpx;
      background-color: #ffffff;
      position: relative;
      display: flex;
      align-items: center;
      .person-head-image {
        margin-right: 16rpx;
        width: $uni-img-size-lg;
        height: $uni-img-size-lg;
        border-radius: 50%;
        background-color: $u-bg-color;
        text-align: center;
        line-height: $uni-img-size-lg;
        font-size: $uni-font-size-base;
        color: $uni-text-color-inverse;
        &.sex-man {
          background-color: $sexman-color;
        }
        &.sex-woman {
          background-color: $sexwoman-color;
        }
      }

      .avata_img {
        width: $uni-img-size-lg;
        height: $uni-img-size-lg;
        background-size: cover;
        border-radius: 50%;
        margin-right: 8px;
      }

      .item-info-container {
        flex: 1;
      }

      .person-item-row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }
      .person-item-title {
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
        overflow: hidden;
        display: flex;
        flex: 1;
        align-items: center;
        .emp-name {
          // width: 105px;
          margin-right: 8px;
        }
      }
      .person-item-node {
        font-size: 28rpx;
        color: #666;
        margin-right: 8px;
        display: inline-block;

        &.post {
          // width: 100px !important;
          margin-right: 16px;
        }

        .label {
          color: #333;
        }
        .value {
          color: rgba(0, 0, 0, 0.65);
          font-weight: 600;
        }
      }
      .person-item-status {
        // transform: scale(0.83);
        font-size: 24rpx;
        padding: 4rpx 8rpx;
        border-radius: 8rpx;
        border: 1px solid $theme-color;
        color: $theme-color;
        font-weight: normal;
        line-height: 1;
      }
    }
  }
}
</style>
