<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="人才画像"></page-head>
    <uni-search-bar
      radius="100"
      placeholder="请输入姓名、科室"
      borderColor="transparent"
      bgColor="#F4F4F4"
      cancelButton="none"
      ref="search"
      class="search-bar"
      @input="search"
    />
    <view class="title-box">
      <ul class="contacts-crumbs">
        <li v-for="(item, index) in titleArr" :key="index">
          <text class="icon" v-if="index !== 0"> > </text>
          <text
            :class="{ active: index < titleArr.length - 1 }"
            @click="jumpItemHandle(item, index)"
          >
            {{ item.orgName }}
          </text>
        </li>
      </ul>
    </view>
    <view class="contacts-content">
      <view v-if="viewTreeChildPersonnl">
        <organization-item
          v-for="item in viewTreeChildPersonnl.orgList"
          :key="item.orgId"
          :data="item"
          :clickLowerLevelFunc="organizationLowerLevelClick"
        />
        <participants-item
          v-for="item in viewTreeChildPersonnl.empList"
          :key="item.empId"
          :person="item"
          :clickItemFunc="participantsItemClick"
        />
      </view>
    </view>
  </view>
</template>

<script>
import common from '@/common/js/common.js';
import ParticipantsItem from './components/participants-item.vue';
import OrganizationItem from './components/organization-item.vue';
export default {
  components: {
    ParticipantsItem,
    OrganizationItem
  },
  data() {
    return {
      titleArr: [], // 面包屑
      viewTreeChildPersonnl: {}, // 显示tree层级 的机构人员列表
      localTreeChildPersonnl: {} // 本地存储一份
    };
  },
  async onLoad() {
    // 默认获取最高级 组织机构数据
    this.getTreeLevelDataHandle('0', res => {
      this.titleArr.push(res);
    });
  },
  methods: {
    search({ value = '' }) {
      if (value == '') {
        this.viewTreeChildPersonnl.empList = this.localTreeChildPersonnl.empList;
        this.viewTreeChildPersonnl.orgList = this.localTreeChildPersonnl.orgList;
        return;
      }

      this.viewTreeChildPersonnl.empList = this.viewTreeChildPersonnl.empList.filter(
        f => f.empName.indexOf(value) !== -1
      );
      this.viewTreeChildPersonnl.orgList = this.viewTreeChildPersonnl.orgList.filter(
        f => f.orgName.indexOf(value) !== -1
      );
    },
    // 机构下级按钮点击事件
    organizationLowerLevelClick(e, val) {
      if (val) {
        return false;
      }
      this.$refs.search.searchVal = '';
      this.getTreeLevelDataHandle(e.orgId, res => {
        this.titleArr.push(res);
      });
    },
    // 机构面包屑跳转
    jumpItemHandle(item, index) {
      if (index + 1 === this.titleArr.length) {
        return;
      }
      this.titleArr.splice(index + 1);
      this.$refs.search.searchVal = '';
      this.getTreeLevelDataHandle(item.orgId);
    },
    // 获取tree层级 的机构人员列表
    async getTreeLevelDataHandle(parentId, func) {
      const { object } = await this.ajax.getOrgEmp({
        name: '',
        parentId
      });

      this.viewTreeChildPersonnl = common.deepClone(object);
      this.localTreeChildPersonnl = common.deepClone(object);
      func && func(object);
    },
    // 人员item点击事件
    participantsItemClick(e) {
      const { empId, empName } = e;
      let _self = this,
        pagePath = '/pages/talent-portrait/talent-details';
      _self.$nextTick(() => {
        uni.navigateTo({
          url: `${pagePath}?employeeId=${e.empId}`
        });
      });
    },
    returnBack() {
      uni.redirectTo({
        url: '/pages/talent-portrait/talent-portrait'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  ::v-deep {
    .uni-navbar__content {
      box-shadow: none !important;
      border-bottom: 1px solid #eee !important;
    }
  }
  .search-bar {
    position: fixed;
    top: 44px;
    left: 0;
    width: 100%;
  }
  .title-box {
    margin-top: 52px;
    width: 100%;
    overflow: scroll;
    .contacts-crumbs {
      padding: 8rpx 0;
      display: flex;
      align-items: center;
      box-shadow: 0px 2rpx 0px 0px #f4f4f4;
      color: #666;
      overflow-x: scroll;
      li {
        white-space: nowrap;
      }
      .icon {
        padding: 0 16rpx;
      }
      .active {
        color: #005bac;
      }
    }
  }
}
</style>
