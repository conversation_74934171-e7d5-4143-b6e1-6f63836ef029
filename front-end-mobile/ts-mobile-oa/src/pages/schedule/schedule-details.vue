<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="日程详情"></page-head>
    <view class="content_wrap" v-if="showContent">
      <view class="content">
        <view class="row_type" v-if="scheduleType === '会议日程'"
          >会议日程</view
        >
        <view class="row row_subject">
          <text class="mark"></text>
          <view class="subject">{{ scheduleSubject }} </view>
        </view>
        <view class="row_content">
          {{ scheduleContent }}
        </view>
        <view class="row">
          <text class="row_lable oa-icon oa-icon-shijian"></text>
          <text class="row_value">{{ scheduleDateStr }}</text>
        </view>
        <view class="row">
          <text class="row_lable oa-icon oa-icon-cuiban"></text>
          <text class="row_value">{{ remindType }}</text>
        </view>
        <view class="row" v-if="scheduleAddress">
          <text class="row_lable oa-icon oa-icon-didian"></text>
          <text class="row_value">{{ scheduleAddress }}</text>
        </view>
      </view>
      <view class="bottom_btn">
        <button class="btn_item" @tap="delet">删除</button>
        <button class="theme-color btn_item" @tap="edit">编辑</button>
      </view>
    </view>
  </view>
</template>

<script>
import { mapMutations } from 'vuex';
import loginModule from '@/common/js/loginModule.js';
export default {
  data() {
    return {
      fromPage: '',
      id: '',
      showContent: false,
      scheduleType: '工作日程',
      scheduleSubject: '',
      scheduleDateStr: '',
      remindType: '不提醒',
      remindTime: 0,
      scheduleContent: '',
      scheduleAddress: ''
    };
  },
  async onLoad(opt) {
    if (opt && opt.token && !this.$config.ENABLE_ACCOUNT_LOGIN) {
      let userInfo = await loginModule.loginInfo(opt);
      if (JSON.stringify(userInfo) != '{}') this.changeState(userInfo);
    }
    this.fromPage = opt.fromPage ? opt.fromPage : 'workBench';
    this.id = opt.id;
    if (opt.id != null) {
      this.id = opt.id;
      this.getDatas(opt.id);
      this.updateScheduleStatus(opt.id);
    }
  },
  methods: {
    ...mapMutations(['changeState']),
    //获取详情
    getDatas(id) {
      let _self = this;
      _self.ajax
        .getScheduleDetails({
          id: id
        })
        .then(res => {
          let data = res.object;
          _self.scheduleType = data.scheduleType;
          _self.scheduleSubject = data.scheduleSubject;
          let date = _self.$common.getDate('dateStr', data.scheduleDate);
          _self.scheduleDateStr = `${date.timeStr} (${date.week})  ${data.scheduleStartTime}-${data.scheduleEndTime}`;
          _self.remindType =
            data.isRemind && data.isRemind === 1
              ? `提前${_self.getremindStr(Number(data.remindTime))}`
              : '不提醒';
          _self.scheduleContent = data.scheduleContent;
          _self.scheduleAddress = data.scheduleAddress;
          _self.showContent = true;
        });
    },
    getremindStr(value) {
      let str = '';
      switch (value) {
        case 15:
          str = '15分钟';
          break;
        case 30:
          str = '30分钟';
          break;
        case 60:
          str = '1小时';
          break;
        case 120:
          str = '2分钟';
          break;
        case 1440:
          str = '一天';
          break;
      }
      return str;
    },
    delet() {
      let _self = this;
      uni.showModal({
        title: '提示',
        content: '您确定删除该日程？',
        confirmText: '取消',
        cancelText: '确定',
        confirmColor: '#005BAC',
        success: function(res) {
          if (res.cancel) {
            _self.ajax
              .deleteSchedule({
                id: _self.id
              })
              .then(res => {
                uni.showToast({ title: '操作成功!', icon: 'none' });
                uni.redirectTo({
                  url: `/pages/schedule/schedule-calendar?fromPage=${_self.fromPage}`
                });
              });
          }
        }
      });
    },
    edit() {
      let _self = this;
      uni.navigateTo({
        url: `/pages/schedule/add-schedule?id=${_self.id}&fromPage=${_self.fromPage}`
      });
    },
    updateScheduleStatus(id) {
      let _self = this;
      _self.ajax.insertOrUpdateSchedule({ id: id, isRemind: '2' });
    },
    returnBack() {
      let _self = this;
      uni.redirectTo({
        url: `/pages/schedule/schedule-calendar?fromPage=${_self.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .theme-color {
    color: $theme-color !important;
  }
  .content_wrap {
    flex: 1;
    overflow-y: auto;
  }
  .content {
    flex: 1;
    padding-top: 22rpx;
    background-color: #fff;
    margin-bottom: 100rpx;
    .row {
      width: 100%;
      background-color: #ffffff;
      position: relative;
      display: flex;
      flex-direction: row;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 30rpx;
        right: 0;
        transform: scaleY(-0.5);
        height: 1px;
        background-color: #eee;
      }
      &:last-child::after {
        height: 0;
      }
      .row_lable {
        width: 100rpx;
        color: #333;
        padding: 22rpx 30rpx;
        box-sizing: border-box;
        text-align: center;
      }
      .row_lable ~ .row_value {
        flex: 1;
        font-size: 32rpx;
        color: #333;
        padding: 22rpx 30rpx;
        padding-left: 0;
        box-sizing: border-box;
        text-align: left;
      }
    }
    .row_subject {
      display: flex;
      flex-direction: row;
      align-items: center;
      &::after {
        height: 0;
      }
      .mark {
        position: absolute;
        content: '';
        top: 28rpx;
        left: 50rpx;
        transform: translate(-50%, -50%);
        width: 20rpx;
        height: 20rpx;
        border-radius: 100%;
        background-color: #3aad73;
      }
      .subject {
        padding-left: 100rpx !important;
        font-size: 36rpx !important;
        font-weight: bold;
        padding-top: 0 !important;
      }
    }
    .row_content {
      width: 100%;
      background-color: #ffffff;
      padding: 0 30rpx 22rpx 100rpx;
      box-sizing: border-box;
      font-size: 32rpx;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 30rpx;
        right: 0;
        transform: scaleY(-0.5);
        height: 1px;
        background-color: #eee;
      }
    }
    .row_type {
      width: 100%;
      background-color: #ffffff;
      padding: 0 30rpx 0 100rpx;
      box-sizing: border-box;
      font-size: 28rpx;
      color: #3aad73;
    }
  }
  .bottom_btn {
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    background-color: #ffffff;
    box-shadow: 0 1px 6px #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    .btn_item {
      height: 90rpx;
      flex: 1;
      box-sizing: border-box;
      text-align: center;
      font-size: 32rpx;
      position: relative;
      background-color: transparent;
      color: #666;
      &::after {
        border: 0;
        top: 20rpx;
        bottom: 20rpx;
        right: 0;
        left: unset;
        transform: scaleX(-0.5);
        width: 1px;
        height: unset;
        background-color: #ccc;
      }
      &:last-child::after {
        width: 0;
      }
    }
  }
}
</style>
