<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="个人日程" />
    <view class="content_wrap" v-if="showContent">
      <view class="calendar_wrap">
        <lxCalendar @change="calendarChange" :dot_lists="dotLists" />
      </view>
      <view class="content">
        <view class="content_tab" v-if="scheduleList.length != 0">
          <view class="schedule_list">
            <view
              class="schedule_item"
              v-for="item in scheduleList"
              :key="item.id"
              @click="checkDetail(item.id)"
            >
              <view class="item_content">
                <text class="item_content_time">
                  {{
                    item.scheduleStartTime == '00:00' &&
                    item.scheduleEndTime == '23:59'
                      ? '全天'
                      : item.scheduleStartTime + ' - ' + item.scheduleEndTime
                  }}
                </text>
                <text
                  class="item_content_type"
                  v-if="item.scheduleType == '会议日程'"
                >
                  会议日程
                </text>
              </view>
              <view class="item_content_subject">{{
                item.scheduleSubject
              }}</view>
              <view class="item_content_info">{{ item.scheduleContent }}</view>
            </view>
          </view>
          <view
            class="addBtn"
            @tap="newAddSchedule"
            v-if="selectedDate >= this.currentDate"
          >
            <text class="oa-icon oa-icon-tianjia1"></text>
          </view>
        </view>
        <view v-else class="nothing">
          <view class="img_content">
            <image
              class="nothing_img"
              src="../../static/img/nothing.png"
              mode="aspectFit"
            >
            </image>
          </view>
          <view class="tips_text" v-if="selectedDate >= this.currentDate">
            <text>新的一天从日程管理开始</text>
          </view>
          <button
            v-if="selectedDate >= this.currentDate"
            class="addBtn"
            size="mini"
            type="primary"
            @tap="newAddSchedule"
          >
            添加日程
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import lxCalendar from '@/components/lx-calendar/lx-calendar.vue';
export default {
  components: {
    lxCalendar
  },
  data() {
    return {
      showContent: false,
      fromPage: '',
      currentDate: '',
      selectedDate: '',
      scheduleList: [],
      dotLists: []
    };
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    let current = this.$common.getDate('date');
    this.currentDate = this.selectedDate = current.timeStr;
    this.getSelectedDate(current.timeStr.substring(0, 7));
    this.getScheduleList(current.timeStr);
  },
  methods: {
    //获取所选月份中有日程的日期
    getSelectedDate(date) {
      let _self = this;
      _self.ajax
        .getSelectedDateSchedule({
          scheduleDate: date,
          date: date
        })
        .then(res => {
          _self.dotLists = res.object;
          _self.showContent = true;
        });
    },
    //获取日程列表
    getScheduleList(date) {
      let _self = this;
      _self.ajax
        .getScheduleDetailsList({
          scheduleDate: date
        })
        .then(res => {
          _self.scheduleList = res.object;
        });
    },
    //查看详情
    checkDetail(id) {
      uni.navigateTo({
        url: `/pages/schedule/schedule-details?fromPage=${this.fromPage}&id=${id}`
      });
    },
    //日历切换事件
    calendarChange(e) {
      this.scheduleList = [];
      this.selectedDate = e.fulldate;
      this.getSelectedDate(e.fulldate.substring(0, 7));
      this.getScheduleList(e.fulldate);
    },
    //新建按钮点击事件
    newAddSchedule() {
      uni.navigateTo({
        url: `/pages/schedule/add-schedule?fromPage=${this.fromPage}&dateStr=${this.selectedDate}`
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/${this.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .content_wrap {
    display: flex;
    flex-direction: column;
    flex: 1;
    .calendar_wrap {
      z-index: 9;
    }
    .content {
      flex: 1;
      overflow: hidden;
      width: 100%;
      position: relative;
      .content_tab {
        height: 100%;
        position: relative;
        .schedule_list {
          height: 100%;
          overflow-y: auto;
          .schedule_item {
            padding: 22rpx 30rpx;
            width: 100%;
            background-color: #ffffff;
            box-sizing: border-box;
            position: relative;
            &::after {
              content: '';
              position: absolute;
              left: 30rpx;
              right: 0;
              height: 1px;
              transform: scaleY(0.5);
              background-color: #eee;
              bottom: 0;
            }
            &:last-child::after {
              height: 0;
            }
            .item_content {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              .item_content_time {
                flex: 1;
                font-size: 32rpx;
                font-weight: bold;
                color: #333333;
              }
              .item_content_type {
                color: #3aad73;
                font-size: 28rpx;
                margin-right: 10rpx;
              }
            }
            .item_content_subject {
              color: #333;
              font-weight: bold;
            }
            .item_content_info {
              color: #333;
              font-size: 32rpx;
              flex: 1;
            }
          }
        }
        .addBtn {
          position: absolute;
          right: 40rpx;
          bottom: 40rpx;
          z-index: 9;
          .oa-icon {
            color: #005bac;
            font-size: 40px;
            border-radius: 100%;
            box-shadow: 0 5px 5px #ccc;
          }
        }
      }
      .nothing {
        position: absolute;
        top: 40%;
        left: 0;
        right: 0;
        transform: translateY(-50%);
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .img_content {
          width: 300rpx;
          height: 300rpx;
          .nothing_img {
            width: 100%;
            height: 100%;
          }
        }
        .tips_text {
          color: #666666;
        }
        .addBtn {
          padding: 10rpx 20rpx;
          margin-top: 30rpx;
          width: 200rpx;
        }
      }
    }
  }
}
</style>
