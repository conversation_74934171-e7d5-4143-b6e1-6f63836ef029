<template>
  <view class="ts-content">
    <page-head title="" @clickLeft="returnBack"></page-head>
    <view class="web-view" id="web-view">
      <web-view v-if="url" ref="webView" :src="url"></web-view>
    </view>
  </view>
</template>

<script>
import Base64 from '@/common/js/base64.min.js';
var wv;
export default {
  data() {
    return {
      url: '',
      path: '',
      activeIndex: -1,
      fileList: [],
      fromPage: null,
      index: null
    };
  },
  onLoad(e) {
    // #ifdef APP-PLUS
    let currentWebview = this.$mp.page.$getAppWebview();
    setTimeout(function() {
      wv = currentWebview.children()[0];
      wv.setStyle({ scalable: true });
    }, 500);
    // #endif
    // #ifdef H5
    this.setViewport(
      'width=device-width, user-scalable=yes, initial-scale=1.0'
    );
    // #endif
    if (e.path) this.path = e.path;
    if (e.fromPage) this.fromPage = e.fromPage;
    if (e.index) this.index = e.index;
    // 获取传递过来的链接
    this.url = `${e.url}&_copyable=false&_printable=false`;

    this.fileList = uni.getStorageSync('fileList');
    if (!this.fileList || !this.fileList.length) {
      return;
    }
    let url = decodeURIComponent(this.url),
      fileUrl = Base64.decode(url.split('url=')[1]);
    fileUrl = fileUrl.split('?')[0];
    let activeId = fileUrl.split('/').pop();
    this.activeIndex = this.fileList.findIndex(item => item.fileId == activeId);
    this.$nextTick(this.handleWebViewLoaded);
  },
  onUnload() {
    uni.removeStorageSync('fileList');
    this.setViewport(
      'width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0'
    );
  },
  methods: {
    setViewport(content) {
      let metaList = document.querySelectorAll('meta[name=viewport]');
      if (metaList.length == 0) return;
      metaList.forEach(item => {
        item.setAttribute('content', content);
      });
    },
    returnBack() {
      if (this.path) {
        uni.redirectTo({
          url: `${this.path}${
            this.fromPage || this.index
              ? '?fromPage=' + this.fromPage + '&index=' + this.index
              : ''
          }`
        });
      } else {
        uni.navigateBack();
      }
    },
    async handleWebViewLoaded() {
      if (
        !this.fileList ||
        !this.fileList.length ||
        this.fileList.length == 1
      ) {
        return;
      }
      let iframe = document.querySelectorAll('iframe');
      iframe = [...iframe].find(dom =>
        dom.src.includes('/ts-preview/onlinePreview?url=')
      );
      let iframeDocument = iframe.contentWindow,
        touchProp = {};
      iframeDocument.addEventListener('touchstart', e => {
        touchProp = {};
        let touch = e.changedTouches[0];
        touchProp.startX = touch.pageX;
      });
      iframeDocument.addEventListener('touchmove', e => {
        if (touchProp.isTouched) {
          return;
        }
        touchProp.isTouched = true;
      });
      iframeDocument.addEventListener('touchend', async e => {
        let touch = e.changedTouches[0],
          pageX = touch.pageX,
          activeItem = {},
          activeIndex = this.activeIndex;
        if (pageX < touchProp.startX && pageX < 20) {
          activeIndex =
            (this.activeIndex == 0 ? this.fileList.length : this.activeIndex) -
            1;
        } else if (
          pageX > touchProp.startX &&
          pageX > iframe.offsetWidth - 20
        ) {
          activeIndex =
            (this.activeIndex == this.fileList.length - 1
              ? -1
              : this.activeIndex) + 1;
        } else {
          return;
        }

        activeItem = this.fileList[activeIndex];
        let { fileId: id, fileRealName: fileName } = activeItem || {};
        if (activeIndex == this.activeIndex || !id || !fileName) {
          return;
        }
        this.url = null;
        await this.$nextTick();
        this.url =
          `${this.$config.BASE_HOST}/ts-preview/onlinePreview?url=` +
          Base64.encode(
            `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`
          ) +
          '&_copyable=false&_printable=false';
        await this.$nextTick();
        this.activeIndex = activeIndex;
        this.handleWebViewLoaded();
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.ts-content {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .web-view {
    flex: 1;
    position: relative;
  }
}
</style>
