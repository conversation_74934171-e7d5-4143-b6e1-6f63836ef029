<template>
  <view class="bg">
    <view class="steps">
      <view class="steps_item" v-for="(i, index) in infoList" :key="index">
        <view class="s_r">
          <view
            class="index"
            :style="{ backgroundColor: backgroundColor, color: color }"
          ></view>
          <view class="line"></view>
        </view>
        <view class="s_l">
          <view class="info_item">
            <view class="top_info">
              <view class="date">{{ i.updateUserName }}</view>
              <view class="title">{{ i.updateDate }}</view>
            </view>
            <view class="info">
              <view class="card_item">进展：{{ i.logRemark }}</view>
              <view v-if="i.logFile" class="files">
                <view class="label">附件：</view>
                <view class="fiel_list" v-if="i.fileList.length > 0">
                  <view
                    class="file_item"
                    v-for="(i, index) in i.fileList"
                    :key="index"
                  >
                    <text @tap.stop="handleClick(i)">{{ i.originalName }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view v-if="infoList.length == 0" class="noData">暂无反馈记录</view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    infoList: {
      type: Array,
      default: () => []
    },
    color: {
      type: String,
      default: () => '#fff'
    },
    backgroundColor: {
      type: String,
      default: () => '#0079fe'
    },
    lineNum: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {};
  },
  watch: {
    infoList: {
      handler: function() {
        this.init();
      },
      immediate: true
    }
  },
  methods: {
    async init() {
      this.infoList.forEach(async i => {
        if (i.logFile != null && i.logFile != '') {
          i.fileList = await this.getFiles(i.logFile);
        }
      });
    },
    async getFiles(fileIds) {
      let list = [],
        _self = this;
      await _self.ajax.getFileAttachmentByBusinessIdOrId(fileIds).then(res => {
        list = res.object;
      });
      return list;
    },
    handleClick(i) {
      this.$emit('handleClick', i);
    }
  }
};
</script>
<style lang="scss" scoped>
.steps {
  display: flex;
  flex-direction: column;
  .noData {
    font-size: 28rpx;
    color: #999;
    text-align: center;
    height: 60rpx;
  }
  .files {
    display: flex;
    .label {
      font-size: 28rpx;
    }
    .fiel_list {
      width: calc(100% - 90rpx);
    }
    .file_item {
      font-size: 28rpx;
      color: #0079fe;
    }
  }
  .steps_item {
    display: flex;
    .s_r {
      padding: 0 20rpx;
      padding-top: 10rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      .line {
        flex: 1;
        width: 5rpx;
        border-left: 4rpx dashed #fff;
        background-color: #0079fe;
      }
      .index {
        width: 24rpx;
        height: 24rpx;
        border-radius: 50rpx;
        border: 4rpx solid #e3eeff;
        box-sizing: border-box;
      }
    }
    .s_l {
      display: flex;
      flex-direction: column;
      flex: 1;
      .info_item {
        background-color: #ffffff;
        margin-right: 30rpx;
        border-radius: 10rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .top_info {
          display: flex;
          .title {
            font-weight: 600;
            font-size: 28rpx;
            color: rgba(102, 102, 102, 1);
            overflow: hidden;
            text-overflow: ellipsis;
            margin-left: 10rpx;
          }
          .date {
            font-weight: 600;
            font-size: 28rpx;
            color: rgba(102, 102, 102, 1);
          }
        }
        .info {
          padding-bottom: 10rpx;
          .card_item {
            font-size: 28rpx;
            color: #5c5c5c;
          }
        }
      }
      .info_item:active {
        background-color: #f4f4f4;
      }
    }
  }
}
.ml5 {
  margin-left: 10rpx;
}
.mt10 {
  margin-top: 20rpx;
}
</style>
