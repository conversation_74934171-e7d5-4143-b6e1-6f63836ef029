<template>
  <uni-drawer :visible="showRight" mode="right" @close="closeDrawer()">
    <view class="option-wrap">
      <view class="option-tap">
        <view class="option-title">类型</view>
        <view class="option-list">
          <text
            class="option-item"
            v-for="item in registerTypeList"
            :class="searchForm.registerType == item.id ? 'selected' : ''"
            :data-id="item.id"
            @tap="chooseType"
            :key="item.id"
            >{{ item.typeName }}</text
          >
        </view>
      </view>
      <view class="option-tap">
        <view class="option-title">紧急程度</view>
        <view class="option-list">
          <text
            class="option-item"
            :class="searchForm.registerUrgency == item.value ? 'selected' : ''"
            :data-id="item.value"
            v-for="(item, index) in urgencyLevelList"
            :key="index"
            @tap="chooseRegisterUrgency"
            >{{ item.label }}</text
          >
        </view>
      </view>
      <view class="option-tap">
        <view class="option-title">是否超期</view>
        <view class="option-list">
          <text
            class="option-item"
            :class="searchForm.isOverdue == item.value ? 'selected' : ''"
            :data-id="item.value"
            v-for="item in isOverdueList"
            :key="item.value"
            @tap="chooseisOverdue"
            >{{ item.label }}</text
          >
        </view>
      </view>
      <view class="option-tap">
        <view class="option-title">是否催办</view>
        <view class="option-list">
          <text
            class="option-item"
            :class="searchForm.isUrge == item.value ? 'selected' : ''"
            :data-id="item.value"
            v-for="item in isUrgeList"
            :key="item.value"
            @tap="chooseisUrge"
            >{{ item.label }}</text
          >
        </view>
      </view>
      <view class="option-tap">
        <view class="option-title">计划完成时间</view>
        <view class="option-list">
          <text class="option-item time-item" @tap="showPicker()">{{
            searchForm.startDate
          }}</text>
          <text class="option-item divider">-</text>
          <text class="option-item time-item" @tap="showPicker()">{{
            searchForm.endDate
          }}</text>
        </view>
      </view>
    </view>
    <view class="btn-tap">
      <view class="btn-item cancle-btn" @tap="reset">重置</view>
      <view class="btn-item them-btn" @tap="submit">确定</view>
    </view>
  </uni-drawer>
</template>

<script>
import uniDrawer from '@/components/uni-drawer/uni-drawer.vue';
export default {
  components: { uniDrawer },
  props: {
    registerTypeList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showRight: false,
      searchForm: {},
      urgencyLevelList: [
        {
          label: '普通',
          value: 1
        },
        {
          label: '紧急',
          value: 2
        },
        {
          label: '非常紧急',
          value: 3
        }
      ],
      isOverdueList: [
        {
          label: '未超期',
          value: 0
        },
        {
          label: '即将超期',
          value: 1
        },
        {
          label: '已超期',
          value: 2
        }
      ],
      isUrgeList: [
        {
          label: '否',
          value: 0
        },
        {
          label: '是',
          value: 1
        }
      ]
    };
  },
  methods: {
    show() {
      this.showRight = true;
    },
    chooseType(e) {
      let data = e.currentTarget.dataset;
      this.searchForm.registerType = data.id;
      this.$forceUpdate();
    },
    showPicker() {
      this.$emit('showPicker');
    },
    chooseRegisterUrgency(e) {
      let data = e.currentTarget.dataset;
      this.searchForm.registerUrgency = data.id;
      this.$forceUpdate();
    },
    chooseisOverdue(e) {
      let data = e.currentTarget.dataset;
      this.searchForm.isOverdue = data.id;
      this.$forceUpdate();
    },
    chooseisUrge(e) {
      let data = e.currentTarget.dataset;
      this.searchForm.isUrge = data.id;
      this.$forceUpdate();
    },
    closeDrawer() {
      this.showRight = false;
    },
    submit() {
      this.showRight = false;
      this.$emit('search', this.searchForm);
    },
    //时间选择确认
    onConfirm(res) {
      let agentTimeArr = [
        `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`,
        `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`
      ];
      this.searchForm.startDate = `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`;
      this.searchForm.endDate = `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`;
      this.$forceUpdate();
      this.$emit('dateComfirm', agentTimeArr);
    },
    //时间取消
    onCancel() {},
    reset() {
      this.searchForm = {};
      this.showRight = false;
      this.$emit('dateComfirm', []);
      this.$emit('search', this.searchForm);
    }
  }
};
</script>
<style lang="scss" scoped>
.option-wrap {
  height: 100%;
  overflow: auto;
  padding-bottom: 90rpx;
  .option-tap {
    padding: 0 20rpx;
    .option-title {
      height: 56rpx;
      line-height: 56rpx;
      font-size: 28rpx;
      margin: 10rpx 0;
      color: #666;
    }
    .option-item {
      border-radius: 35rpx;
      line-height: 70rpx;
      height: 70rpx;
      width: 30%;
      margin: 0 3% 20rpx 0;
      text-align: center;
      background: #ffffff;
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 28rpx;
      border: 1px solid #ddd;
      box-sizing: border-box;
      color: #333333;
    }
    .selected {
      border-color: #0079fe;
      color: #0079fe;
    }
    .time-item {
      border: 0;
      width: 40%;
      border-radius: 40rpx;
      background-color: #f2f2f2;
    }
    .divider {
      width: auto;
      border: 0;
      box-sizing: border-box;
    }
  }
}
.btn-tap {
  padding: 0 20rpx;
  text-align: right;
  position: fixed;
  background: #fff;
  width: 100%;
  bottom: 0;
  .btn-item {
    border-radius: 8rpx;
    line-height: 70rpx;
    height: 70rpx;
    width: 50%;
    text-align: center;
    background: #ffffff;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 28rpx;
    border: 1px solid #ddd;
    border-right: 0;
    box-sizing: border-box;
    color: #333333;
  }
  .cancle-btn {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
  }
  .them-btn {
    border-left: 0;
    border: 1px solid #005bac;
    background-color: #005bac;
    color: #ffffff;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }
}
</style>
