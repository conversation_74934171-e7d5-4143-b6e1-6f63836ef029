<template>
  <view class="bg">
    <view class="steps">
      <view class="steps_item" v-for="(i, index) in infoList" :key="index">
        <view class="s_r">
          <view
            class="index"
            :style="{ backgroundColor: backgroundColor, color: color }"
          ></view>
          <view class="line"></view>
        </view>
        <view class="s_l">
          <view class="info_item">
            <view class="top_info">
              <view class="date">{{ i.createDate }}</view>
              <view class="title"
                >[{{ getNodeName(i) }}] {{ i.updateUserName }}</view
              >
            </view>
            <view class="info">
              <view
                class="card_item"
                v-if="
                  i.logType === '4' ||
                    i.logType === '5' ||
                    i.logType === '13' ||
                    i.logType === '14'
                "
              >
                {{
                  i.logType === '4' || i.logType === '13' ? '验收' : '批示'
                }}结论：
                <text
                  style="color: #ff3b30;"
                  v-if="i.logType === '13' || i.logType === '14'"
                >
                  不通过
                </text>
                <text style="color: #4bd863;" v-else>
                  通过
                </text>
              </view>
              <view v-if="getLogData(i)" class="card_item">
                {{ getLogTimeName(i) }}：{{
                  i.logDate || i.updateDate.split(' ')[0]
                }}
              </view>
              <view v-if="getRemarkName(i)" class="card_item"
                >{{ getRemarkName(i) }}：{{ i.logRemark || '--' }}</view
              >
              <view v-if="i.logFile" class="files">
                <view class="label">附件：</view>
                <view class="fiel_list" v-if="i.fileList.length > 0">
                  <view
                    class="file_item"
                    v-for="(i, index) in i.fileList"
                    :key="index"
                  >
                    <!-- <text
                      @tap.stop="
                        downLoadFile(i.id, i.originalName, i.fileExtension)
                      "
                      >{{ i.originalName }}</text
                    > -->
                    <text @tap.stop="handleClick(i)">{{ i.originalName }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    infoList: {
      type: Array,
      default: () => []
    },
    color: {
      type: String,
      default: () => '#fff'
    },
    backgroundColor: {
      type: String,
      default: () => '#0079fe'
    },
    lineNum: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
      logStatus: [
        '登记',
        '指派',
        '办理',
        '验收',
        '批示',
        '催办',
        '延期',
        '撤销',
        '终止',
        '转办',
        '抄送',
        '进度反馈',
        '验收驳回',
        '批示驳回'
      ],
      logStatusRemarkName: [
        { type: '2', name: '指派说明' },
        { type: '3', name: '办理说明' },
        { type: '4', name: '验收意见' },
        { type: '5', name: '批示意见' },
        { type: '6', name: '催办说明' },
        { type: '7', name: '延期说明' },
        { type: '8', name: '撤销原因' },
        { type: '9', name: '终止原因' },
        { type: '10', name: '转办原因' },
        { type: '12', name: '反馈说明' },
        { type: '13', name: '验收意见' },
        { type: '14', name: '批示意见' }
      ]
    };
  },
  watch: {
    infoList: {
      handler: function() {
        this.init();
      },
      immediate: true
    }
  },
  methods: {
    async init() {
      this.infoList.forEach(async i => {
        if (i.logFile != null && i.logFile != '') {
          i.fileList = await this.getFiles(i.logFile);
        }
      });
    },
    async getFiles(fileIds) {
      let list = [],
        _self = this;
      await _self.ajax.getFileAttachmentByBusinessIdOrId(fileIds).then(res => {
        list = res.object;
      });
      return list;
    },
    handleClick(i) {
      this.$emit('handleClick', i);
    },
    getNodeName(row) {
      let nodeName = this.logStatus.find(
        (item, index) => index + 1 == parseInt(row.logType)
      );
      return nodeName;
    },
    getRemarkName(e) {
      let obj = this.logStatusRemarkName.find(el => el.type === e.logType);
      if (obj) {
        return obj.name;
      } else {
        return '';
      }
    },
    getLogTimeName(items) {
      return (
        this.logStatus.find(
          (item, index) => index + 1 == parseInt(items.logType)
        ) + '日期'
      );
    },
    getLogData(e) {
      if (['2', '3', '4', '5', '7', '10', '13', '14'].indexOf(e.logType) > -1) {
        return true;
      } else {
        return false;
      }
    },
    // 审核状态
    auditStatus(i) {
      if (i == 2) {
        return {
          text: '通过',
          color: '#00D288',
          bgColor: '#EAFFF8'
        };
      } else if (i == 3) {
        return {
          text: '驳回',
          color: '#FF4141',
          bgColor: '#FFDCD5'
        };
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.steps {
  display: flex;
  flex-direction: column;
  .files {
    display: flex;
    .label {
      font-size: 28rpx;
    }
    .fiel_list {
      width: calc(100% - 90rpx);
    }
    .file_item {
      font-size: 28rpx;
      color: #0079fe;
    }
  }
  .steps_item {
    display: flex;
    .s_r {
      padding: 0 20rpx;
      padding-top: 10rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      .line {
        flex: 1;
        width: 5rpx;
        border-left: 4rpx dashed #fff;
        background-color: #0079fe;
      }
      .index {
        width: 24rpx;
        height: 24rpx;
        border-radius: 50rpx;
        border: 4rpx solid #e3eeff;
        box-sizing: border-box;
      }
    }
    .s_l {
      display: flex;
      flex-direction: column;
      flex: 1;
      .info_item {
        background-color: #ffffff;
        margin-right: 30rpx;
        border-radius: 10rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .top_info {
          display: flex;
          .title {
            font-weight: 600;
            font-size: 28rpx;
            color: rgba(102, 102, 102, 1);
            overflow: hidden;
            text-overflow: ellipsis;
            margin-left: 10rpx;
          }
          .date {
            font-weight: 600;
            font-size: 28rpx;
            color: rgba(102, 102, 102, 1);
          }
        }
        .info {
          padding-bottom: 10rpx;
          .card_item {
            font-size: 28rpx;
            color: #5c5c5c;
          }
        }
      }
      .info_item:active {
        background-color: #f4f4f4;
      }
    }
  }
}
.ml5 {
  margin-left: 10rpx;
}
.mt10 {
  margin-top: 20rpx;
}
</style>
