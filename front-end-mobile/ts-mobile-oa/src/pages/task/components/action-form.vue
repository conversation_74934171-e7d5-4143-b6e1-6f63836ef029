<template>
  <uni-popup
    type="center"
    ref="basePopup"
    :topHeight="'20%'"
    :leftHeight="'20px'"
    :rightHeight="'20px'"
    style="background-color: #fff;"
    class="action"
  >
    <view style="background: #fff;">
      <view style="padding-bottom: 120rpx;">
        <view v-if="actionStatus == 3" class="completeDate"
          >原计划完成时间：{{ row.completeDate }}</view
        >
        <view v-if="actionStatus == 1 && row.urgeCount > 0" class="completeDate"
          >已催办{{ row.urgeCount }}次,上次催办时间:{{
            row.latelyUrgeDate
          }}</view
        >
        <base-form
          class="base-form"
          ref="baseForm"
          :form-list="useFormList"
          :form-data.sync="form"
          :rules="rules"
          :showSubmitButton="false"
          @select-person-callback="handleSelectPersonCallback"
        >
        </base-form>
      </view>
      <view class="footer">
        <view class="btn close" @click="close">取消</view>
        <view class="submit btn" @click="submit">确定</view>
      </view>
    </view>
  </uni-popup>
</template>
<script>
var dayjs = require('dayjs');
import BaseForm from '@/components/base-form/base-form.vue';
import actions from '../mixnis/actions';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
export default {
  components: { BaseForm, uniPopup },
  mixins: [actions],
  data() {
    return {
      personlist: [],
      useFormList: [],
      action: '',
      actionStatus: '',
      row: {}
    };
  },
  methods: {
    close() {
      this.$refs.basePopup.close();
      this.form = this.$options.data().form;
    },
    open(actionStatus, row) {
      this.useFormList = this.formList.filter(
        e => e.actionStatus.indexOf(actionStatus) > -1
      );
      this.actionStatus = Number(actionStatus);
      this.action = this.actions[Number(actionStatus) - 1];
      this.row = row;
      this.defaultTime();
      this.$refs.basePopup.open();
    },
    defaultTime() {
      if (this.actionStatus == 4) {
        this.form.feedbackDate = dayjs().format('YYYY-MM-DD');
      }
      if (this.actionStatus == 6) {
        this.form.handleDate = dayjs().format('YYYY-MM-DD');
      }
      if (this.actionStatus == 7) {
        this.form.transferDate = dayjs().format('YYYY-MM-DD');
      }
      if (this.actionStatus == 8) {
        this.form.checkDate = dayjs().format('YYYY-MM-DD');
      }
      if (this.actionStatus == 9) {
        this.form.approveDate = dayjs().format('YYYY-MM-DD');
      }
    },
    handleSelectPersonCallback(item) {
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      this.personlist = [];
      if (this.form[item.prop] != '') {
        this.personlist.push({
          id: this.form[item.propVal],
          name: this.form[item.prop]
        });
      }
      uni.$on('personlist', data => {
        this.personlist = data;
        uni.removeStorageSync('person_list');
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
        this.form[item.prop] = '';
        this.form[item.propVal] = '';
        this.form[item.propVal] = data.map(item => item.id).join(',');
        this.form[item.prop] = data.map(item => item.name).join(',');
      });
      uni.setStorageSync('person_list', JSON.stringify(this.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=radio'
      });
    },
    formatData(data) {
      if (data.cancelFilesList == null || data.cancelFilesList.length == 0) {
        data.cancelFiles = '';
      }
      delete data.cancelFilesList;
      if (data.delayFilesList == null || data.delayFilesList.length == 0) {
        data.delayFiles = '';
      }
      delete data.delayFilesList;
      if (
        data.feedbackFliesList == null ||
        data.feedbackFliesList.length == 0
      ) {
        data.feedbackFlies = '';
      }
      delete data.feedbackFliesList;
      if (data.closeFliesList == null || data.closeFliesList.length == 0) {
        data.closeFlies = '';
      }
      delete data.closeFliesList;
      if (data.handleFliesList == null || data.handleFliesList.length == 0) {
        data.handleFlies = '';
      }
      delete data.handleFliesList;
      if (
        data.transferFliesList == null ||
        data.transferFliesList.length == 0
      ) {
        data.transferFlies = '';
      }
      delete data.transferFliesList;
      if (data.checkFliesList == null || data.checkFliesList.length == 0) {
        data.checkFlies = '';
      }
      delete data.checkFliesList;
      if (data.approveFliesList == null || data.approveFliesList.length == 0) {
        data.approveFlies = '';
      }
      delete data.approveFliesList;
    },
    async submit() {
      const result = await this.$refs.baseForm.validate();
      if (!result) return;
      const data = Object.assign({}, this.form);
      data.registerId = this.row.id;
      if (this.row.taskId) {
        data.taskId = this.row.taskId;
      }
      this.formatData(data);
      for (let key in data) {
        if (data[key] == '' || data[key].length == 0) {
          delete data[key];
        }
      }
      const res = await this.ajax[this.action](data);
      if (!res.success) {
        uni.showToast({
          title: res.message || '操作失败!',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      uni.showToast({
        title: '操作成功',
        duration: 1000,
        icon: 'none'
      });
      setTimeout(() => {
        this.$set(this, 'form', {});
        this.$emit('ok');
        this.close();
      }, 500);
    }
  }
};
</script>
<style lang="scss">
.action {
  z-index: 10000 !important;
  background-color: rgba(0, 0, 0, 0.4) !important;
}
</style>
<style lang="scss" scoped>
::v-deep {
  .uni-transition {
    background: #fff;
    border-radius: 10rpx;
    padding-bottom: 10px;
    position: relative;
  }
  .uni-popup__wrapper-box {
    width: 100%;
  }
  .completeDate {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
    text-align: center;
  }
  .base-form {
    .u-form-item--left__content__label {
      font-size: 28rpx;
    }
  }
}
.footer {
  display: flex;
  height: 100rpx;
  border-radius: 0 0 10rpx 10rpx;
  position: absolute;
  bottom: -20rpx;
  width: 100%;
  .btn {
    line-height: 100rpx;
    width: 50%;
    text-align: center;
    font-size: 28rpx;
  }
  .submit {
    // background-color: #005bac;
    color: #005bac;
    border: 1px solid #ddd;
    border-radius: 0 0 10rpx 0;
  }
  .close {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 0 0 0 10rpx;
  }
}
</style>
