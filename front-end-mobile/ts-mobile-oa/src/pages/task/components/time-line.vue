<template>
  <view class="tl">
    <view class="tl-steps">
      <view class="tl-dot" :class="initClass(1)">1</view>
      <view class="tl-line" :class="initLine(1)"></view>
      <view class="tl-dot" :class="initClass(2)">2</view>
      <view class="tl-line" :class="initLine(2)"></view>
      <view class="tl-dot" :class="initClass(3)">3</view>
      <view class="tl-line" :class="initLine(3)"></view>
      <view class="tl-dot" :class="initClass(4)">4</view>
      <view class="tl-line" :class="initLine(4)"></view>
      <view class="tl-dot" :class="initClass(5)">5</view>
    </view>
    <view class="tl-steps-title">
      <view
        class="title"
        v-for="(item, index) in list"
        :key="index"
        :class="initClassText(index)"
      >
        <view class="label">{{ item.title }}</view>
      </view>
    </view>
    <view class="tl-steps-title">
      <view
        class="title"
        v-for="(item, index) in list"
        :key="index"
        :class="initClassText(index)"
      >
        <view class="text">{{ item.description }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      activeTep: 0,
      list: [
        { title: '提交', description: '' },
        { title: '指派', description: '' },
        { title: '承办', description: '' },
        { title: '验收', description: '' },
        { title: '批示', description: '' }
      ]
    };
  },
  methods: {
    initClass(index) {
      return this.activeTep >= index
        ? 'tl-green'
        : this.activeTep + 1 == index
        ? 'tl-gray'
        : 'tl-grays';
    },
    initLine(index) {
      return this.activeTep >= index ? 'tl-line-green' : '';
    },
    initClassText(index) {
      return this.activeTep > index ? 'active' : '';
    },
    formatData(data) {
      if (data.createUserName) {
        this.list[0].description = data.createUserName;
      }
      if (data.registerName) {
        this.list[1].description = data.registerName;
      }
      if (data.superviseTaskList.length > 0) {
        let names = '';
        data.superviseTaskList.forEach(e => {
          names += e.taskName + ',';
        });
        this.list[2].description = names.substring(0, names.lastIndexOf(','));
      }
      if (data.checkName) {
        this.list[3].description = data.checkName;
      }
      if (data.approveName) {
        this.list[4].description = data.approveName;
      }
      this.activeTep =
        data.registerStatus === '1'
          ? 1
          : data.registerStatus === '2'
          ? 2
          : data.registerStatus === '3'
          ? 3
          : data.registerStatus === '4'
          ? 4
          : data.registerStatus === '5'
          ? 5
          : 0;
    }
  }
};
</script>

<style lang="scss" scoped>
.tl {
  background: #fff;
  margin-bottom: 8rpx;
}
.tl-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 60rpx 10rpx 60rpx;
  .tl-dot {
    width: 42rpx;
    height: 42rpx;
    border-radius: 50%;
    line-height: 42rpx;
    text-align: center;
    color: #fff;
  }
  .tl-line {
    width: calc(20% - 12rpx);
    border: 1px dashed#cccccc;
  }
  .tl-line-green {
    border: 1px dashed #0076fe;
  }
  .tl-green {
    background: #0076fe !important;
  }
  .tl-grays {
    background: #cecece;
  }
  .tl-gray {
    background: #9a9a9a;
  }
}
.tl-steps-title {
  display: flex;
  align-items: center;
  padding: 0 20rpx 0 0rpx;
  .title {
    width: 17.6%;
    min-width: 17.6%;
    margin-left: 18rpx;
    text-align: center;
    color: #000;
    .label {
      font-size: 28rpx;
    }
    .text {
      font-size: 28rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      padding: 0 5rpx;
    }
  }
  .active {
    color: #0076fe;
  }
}
</style>
