<template>
  <view class="ts-content" v-if="showContent">
    <page-head title="任务办理" @clickLeft="returnBack"></page-head>
    <view class="search-box">
      <view class="search-form">
        <uni-search-bar
          radius="100"
          bgColor="#FFFFFF"
          searchBgColor="#eeeeee"
          borderColor="transparent"
          cancelButton="none"
          @confirm="search"
          placeholder="请输入任务名称"
        ></uni-search-bar>
      </view>
      <view class="search-sift" @tap="changeDrawer">
        <text class="search-sift-icon oa-icon oa-icon-shaixuan"></text>
      </view>
    </view>
    <scroll-view class="swiper-head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tab, index) in tabBarsHandle"
        class="uni-tab-item"
        :key="index"
        :data-current="index"
        @click="ontabtap"
      >
        <view :class="tabIndex === index ? 'uni-tab-item-title-active' : ''">
          <text class="uni-tab-item-title">{{ tab.name }}</text>
          <text
            class="uni-tab-item-num"
            v-if="tab.total != null && tab.total != 0"
            >{{ tab.total >= 100 ? '99+' : tab.total }}</text
          >
        </view>
      </view>
    </scroll-view>
    <swiper
      :current="tabIndex"
      class="swiper-box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in tabBarsHandle"
        :key="index"
      >
        <mescroll
          :ref="'mescroll' + index"
          :mescrollIndex="index"
          :down="item.downOption"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view class="contact-list">
            <list-item
              v-for="(item, index) in item['list']"
              :key="index"
              :dataSource="item"
              :colmun="colmun1"
              :footBtnList="getfootBtnList(item.registerStatus)"
              @itemClick="toDetail"
            />
          </view>
        </mescroll>
      </swiper-item>
    </swiper>
    <search-drawer
      ref="searchDrawer"
      :registerTypeList="registerTypeList"
      @search="searchByForm"
      @showPicker="showPicker"
      @dateComfirm="dateComfirm"
    />
    <date-picker
      startDate="2000-01-01"
      :value="agentTimeArr"
      endDate="2100-12-31"
      pickerZindex="999"
      mode="range"
      :current="true"
      @confirm="onConfirm"
      @cancel="onCancel"
      ref="range"
    ></date-picker>
    <action-form ref="actionForm" @ok="refh" />
  </view>
</template>

<script>
import Mescroll from '@/components/mescroll-swiper/mescroll.vue';
import taskMixnis from './mixnis/task.js';
import listItem from './components/list-item.vue';
import searchDrawer from './components/search-drawer.vue';
import datePicker from '@/components/picker/date-picker.vue';
import actionForm from './components/action-form.vue';
export default {
  components: {
    Mescroll,
    listItem,
    searchDrawer,
    datePicker,
    actionForm
  },
  mixins: [taskMixnis],
  data() {
    return {
      showContent: false,
      keywords: '',
      selectedList: [],
      tabIndex: 0,
      menuIndex: 2,
      queryMap: {},
      agentTimeArr: [],
      registerTypeList: []
    };
  },
  onLoad(opt) {
    if (opt.index && opt.index != 'undefined')
      this.tabIndex = Number(opt.index);
    this.tabBarsHandle.map((item, index) => {
      if (this.tabIndex == index) {
        item.downOption = true;
        item.isInit = true;
      } else {
        item.downOption = false;
        item.isInit = false;
      }
    });
    this.getSuperviseTypeList();
    this.showContent = true;
  },
  methods: {
    //搜索
    search(res) {
      this.keywords = res.value;
      this.tabBarsHandle.forEach(item => {
        item.list = [];
        item.isInit = false;
      });
      this.$nextTick(() => {
        this.tabBarsHandle[this.tabIndex]['isInit'] = true;
        this.$refs[`mescroll${this.tabIndex}`][0].downCallback();
      });
    },
    refh() {
      this.$refs[`mescroll${this.tabIndex}`][0].downCallback();
    },
    //显示时间弹出层
    showPicker() {
      this.$refs.range.show();
    },
    //时间选择确认
    onConfirm(res) {
      this.$refs.searchDrawer.onConfirm(res);
    },
    dateComfirm(timeArr) {
      this.agentTimeArr = timeArr;
    },
    //时间取消
    onCancel() {},
    searchByForm(searchForm) {
      this.queryMap = searchForm;
      this.tabBarsHandle.forEach(item => {
        item.list = [];
        item.isInit = false;
      });
      this.$nextTick(() => {
        this.tabBarsHandle[this.tabIndex]['isInit'] = true;
        this.$refs[`mescroll${this.tabIndex}`][0].downCallback();
      });
    },
    //分类抽屉切换
    changeDrawer() {
      this.$refs.searchDrawer.show();
    },
    //tab点解切换
    ontabtap(e) {
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      this.switchTab(Number(index));
    },
    async getSuperviseTypeList() {
      await this.ajax
        .getSuperviseTypeList()
        .then(async res => {
          this.registerTypeList = res.object;
        })
        .catch(() => {});
    },
    //tab滑动切换
    async ontabchange(e) {
      let index = e.target.current || e.detail.current;
      await this.switchTab(Number(index));
    },
    async switchTab(index) {
      if (this.tabIndex === index) return;
      else if (!this.tabBarsHandle[index]['isInit']) {
        this.tabBarsHandle[index]['isInit'] = true;
        await this.$refs[`mescroll${index}`][0].downCallback();
      }
      this.tabIndex = index;
    },
    async getListData(page, successCallback, errorCallback, keywords, index) {
      let query = {
        taskIndex: this.tabBarsHandle[index]['taskIndex'],
        pageSize: page.size,
        pageNo: page.num,
        registerMatter: this.keywords,
        sidx: 'create_date',
        sord: 'desc'
      };
      for (let key in this.queryMap) {
        query[key] = this.queryMap[key];
      }
      await this.ajax
        .superviseRegister(query)
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      this.tabBarsHandle[index]['total'] = totalCount;
      this.tabBarsHandle[index]['list'] = this.tabBarsHandle[index][
        'list'
      ].concat(rows);
    },
    datasInit(keywords, index) {
      this.tabBarsHandle[index]['list'] = [];
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  /deep/ .uni-navbar__header-container {
    padding-right: 88rpx;
  }
  /deep/ .uni-navbar__header-btns-right {
    position: absolute;
    right: 0;
  }
  .search-box {
    display: flex;
    align-items: center;
    background-color: #eeeeee;
    .search-form {
      flex: 1;
    }
    .search-sift {
      font-size: 28rpx;
      color: #666666;
      padding-right: 16rpx;
      .search-sift-icon {
        font-size: 36rpx !important;
        color: #666666;
      }
    }
  }
  .swiper-head {
    position: relative;
    width: 750rpx;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    white-space: nowrap;
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
    .uni-tab-item-title-active {
      color: $theme-color;
      border-bottom: 2px solid $theme-color;
    }
    .uni-tab-item {
      display: inline-block;
      flex-wrap: nowrap;
      width: 50%;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title,
      .uni-tab-item-num {
        color: #666;
        font-size: 30rpx;
        height: 100%;
        line-height: 2.5;
        flex-wrap: nowrap;
        box-sizing: border-box;
        white-space: nowrap;
      }
      .uni-tab-item-num {
        font-size: 28rpx;
        color: #fff;
        background-color: #f59a23;
        border-radius: 40rpx;
        padding: 0 10rpx;
        margin: 0 10rpx;
      }
    }
  }
  .swiper-box {
    flex: 1;
    .swiper-item {
      flex: 1;
      flex-direction: row;
    }
  }
}
</style>
