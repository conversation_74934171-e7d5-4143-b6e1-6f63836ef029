<template>
  <view class="ts-content" v-if="showContent">
    <page-head
      :title="title"
      rightIcon=""
      rightText=""
      @clickLeft="returnBack"
    ></page-head>
    <view style="padding-bottom: 100rpx">
      <view class="form-item">
        <view class="item-tile">
          <view class="t_l">
            <view class="t_l_l"></view>
            <view class="t_l_a">任务信息</view>
          </view>
        </view>
        <base-form
          class="base-form"
          ref="baseForm"
          :form-list="formList"
          :form-data.sync="form"
          :rules="rules"
          :showSubmitButton="false"
        ></base-form>
      </view>
      <view
        class="form-item mrt20"
        v-for="(item, index) in form.superviseTaskList"
        :key="index"
      >
        <view class="item-tile">
          <view class="t_l">
            <view class="t_l_l"></view>
            <view class="t_l_a">承办人</view>
            <view style="font-size: 24rpx; margin-left: 8rpx;color: #777"
              >(如果多人各自办理督办任务，请分别添加)</view
            >
          </view>
          <view class="t_r">
            <uni-icons
              :color="'#0079fe'"
              :type="'plus-filled'"
              size="48"
              @click="addTaskList"
            />
            <uni-icons
              :color="'#f23232'"
              :type="'minus-filled'"
              size="48"
              style="margin-left: 10rpx;margin-right: 15rpx"
              @click="delTaskList(index)"
              v-if="index >= 1"
            />
          </view>
        </view>
        <base-form
          class="base-form"
          :ref="`baseForm${index}`"
          :form-list="formListTaskSup"
          :form-data.sync="item"
          :rules="rules"
          @select-person-callback="e => handleSelectPersonCallback(e, item)"
          :showSubmitButton="false"
        ></base-form>
      </view>
      <view class="form-item" style="margin-top: 20rpx;">
        <base-form
          class="base-form"
          ref="baseFormBootm"
          :form-list="formListBottom"
          :form-data.sync="form"
          :rules="rules"
          @select-person-callback="handleSelectPersonCallback"
          :showSubmitButton="false"
        ></base-form>
      </view>
    </view>
    <view class="action-content">
      <view class="action-item" @click="submit(0)" v-if="type == ''"
        >存草稿</view
      >
      <view class="action-item submit-btn" @click="submit(2)">提交</view>
    </view>
  </view>
</template>

<script>
var dayjs = require('dayjs');
import BaseForm from '@/components/base-form/base-form.vue';
import common from '@/common/js/common.js';
import uniIcons from '@/components/uni-icons/uni-icons.vue';
import taskMixnis from './mixnis/addTaskSup.js';
export default {
  components: { BaseForm, uniIcons },
  mixins: [taskMixnis],
  data() {
    return {
      showContent: false,
      registerTypeList: [],
      action: '',
      id: '',
      personlist: [],
      toIndex: -1,
      type: '',
      title: '新增登记'
    };
  },
  watch: {
    'form.registerType': {
      handler(newValue, oldValue) {
        if (newValue) {
          let checkType = this.registerTypeList.find(e => e.id == newValue);
          this.form.approveCode = checkType.approveUserCode;
          this.form.approveName = checkType.approveUserName;
          this.form.checkCode = checkType.checkUserCode;
          this.form.checkName = checkType.checkUserName;
          this.form.registerUser = checkType.superviseUserCode;
          this.form.registerName = checkType.superviseUserName;
        }
      }
    }
  },
  async onLoad(opt) {
    this.action = 'saveTaskRegistrtion';
    await this.ajax
      .getSuperviseTypeList({ pageIndex: 1 })
      .then(async res => {
        this.registerTypeList = res.object;
        let list = this.registerTypeList.map(e => {
          return {
            label: e.typeName,
            value: e.id
          };
        });
        this.formList[1].optionList = list;
      })
      .catch(() => {});
    if (opt.id) {
      this.action = 'updateTaskRegistrtion';
      this.id = opt.id;
      await this.ajax
        .getTaskDetail({ id: opt.id })
        .then(async res => {
          let resList = [],
            taskList = [];
          if (
            res.object.registerFiles == null ||
            res.object.registerFiles == ''
          ) {
            res.object.registerFiles = common.guid();
          } else {
            resList = await this.getFiles(res.object.registerFiles);
          }
          if (res.object.taskFile == null || res.object.taskFile == '') {
            res.object.taskFile = common.guid();
          } else {
            taskList = await this.getFiles(res.object.taskFile);
          }
          if (res.object.superviseTaskList.length == 0) {
            res.object.superviseTaskList.push({
              taskCode: '',
              taskName: '',
              taskRemark: '',
              copyLeader: 1
            });
          }
          this.form = Object.assign({}, res.object);
          this.$set(this.form, 'registerFilesList', resList);
          this.$set(this.form, 'taskFileList', taskList);
        })
        .catch(() => {});
    }
    if (opt.index) this.toIndex = opt.index;
    if (opt.type) {
      this.type = opt.type;
      this.title = '任务指派';
      this.formList.forEach(e => {
        e.disabled = true;
      });
    } else {
      this.formList.forEach(e => {
        e.disabled = false;
      });
    }
    this.form.taskDate = dayjs().format('YYYY-MM-DD');
    this.showContent = true;
  },
  methods: {
    //获取附件
    async getFiles(fileIds) {
      let list = [],
        _self = this;
      await _self.ajax.getFileAttachmentByBusinessIdOrId(fileIds).then(res => {
        list = res.object;
      });
      return list;
    },
    handleSelectPersonCallback(item, e = null) {
      this.personlist = [];
      if (e == null) {
        if (this.form[item.prop] != '') {
          this.personlist.push({
            id: this.form[item.propVal],
            name: this.form[item.prop]
          });
        }
      } else {
        if (e[item.prop] != '') {
          this.personlist.push({
            id: e[item.propVal],
            name: e[item.prop]
          });
        }
      }
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
        if (e == null) {
          this.form[item.prop] = '';
          this.form[item.propVal] = '';
          this.form[item.propVal] = data.map(item => item.id).join(',');
          this.form[item.prop] = data.map(item => item.name).join(',');
        } else {
          e[item.prop] = '';
          e[item.propVal] = '';
          e[item.propVal] = data.map(item => item.id).join(',');
          e[item.prop] = data.map(item => item.name).join(',');
        }
      });
      uni.setStorageSync('person_list', JSON.stringify(this.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=radio'
      });
    },
    async getSuperviseTypeList() {
      await this.ajax
        .getSuperviseTypeList()
        .then(async res => {
          this.registerTypeList = res.object;
        })
        .catch(() => {});
    },
    delTaskList(index) {
      this.form.superviseTaskList.splice(index, 1);
    },
    addTaskList() {
      this.form.superviseTaskList.push({
        taskCode: '',
        taskName: '',
        taskRemark: '',
        copyLeader: 1
      });
    },
    async checkForm() {
      const result = await this.$refs.baseForm.validate();
      if (!result) return false;
      let boolean = true;
      this.form.superviseTaskList.forEach(async (item, index) => {
        let boo = await this.$refs[`baseForm${index}`][0].validate();
        if (!boo) {
          boolean = false;
        }
      });
      const result1 = await this.$refs.baseFormBootm.validate();
      if (!result1) return false;
      return result && result1 && boolean;
    },
    async submit(registerStatus) {
      let boolean = await this.checkForm();
      if (!boolean) return;
      const data = Object.assign({}, this.form);
      data.registerStatus = registerStatus;
      if (
        data.registerFilesList == null ||
        data.registerFilesList.length == 0
      ) {
        data.registerFiles = '';
      }
      delete data.registerFilesList;
      if (data.taskFileList == null || data.taskFileList.length == 0) {
        data.taskFile = '';
      }
      delete data.taskFileList;
      const res = await this.ajax[this.action](data);
      if (!res.success) {
        uni.showToast({
          title: res.message || '操作失败!',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      uni.showToast({
        title: '操作成功',
        duration: 1000,
        icon: 'none'
      });
      setTimeout(() => {
        this.$set(this, 'form', {});
        this.returnBack();
      }, 500);
    },
    //返回上一层
    returnBack() {
      this.form = this.$options.data().form;
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/ts-mobile-oa/pages/task/task-supervision?index=${this.toIndex}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .mrt20 {
    margin-top: 20rpx;
  }
  /deep/ .base-form {
    height: calc(100% - 84px);
    overflow: auto;
    .form-container {
      margin-top: 0;
    }
    .u-form-item--left__content__label {
      font-size: 28rpx;
    }
    .u-switch {
      &.u-switch--on {
        background: $theme-color !important;
      }
    }
  }
  .form-item {
    .item-tile {
      background: #fff;
      display: flex;
      justify-content: space-between;
      padding: 10rpx;
      border-bottom: 1px solid #bbb;
      .t_l {
        display: flex;
        padding-left: 15rpx;
        align-items: center;
        .t_l_l {
          width: 8rpx;
          height: 36rpx;
          background: #0079fe;
        }
        .t_l_a {
          font-size: 28rpx;
          margin-left: 10rpx;
        }
      }
    }
  }
  .action-content {
    position: fixed;
    bottom: 0;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    box-shadow: 0 -1px 6px #ccc;
    width: 100%;
    height: 40px;
    .action-item {
      flex: 1;
      text-align: center;
      color: $uni-text-color-grey;
      position: relative;
      &:not(:last-child)::after {
        content: ' ';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 15px;
        border-right: 1px solid #eee;
      }
      &.submit-btn {
        color: $theme-color;
      }
    }
  }
}
</style>
