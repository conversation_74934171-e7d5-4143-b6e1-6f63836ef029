<template>
  <view class="ts-content">
    <page-head
      :title="title"
      @clickLeft="returnBack"
      right-text="写邮件"
      @clickRight="sendEmail"
    ></page-head>
    <uni-search-bar
      cancelButton="none"
      radius="100"
      bgColor="#FFFFFF"
      searchBgColor="#eeeeee"
      borderColor="transparent"
      @confirm="search"
    ></uni-search-bar>
    <view class="uni-flex uni-row">
      <view class="row_item allread" @click="allread">
        <text class="row_item_icon oa-icon icon_xuanzhong"></text>
        全标已读
      </view>
      <view class="row_item editEmail" @click="isShowFoot">
        <text class="row_item_icon oa-icon icon_bianji1"></text>
        {{ isShowCheck ? '完成' : '编辑' }}
      </view>
    </view>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view class="contact_list">
          <view
            class="contact_item"
            v-for="(item, index) in dataList"
            :key="item.statusId"
            @tap="chooseItem(item, index)"
          >
            <uni-icons
              v-if="isShowCheck"
              :type="item.choose ? 'checkbox-filled' : 'circle'"
              :color="item.choose ? '#005BAC' : '#aaa'"
              size="48"
              style="margin-right: 20rpx;"
            />
            <image
              class="contact_item_img"
              :style="{ marginLeft: isShowCheck ? `20rpx` : 0 }"
              v-if="item.empHeadImg"
              :src="`${$config.BASE_HOST}${item.empHeadImg}`"
              mode="aspectFill"
            ></image>
            <view
              v-else
              class="contact_item_img"
              :class="item.empSex == 0 ? 'sex_man' : 'sex_woman'"
            >
              {{ item.senderName.substring(item.senderName.length - 2) }}
            </view>
            <view class="user_info">
              <view class="bolderText">
                <text class="unRead" v-if="item.isSeen === '0'"></text>
                <text class="sendPerson">{{ item.senderName }}</text>
                <text class="postTime">{{ item.postTime }}</text>
              </view>
              <view class="subject">
                <text
                  v-if="item.hasAttachment == 1"
                  class="oa-icon oa-icon-fujian"
                ></text>
                <text class="subjectTitle">{{
                  item.subject ? item.subject : '无主题'
                }}</text>
              </view>
              <rich-text
                class="emailContent"
                :nodes="
                  item.content
                    ? item.content.replace(/<\/?[^>]*>/g, '')
                    : '此邮件没有文字内容'
                "
              ></rich-text>
            </view>
          </view>
        </view>
      </mescroll>
    </view>
    <transition name="slide-fade">
      <view class="operation-box" v-show="isShowFootBtn">
        <view
          class="btn-item"
          v-for="(item, index) in footBtnList"
          :key="index"
          :class="item.class"
          @click="footBtnOperate(item.type)"
        >
          {{ item.name }}
        </view>
      </view>
    </transition>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import uniSearchBar from '@/components/uni-search-bar/uni-search-bar.vue';
export default {
  components: {
    uniSearchBar,
    mescroll
  },
  data() {
    return {
      fromPage: '',
      title: null,
      folderId: 1, //收件箱-1，发件箱-2，草稿箱-3，垃圾箱-4
      keywords: '',
      dataList: [],
      selectedList: [],
      checkType: 'checkBox',
      isShowCheck: false, //是否显示选框
      isShowFootBtn: false, //是否显示底部按钮
      footBtnList: [],
      footBtns: {
        deleteEmailBtn: {
          name: '删除',
          class: 'delete-btn',
          type: 'delete'
        },
        noreadEmailBtn: {
          name: '标记未读',
          class: 'unread-btn',
          type: 'unread'
        },
        readEmailBtn: {
          name: '标记已读',
          class: 'read-btn',
          type: 'read'
        },
        dropBtn: {
          name: '彻底删除',
          class: 'drop-btn',
          type: 'clean'
        },
        recoveryEmailBtn: {
          name: '回复邮件',
          class: 'recovery-btn',
          type: 'recovery'
        }
      }
    };
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.folderId = opt.folderId;
    if (this.folderId == 1) {
      this.footBtnList = [
        this.footBtns['deleteEmailBtn'],
        this.footBtns['noreadEmailBtn'],
        this.footBtns['readEmailBtn']
      ];
      this.title = '收件箱';
    }
    if (this.folderId == 2 || this.folderId == 3) {
      this.footBtnList = [this.footBtns['deleteEmailBtn']];
      if (this.folderId == 2) {
        this.title = '发件箱';
      } else {
        this.title = '草稿箱';
      }
    }
    if (this.folderId == 4) {
      this.footBtnList = [
        this.footBtns['dropBtn'],
        this.footBtns['recoveryEmailBtn']
      ];
      this.title = '垃圾箱';
    }
  },
  methods: {
    getListData(page, successCallback, errorCallback) {
      this.ajax
        .getEmailList({
          pageSize: page.size,
          pageNo: page.num,
          subject: this.keywords,
          folderId: this.folderId
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(e => {
          errorCallback();
        });
    },
    setListData(rows) {
      rows.forEach(item => {
        item.choose = this.selectedList.some(one => one.id === item.id);
      });
      this.dataList = this.dataList.concat(rows);
    },
    datasInit() {
      this.dataList = [];
    },
    chooseItem(item, index) {
      if (this.isShowCheck) {
        if (this.checkType === 'radio') {
          // 单选模式
          this.isShowFootBtn = true;
          if (!item.choose) {
            // 选中
            this.dataList.forEach(one => {
              one.choose = false; // 全部设为false
            });
            item.choose = true; // 当前项设为true
            this.selectedList = [item];
          } else {
            // 撤销选中
            item.choose = false;
            this.selectedList = [];
          }
        } else if (this.checkType === 'checkBox') {
          // 多选模式
          item.choose = !item.choose; // 改变当前点击项
          if (item.choose) {
            this.isShowFootBtn = true;
            this.selectedList.push(item);
          } else {
            this.selectedList = this.selectedList.filter(i => i.id != item.id);
          }
        }
      } else {
        if (this.folderId == 3) {
          if (item.outEmailAddress) {
            uni.navigateTo({
              url: `/pages/email/out-email-send?statusId=${item.statusId}&index=${this.folderId}&fromPage=${this.fromPage}`
            });
          } else {
            uni.navigateTo({
              url: `/pages/email/email-send?statusId=${item.statusId}&index=${this.folderId}&fromPage=${this.fromPage}`
            });
          }
        } else {
          this.$set(this.dataList[index], 'isSeen', '1');
          uni.navigateTo({
            url: `/pages/email/email-details?statusId=${item.statusId}&folderId=${this.folderId}&fromPage=${this.fromPage}`
          });
        }
      }
    },
    search(e) {
      this.keywords = e ? e.value : '';
      this.datasInit();
      this.$nextTick(() => {
        this.$refs['mescroll'].downCallback();
      });
    },
    isShowFoot() {
      this.isShowCheck = !this.isShowCheck;
      this.isShowFootBtn = !this.isShowFootBtn;
      if (!this.isShowFootBtn) {
        this.selectedList = [];
        this.dataList.forEach(one => {
          one.choose = false; // 全部设为false
        });
      }
    },
    //全标已读按钮点击事件
    allread() {
      uni.showModal({
        title: '提示',
        content: '您确定将全部邮件标记为已读？',
        confirmColor: '#005BAC',
        success: res => {
          if (res.confirm) {
            this.allReaded();
          }
        }
      });
    },
    //全标已读请求
    allReaded() {
      this.ajax.setEmailAllRead().then(res => {
        if (res.success) {
          uni.showToast({
            title: '操作成功',
            icon: 'none'
          });
          this.dataList.forEach(item => {
            item.isSeen = 1;
          });
        }
      });
    },
    //底部按钮操作
    footBtnOperate(type) {
      let statusIds = [];
      if (this.selectedList.length == 0) {
        uni.showToast({
          title: '请选择要操作的数据',
          icon: 'none'
        });
        return;
      }
      this.selectedList.forEach(e => {
        statusIds.push(e.statusId);
      });
      this.isShowCheck = false;
      this.isShowFootBtn = false;
      this.selectedList = [];
      this.dataList.forEach(one => {
        one.choose = false; // 全部设为false
      });
      this.$nextTick(() => {
        this.operateRequest(type, statusIds);
      });
    },
    //操作请求
    operateRequest(type, statusIds) {
      let idStr = statusIds.join(',');
      this.ajax
        .confirmEmail({
          ids: idStr,
          method: type
        })
        .then(res => {
          if (res.success) {
            uni.showToast({
              title: '操作成功',
              icon: 'none'
            });
            this.modifyData(type, statusIds);
          }
        });
    },
    modifyData(type, statusIds) {
      if (type == 'delete' || type == 'clean' || type == 'recovery') {
        this.dataList = this.dataList.filter(
          item => !statusIds.includes(item.statusId)
        );
      } else if (type == 'unread') {
        this.dataList.forEach(item => {
          if (statusIds.includes(item.statusId)) item.isSeen = '0';
        });
      } else if (type == 'read') {
        this.dataList.forEach(item => {
          if (statusIds.includes(item.statusId)) item.isSeen = '1';
        });
      }
    },
    sendEmail() {
      uni.navigateTo({
        url: `/pages/email/email-send?index=${this.folderId}&fromPage=${this.fromPage}`
      });
    },
    returnBack() {
      uni.redirectTo({
        url: `/pages/email/email-list?fromPage=${this.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  &::before {
    position: absolute;
    z-index: 10;
    right: 0;
    top: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #eeeeee;
  }
  .slide-fade-enter-active {
    //动画渐入
    transition: all 0.5s ease;
  }
  .slide-fade-leave-active {
    //动画渐出
    // transition: all .5s cubic-bezier(1.0, 0.5, 0.8, 1.0);
    transition: all 0.5s ease;
  }
  .slide-fade-enter, .slide-fade-leave-to
		/* .slide-fade-leave-active for below version 2.1.8 */ {
    //动画初始化
    transform: translateY(10px);
    opacity: 0;
  }
  .uni-row {
    height: 80rpx;
    background-color: #ffffff;
    width: 100%;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    .row_item {
      width: 50%;
      text-align: center;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333333;
      &:first-child::after {
        top: 20rpx;
        bottom: 20rpx;
        right: 0;
        width: 1px;
        content: '';
        position: absolute;
        background-color: #999999;
        -webkit-transform: scaleX(0.5);
        transform: scaleX(0.5);
      }
      .row_item_icon {
        line-height: 1;
        font-size: 32rpx;
        margin-right: 10rpx;
        color: #666666;
      }
    }
  }
  .mescroll-content {
    flex: 1;
    position: relative;
  }
  .contact_item {
    padding: 20rpx 30rpx;
    display: flex;
    align-items: center;
    background-color: #ffffff;
    position: relative;
    &::after {
      position: absolute;
      bottom: 0;
      right: 0;
      left: 30rpx;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &:last-child::after {
      height: 0;
    }
    .user_info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
    }
    .contact_item_img {
      width: 80rpx;
      height: 80rpx;
      margin-right: 20rpx;
      border-radius: 100%;
      color: #ffffff;
      text-align: center;
      line-height: 2.8;
    }
    .sex_man {
      background-color: $sexman-color;
    }
    .sex_woman {
      background-color: $sexwoman-color;
    }
    .sendPerson {
      font-weight: bolder;
      color: #333333;
      font-size: 32rpx;
    }
    .unRead {
      display: inline-block;
      width: 20rpx;
      height: 20rpx;
      background-color: #f59a23;
      border-radius: 100%;
      margin-right: 10rpx;
    }
    .subject {
      font-size: 28rpx;
      color: #333333;
      .oa-icon {
        font-size: 24rpx;
        color: #13b581;
        float: right;
      }
    }
    .emailContent {
      font-size: 28rpx;
      color: #999999;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
    .postTime {
      color: darkgrey;
      float: right;
      font-size: 20rpx;
      font-weight: normal;
    }
    .styleStr {
      color: blue;
      float: right;
    }
    .subjectTitle {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
  }
  .operation-box {
    background: #fff;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    box-shadow: 0 1px 6px #ddd;
    z-index: 10;
    .btn-item {
      flex: 1;
      line-height: 100rpx;
      height: 100rpx;
      text-align: center;
    }
    .unread-btn {
      color: #f59a23;
    }
    .read-btn {
      color: #005bac;
    }
    .recovery-btn {
      color: #005bac;
    }
  }
}
</style>
