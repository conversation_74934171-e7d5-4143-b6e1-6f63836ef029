<template>
  <view class="ts-content">
    <page-head
      :title="title"
      @clickLeft="returnBack"
      right-text="发送"
      @clickRight="reply"
    ></page-head>
    <view class="row_data">
      <text class="label">收件人：</text>
      <input class="row_input" v-model="email.toName" disabled />
      <text
        v-if="isShowChoosePerson"
        @click="choosePerson()"
        class="file oa-icon oa-icon-tianjiachaosong"
      ></text>
    </view>
    <view class="row_data">
      <text class="label">标题：</text>
      <input
        class="row_input"
        style="font-weight: bold;font-size: 32rpx;"
        v-model="email.subject"
      />
      <text class="file oa-icon oa-icon-fujian" @click="chooseFile()"></text>
    </view>
    <view>
      <textarea
        maxlength="-1"
        v-model="replyContent"
        placeholder="请输入正文"
        class="textarea"
      />
    </view>
    <view class="emailContent">
      <rich-text :nodes="contentStr"></rich-text>
    </view>
    <view class="opt-row">
      <view v-if="isShow">
        <view class="mask" v-if="isShowFile" @click="isShowFileList()"></view>
        <view class="attachment">
          <view
            class="file-icon oa-icon oa-icon-fujian"
            @click="isShowFileList()"
          >
            {{ fileCount }}个附件</view
          >
          <transition name="slide-fade">
            <view class="attachment_list" v-show="isShowFile">
              <view
                class="attachment_item"
                v-for="item in attachmentList"
                :key="item.id"
                @click="previewFile(item.id, item.fileName)"
              >
                <text
                  class="oa-icon"
                  :class="'oa-icon-' + $oaModule.formatFileType(item.extension)"
                ></text>
                <view class="attachment_item_info">
                  <text class="original_name">{{ item.originalName }}</text>
                  <text class="file_size">{{
                    item.fileSize | fileSizeFilter
                  }}</text>
                </view>
                <view
                  class="oa-icon oa-icon-xiazai down_load"
                  @tap.stop="downloadFile(item.id, item.fileName)"
                >
                </view>
              </view>
              <view
                class="attachment_item"
                v-for="item in uploadFile"
                :key="item.fileId"
              >
                <view
                  class="attachment_item_content"
                  @click="previewFile(item.fileId, item.fileRealName)"
                >
                  <text
                    class="oa-icon"
                    :class="
                      'oa-icon-' + $oaModule.formatFileType(item.fileType)
                    "
                  ></text>
                  <view class="attachment_item_info">
                    <text class="original_name">{{ item.fileName }}</text>
                    <text class="file_size">{{
                      item.fileSize | fileSizeFilter
                    }}</text>
                  </view>
                </view>
                <text
                  class="oa-icon oa-icon-xiazai down_load"
                  @tap.stop="downloadFile(item.id, item.fileName)"
                >
                </text>
                <text
                  class="oa-icon oa-icon-guanbi delete_file"
                  @click.stop="deleteFile(item.fileId)"
                ></text>
              </view>
            </view>
          </transition>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { chooseImage } from '../../common/js/uploadImg.js';
import Base64 from '@/common/js/base64.min.js';
export default {
  data() {
    return {
      fromPage: '',
      email: {
        senderName: null,
        toId: null,
        toName: null,
        subject: null,
        content: null,
        isDraft: 0,
        replyId: null,
        uploadedFile: '',
        saveToOutbox: 'on',
        sendToWx: 'on'
      },
      postTime: null,
      replyContent: '',
      contentStr: '',
      isShow: false,
      isShowFile: false,
      attachmentList: [],
      fileCount: 0,
      folderId: null,
      oldFileStr: '',
      isShowChoosePerson: false,
      title: '',
      fromType: '',
      toNameStr: '',
      personlist: [],
      uploadFile: []
    };
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    let details = uni.getStorageSync('email_detail');
    this.fromType = opt.from;
    this.postTime = details.postTime;
    this.email.senderName = details.senderName;
    this.toNameStr = details.toName || details.outEmailAddress;
    if (this.fromType == 'reply') {
      this.title = '回复邮件';
      this.email.toId = details.senderId;
      this.email.toName = details.senderName;
      this.email.subject = `回复：${details.subject}`;
    } else {
      this.title = '转发邮件';
      this.isShowChoosePerson = true;
      this.email.subject = `转发：${details.subject}`;
    }

    this.email.content = details.content;
    this.email.replyId = details.statusId;
    this.attachmentList = details.attachmentList ? details.attachmentList : [];
    this.folderId = opt.folderId;

    this.contentStr = '<br><br><br>';
    this.contentStr +=
      "<div style='background-color:#f2f2f2;padding:15px;margin-bottom:10px;border-radius: 5px;'>";
    this.contentStr +=
      "<p style='color:#999999;'>---------------原始邮件---------------</p>";
    this.contentStr +=
      "<p style='color:#333333;'><span style='color:#999999;margin-right:5px;'>发件人:</span>" +
      this.email.senderName +
      '</p>';
    this.contentStr +=
      "<p style='color:#333333;'><span style='color:#999999;margin-right:5px;'>发送时间:</span>" +
      this.postTime +
      '</p>';
    this.contentStr +=
      "<p style='color:#333333;'><span style='color:#999999;margin-right:5px;'>收件人:</span>" +
      this.toNameStr +
      '</p>';
    if (null != this.email.ccName) {
      this.contentStr +=
        "<p style='color:#333333;'><span style='color:#999999;margin-right:5px;'>抄送人:</span>" +
        this.email.ccName +
        '</p>';
    }
    this.contentStr +=
      "<p style='color:#333333;'><span style='color:#999999;margin-right:5px;'>主题:</span>" +
      this.email.subject +
      '</p>';
    let ct = this.email.content
      ? this.email.content.replace('padding:15px;', '')
      : '';
    this.contentStr += "<p style='color:#333333;'>" + ct + '</p>';
    this.contentStr += '</div>';

    if (null != details.attachmentList && details.attachmentList.length > 0) {
      this.isShow = true;
      this.attachmentList = details.attachmentList;
      this.fileCount = details.attachmentList.length;
      details.attachmentList.forEach(e => {
        this.oldFileStr += e.id + ',';
      });
    }
  },
  methods: {
    choosePerson() {
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        this.personlist = data;
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
        this.email.toId = '';
        this.email.toName = '';
        this.personlist.forEach(e => {
          this.email.toId += e.id + ',';
          this.email.toName += e.name + ',';
        });
        this.email.toId = this.email.toId.substring(
          0,
          this.email.toId.length - 1
        );
        this.email.toName = this.email.toName.substring(
          0,
          this.email.toName.length - 1
        );
      });
      uni.setStorageSync('person_list', JSON.stringify(this.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    chooseFile() {
      chooseImage({
        limitNum: 9, //数量
        showLoading: true,
        hideLoading: true,
        loadingTitle: '上传中...',
        uploadFileUrl: `${this.$config.BASE_HOST}/ts-document/attachment/fileUpload?module=email`, //服务器地址
        fileKeyName: 'file', //参数
        success: res => {
          let resVal = JSON.parse(res),
            nameList = resVal.object[0].fileName.split('.');
          resVal.object[0].fileType =
            nameList.length > 1 ? nameList[nameList.length - 1] : '';
          this.email.uploadedFile += resVal.object[0].fileId + ',';
          this.uploadFile.push(resVal.object[0]);
          this.fileCount = this.attachmentList.length + this.uploadFile.length;
          this.isShow = true;
        }
      });
    },
    reply() {
      let data = this.email;
      if (null == this.email.toId || '' == this.email.toId) {
        uni.showToast({
          title: '收件人不能为空',
          icon: 'none'
        });
        return;
      }
      if (null == this.email.subject || '' == this.email.subject) {
        uni.showToast({
          title: '请输入标题',
          icon: 'none'
        });
        return;
      }
      if (
        this.fromType == 'reply' &&
        (null == this.replyContent || '' == this.replyContent)
      ) {
        uni.showToast({
          title: '请输入正文',
          icon: 'none'
        });
        return;
      }
      data.content = this.replyContent + this.contentStr;
      data.uploadedFile += this.oldFileStr;
      this.ajax.sendEmailInternal(data, '正在发送').then(res => {
        uni.showToast({
          title: res.object,
          duration: 2000,
          icon: 'none'
        });
        uni.removeStorageSync('email_detail');
        this.$nextTick(() => {
          uni.navigateTo({
            url: `/pages/email/email-box-list?folderId=2&fromPage=${this.fromPage}`
          });
        });
      });
    },
    isShowFileList() {
      this.isShowFile = !this.isShowFile;
    },
    returnBack() {
      uni.removeStorageSync('email_detail');
      this.$nextTick(function() {
        uni.navigateTo({
          url: `/pages/email/email-details?folderId=${this.folderId}&statusId=${this.email.replyId}&fromPage=${this.fromPage}`
        });
      });
    },
    //删除附件
    deleteFile(id) {
      let fileList = this.email.uploadedFile.split(',');
      this.uploadFile.some((item, i) => {
        if (item.fileId == id) {
          fileList.splice(i, 1);
          this.uploadFile.splice(i, 1);
          return true;
        }
      });
      this.email.uploadedFile = fileList.join(',');
      this.fileCount = this.attachmentList.length + this.uploadFile.length;
      if (this.fileCount == 0) {
        this.isShowFile = false;
        this.isShow = false;
      }
    },
    previewFile(id, fileName) {
      let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    },
    downloadFile(id, fileName) {
      let filePath = `${this.$config.BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      this.$downloadFile.downloadFile(filePath);
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  overflow: hidden;
  background: #ffffff;
  // .slide-fade-enter-active {//动画渐入
  //   transition: all .8s linear;
  // }
  // .slide-fade-leave-active {//动画渐出
  //   transition: all .3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
  // }
  // .slide-fade-enter, .slide-fade-leave-to
  // /* .slide-fade-leave-active for below version 2.1.8 */ {//动画初始化
  //   transform: translateY(10px);
  //   opacity: 0;
  // }
  .row_data {
    font-size: 28rpx;
    border-bottom: 1px solid #efefef;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 0 10rpx;
    .label {
      font-size: 28rpx;
      color: #999999;
      padding: 20rpx;
    }
    .row_input {
      flex: 1;
      font-size: 28rpx;
      padding: 20rpx 0;
    }
    .file {
      color: #10b47f;
      font-size: 32rpx;
      padding: 20rpx;
      line-height: 1;
    }
  }
  .textarea {
    padding: 20rpx 30rpx;
    height: 400rpx !important;
    width: 100%;
    box-sizing: border-box;
  }
  .emailContent {
    font-size: 28rpx;
    margin-bottom: 80rpx;
    padding: 0 30rpx 20rpx;
    height: 100%;
  }
  .opt-row {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    box-shadow: 0 -1px 6px #ddd;
    .mask {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #333333;
      opacity: 0.2;
      z-index: 9;
    }
    .attachment {
      z-index: 10;
      background-color: #ffffff;
      position: relative;
      height: 100%;
      .file-icon {
        color: #10b47f;
        font-size: 28rpx;
        text-align: center;
        height: 80rpx;
        line-height: 80rpx;
        z-index: 10;
        background-color: #ffffff;
        position: relative;
      }
      .attachment_list {
        background-color: #ffffff;
        z-index: 10;
        position: relative;
        height: 100%;
        padding: 10rpx 20rpx 20rpx;
        box-sizing: border-box;
        max-height: 700rpx;
        overflow: auto;
        .attachment_item {
          text-decoration: none;
          display: block;
          font-size: 28rpx;
          color: #333333;
          padding: 6rpx 20rpx;
          border: 1px solid #dddddd;
          border-radius: 5px;
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;
          .attachment_item_content {
            flex: 1;
            display: flex;
            align-items: center;
          }
          .oa-icon {
            font-size: 40rpx;
            margin-right: 20rpx;
            color: $theme-color;
          }
          .attachment_item_info {
            flex: 1;
            .original_name {
              font-size: 28rpx;
              color: #333333;
              margin-right: 20rpx;
            }
            .file_size {
              color: #999999;
              font-size: 24rpx;
            }
          }
          .down_load {
            flex-shrink: 0;
            margin-left: 8px;
          }
        }
      }
    }
  }
}
</style>
