<template>
  <view class="ts-content">
    <page-head
      title="邮箱"
      @clickLeft="returnBack"
      right-text="写邮件"
      @clickRight="showActionSheet"
    ></page-head>
    <uni-list v-if="showContent">
      <uni-list-item
        v-for="(item, index) in email_main_list"
        :key="index"
        :title="item.title"
        :number="item.number"
        :show-badge="item.show_badge"
        :badge-text="item.badge_text"
        :badgeType="item.badgeType"
        :thumb="item.thumb"
        @click="loadEmailDetailsList(item.index)"
        :iconStyleStr="item.iconStyleStr"
      ></uni-list-item>
    </uni-list>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import uniList from '@/components/uni-list/uni-list.vue';
import uniListItem from '@/components/uni-list-item/uni-list-item.vue';
export default {
  components: {
    uniList,
    uniListItem
  },
  data() {
    return {
      showContent: false,
      fromPage: '',
      email_main_list: [
        {
          title: '收件箱',
          show_badge: true,
          badge_text: '',
          badgeType: 'warning',
          index: '1',
          thumb: 'oa-icon-shoujianxiang',
          number: '',
          iconStyleStr: 'color: rgb(249, 170, 93);font-size:60rpx'
        },
        {
          title: '发件箱',
          show_badge: false,
          badgeType: 'warning',
          index: '2',
          number: '',
          thumb: 'oa-icon-fajianxiang',
          iconStyleStr: 'color: rgb(123, 184, 114);font-size:60rpx'
        },
        {
          title: '草稿箱',
          show_badge: false,
          badgeType: 'warning',
          index: '3',
          number: '',
          thumb: 'oa-icon-caogaoxiang',
          iconStyleStr: 'color: rgb(74, 166, 234);font-size:60rpx'
        },
        {
          title: '垃圾箱',
          show_badge: false,
          badgeType: 'warning',
          index: '4',
          number: '',
          thumb: 'oa-icon-lajixiang',
          iconStyleStr: 'color: rgb(45, 118, 235);font-size:60rpx'
        }
      ],
      showBadge: false
    };
  },
  computed: {
    ...mapState(['empcode'])
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.getEmailNum();
  },
  methods: {
    getEmailNum() {
      this.ajax
        .getEmailNum({
          userCode: this.empcode
        })
        .then(res => {
          let data = res.object;
          data.forEach((item, i) => {
            switch (item.FOLDER_ID) {
              case 1:
                this.email_main_list[0].number = data[i].EMAIL_NUMS.toString();
                if (data[i].NO_READ > 0) {
                  this.showBadge = true;
                  this.email_main_list[0].badge_text = data[
                    i
                  ].NO_READ.toString();
                }
                break;
              case 2:
                this.email_main_list[1].number = data[i].EMAIL_NUMS.toString();
                break;
              case 3:
                this.email_main_list[2].number = data[i].EMAIL_NUMS.toString();
                break;
              case 4:
                this.email_main_list[3].number = data[i].EMAIL_NUMS.toString();
                break;
            }
          });
          this.showContent = true;
        });
    },
    loadEmailDetailsList(index) {
      uni.navigateTo({
        url: `/pages/email/email-box-list?fromPage=${this.fromPage}&folderId=${index}`
      });
    },
    showActionSheet() {
      uni.showActionSheet({
        itemList: ['内部邮件', '外部邮件'],
        success: res => {
          if (res.tapIndex == 0) {
            uni.navigateTo({
              url: `/pages/email/email-send?fromPage=${this.fromPage}&index=0`
            });
          } else {
            uni.navigateTo({
              url: `/pages/email/out-email-send?fromPage=${this.fromPage}&index=0`
            });
          }
        }
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/${this.fromPage}`
      });
    }
  }
};
</script>

<style scoped>
.uni-list::before {
  height: 1px;
}
.uni-actionsheet__cell {
  font-size: 32rpx;
}
</style>
