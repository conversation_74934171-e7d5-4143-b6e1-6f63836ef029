<template>
  <view class="ts-content">
    <page-head
      title="信息发布"
      @clickLeft="returnBack"
      rightText="提交"
      @clickRight="releaseSubmit"
    ></page-head>
    <view class="uni-input-group">
      <view class="form_row dis_flex">
        <view class="row_lable">
          <text class="required_red oa-icon oa-icon-asterisks"></text>
          <text class="lable_text">所属栏目</text>
        </view>
        <view
          class="row_value row_value_input"
          data-popup-type="bottom"
          data-popup-name="popcolumn"
          @tap="togglePopColumn"
        >
          <input
            class="row_value_input_text"
            disabled="disabled"
            placeholder="请选择所属栏目"
            :value="info.channelName"
          />
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
      <view class="form_row">
        <view class="row_lable">
          <text class="required_red oa-icon oa-icon-asterisks"></text>
          <text class="lable_text">文档标题</text>
        </view>
        <view class="row_value row_value_textarea">
          <textarea
            class="row_value_textarea_text"
            :class="{ slectedInp: info.informationTitle }"
            style="max-height: 170rpx;"
            placeholder="长度不超过50字"
            maxlength="50"
            v-model="info.informationTitle"
          />
          <text
            class="markInRed"
            :class="info.titleColor == 'on' ? 'selected' : 'markInRed'"
            @tap="toggleMark"
            >标红</text
          >
        </view>
      </view>
      <view class="form_row">
        <view class="row_lable" style="width: 100%;">
          <text class="lable_text">可查看人</text>
          <text class="row_lable_placeholder" v-if="!viewerNameEllipsis"
            >不选择表示完全公开</text
          >
          <text
            class="add_icon oa-icon oa-icon-tianjiachaosong"
            @tap="showPersonSelet"
          ></text>
        </view>
        <view class="row_value row_value_personal" v-if="viewerNameEllipsis">
          <text class="personNameStr" @tap="showPersonSelet">{{
            viewerNameEllipsis
          }}</text>
          <uni-icons
            v-if="viewerNameEllipsis"
            :size="40"
            class="uni-icon-wrapper"
            color="#bbb"
            type="closeempty"
            @tap="emptyPerson"
          />
        </view>
      </view>
      <view class="form_row dis_flex">
        <view class="row_lable">有效期限</view>
        <view class="row_value row_value_input" @tap="showPicker('range')">
          <input
            class="row_value_input_text"
            disabled="true"
            placeholder="为空表示永久有效"
            :value="rangeDate"
          />
          <uni-icons
            v-if="rangeDate"
            :size="48"
            class="uni-icon-wrapper"
            color="#bbb"
            type="clear"
            @tap="clearIcon"
          />
        </view>
      </view>
    </view>
    <view class="main_text">
      <textarea
        maxlength="-1"
        placeholder="请输入正文"
        class="textarea"
        v-model="info.informationContent"
      />
    </view>
    <view class="opt-row">
      <view v-if="isShow">
        <view class="mask" v-if="isShowFile" @click="isShowFileList"></view>
        <view class="attachment">
          <view
            class="file_icon oa-icon oa-icon-fujian"
            @click="isShowFileList"
          >
            {{ fileCount }}个附件</view
          >
          <transition name="slide-fade">
            <view class="attachment_list" v-show="isShowFile">
              <view
                class="attachment_item"
                v-for="(item, index) in attachmentList"
                :key="index"
              >
                <view
                  class="attachment_item_content"
                  @click="previewFile(item.fileId, item.fileRealName)"
                >
                  <text
                    class="oa-icon"
                    :class="
                      'oa-icon-' + $oaModule.formatFileType(item.fileType)
                    "
                  ></text>
                  <view class="attachment_item_info">
                    <text class="original_name">{{ item.fileName }}</text>
                    <text class="file_size">{{
                      item.fileSize | fileSizeFilter
                    }}</text>
                  </view>
                  <view
                    class="oa-icon oa-icon-xiazai down_load"
                    @click.stop="downloadFile(item.fileId, item.fileRealName)"
                  >
                  </view>
                </view>
                <text
                  @click="deleteFile(item.fileId)"
                  class="oa-icon oa-icon-guanbi delete_file"
                ></text>
              </view>
            </view>
          </transition>
        </view>
      </view>
    </view>
    <view class="bottomBtn">
      <view
        class="btn-item"
        data-popup-type="bottom"
        data-popup-name="popupdetail"
        @tap="togglePopDetail"
      >
        <text></text>
        <text>高级设置</text>
      </view>
      <view class="btn-item" @tap="addFile">
        <text></text>
        <text>附件</text>
      </view>
    </view>
    <!-- 栏目来源弹出层 -->
    <uni-popup ref="showpopcolumn" :type="popupType">
      <view class="popup-content">
        <view class="contact-list scroll_list" v-if="columnlist.length > 0">
          <view
            class="contact-item"
            v-for="item in columnlist"
            :key="item.id"
            :data-column-id="item.id"
            :data-column-name="item.channelName"
            data-popup-name="popcolumn"
            @tap="selectColumn"
          >
            <text class="contact-item-text">{{ item.channelName }}</text>
            <uni-icons
              style="line-height: 1;"
              v-if="info.channelId == item.id"
              type="checkmarkempty"
              color="#005BAC"
              size="48"
            />
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- 详细设置弹出层 -->
    <uni-popup ref="showpopupdetail" :type="popupType">
      <view class="popup-content uni-input-group">
        <view class="form_row dis_flex">
          <view class="row_lable">消息推送</view>
          <view class="row_value">
            <switch
              class="extra_switch"
              :checked="info.messageRemind === 'on' ? true : false"
              data-tap="messageRemind"
              @change="onSwitchChange"
            />
          </view>
        </view>
        <!-- <view class="form_row dis_flex">
					<view class="row_lable">水印</view>
					<view class="row_value">
						<switch class="extra_switch" :checked="info.watermark === 'on' ? true : false" data-tap="watermark" @change="onSwitchChange" />
					</view>
				</view>
				<view class="form_row dis_flex">
					<view class="row_lable">水印文字</view>
					<view class="row_value row_value_input">
						<input class="row_value_input_text" :class="{'slectedInp':info.watermarkText }" placeholder="自定义水印文字,长度不超过10字" maxlength="10" v-model="info.watermarkText" />
					</view>
				</view> -->
      </view>
      <view class="pop_btn-wrap">
        <button
          class="pop_colse_btn"
          type="default"
          data-popup-name="popupdetail"
          @tap="closeDetail"
        >
          确定
        </button>
      </view>
    </uni-popup>
    <date-picker
      startDate="2000-01-01"
      :value="agentTimeArr"
      endDate="2100-12-31"
      mode="range"
      :current="true"
      @confirm="onConfirm"
      @cancel="onCancel"
      ref="range"
    ></date-picker>
  </view>
</template>

<script>
import { chooseImage } from '../../common/js/uploadImg.js';
import datePicker from '@/components/picker/date-picker.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import Base64 from '@/common/js/base64.min.js';
export default {
  components: {
    uniPopup,
    datePicker
  },
  data() {
    return {
      showContent: false,
      isSubmit: false,
      isShow: false,
      isShowFile: false,
      fileCount: ' 0',
      info: {
        channelName: '', //栏目Name
        channelId: '', //栏目ID
        informationTitle: '', //标题
        validbeginTime: '', //有效开始时间
        validendTime: '', //有效结束时间
        titleColor: 'off', //是否标红,
        informationReaderName: '', //可查看人姓名,
        informationReader: '', //可查看人id,
        //informationReaderOrg: '',  //可查看人部门
        //informationReaderGroup: '',  //可查看人群组,
        informationContent: '', //正文,
        messageRemind: 'on', //消息推送 on开	off关
        watermark: 'on', //水印 on开	off关
        watermarkText: '', //水印文字
        uploadedFile: '', //附件ID
        informationStatus: '1'
      },
      agentTimeArr: [],
      rangeDate: '',
      columnlist: [],
      personlist: [],
      viewerNameEllipsis: '',
      popupType: '', //弹出层类型
      attachmentList: []
    };
  },
  onLoad(opt) {
    let _self = this;
    _self.fromPage = opt.fromPage ? opt.fromPage : '';
    this.getInformationColumn();
  },
  methods: {
    //获取栏目选项
    getInformationColumn() {
      let _self = this;
      _self.ajax.getInformationChannel().then(res => {
        _self.columnlist = _self.columnlist.concat(res.object);
      });
    },
    togglePopColumn(e) {
      let _self = this;
      let data = e.currentTarget.dataset;
      _self.popupType = data.popupType;
      _self.$nextTick(() => {
        _self.$refs[`show${data.popupName}`].open();
      });
    },
    selectColumn(e) {
      let _self = this;
      let data = e.currentTarget.dataset;
      _self.info.channelId = data.columnId;
      _self.info.channelName = data.columnName;
      _self.$refs[`show${data.popupName}`].close();
    },
    toggleMark() {
      if (this.info.titleColor == 'on') {
        this.info.titleColor = 'off';
      } else {
        this.info.titleColor = 'on';
      }
    },
    //跳转至人员选择
    showPersonSelet() {
      let _self = this;
      //监听事件
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        _self.personlist = data;
        let viewerNameArr = [],
          viewerIdArr = [],
          viewerNameEllipsisArr = [];
        data.forEach((item, index) => {
          viewerNameArr.push(item.name);
          viewerIdArr.push(item.id);
          if (index < 3) {
            viewerNameEllipsisArr.push(item.name);
          }
        });
        _self.viewerNameEllipsis = viewerNameEllipsisArr.join(',');
        if (data.length > 3) {
          _self.viewerNameEllipsis += `等${data.length}人`;
        }
        _self.info.informationReaderName = viewerNameArr.join(',');
        _self.info.informationReader = viewerIdArr.join(',');
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      uni.setStorageSync('person_list', JSON.stringify(_self.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    emptyPerson() {
      let _self = this;
      _self.personlist = [];
      _self.viewerNameEllipsis = '';
      _self.info.informationReaderName = '';
      _self.info.informationReader = '';
    },
    //显示时间弹出层
    showPicker(dateRef) {
      this.$refs[dateRef].show();
    },
    //时间选择确认
    onConfirm(res) {
      let _self = this;
      _self.rangeDate = res.result;
      _self.agentTimeArr = [
        `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`,
        `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`
      ];
      _self.info.validbeginTime = `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`;
      _self.info.validendTime = `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`;
    },
    //时间取消
    onCancel() {},
    clearIcon() {
      let _self = this;
      _self.rangeDate = '';
      _self.agentTimeArr = [];
      _self.info.validbeginTime = ``;
      _self.info.validendTime = ``;
    },
    togglePopDetail(e) {
      let _self = this,
        data = e.currentTarget.dataset;
      _self.popupType = data.popupType;
      _self.$nextTick(() => {
        _self.$refs[`show${data.popupName}`].open();
      });
    },
    closeDetail(e) {
      let _self = this,
        data = e.currentTarget.dataset;
      _self.$nextTick(() => {
        _self.$refs[`show${data.popupName}`].close();
      });
    },
    addFile() {
      let _self = this;
      chooseImage({
        limitNum: 9, //数量
        showLoading: true,
        hideLoading: true,
        loadingTitle: '上传中...',
        uploadFileUrl: `${_self.$config.BASE_HOST}/ts-document/attachment/fileUpload?module=email`, //服务器地址
        fileKeyName: 'file', //参数
        success: res => {
          let resVal = JSON.parse(res),
            nameList = resVal.object[0].fileName.split('.');
          resVal.object[0].fileType =
            nameList.length > 1 ? nameList[nameList.length - 1] : '';
          _self.info.uploadedFile += `${resVal.object[0].fileId},`;
          _self.isShow = true;
          _self.attachmentList.push(resVal.object[0]);
          _self.fileCount = _self.attachmentList.length;
        }
      });
    },
    isShowFileList() {
      this.isShowFile = !this.isShowFile;
    },
    deleteFile(id) {
      let _self = this;
      _self.attachmentList.some((item, i) => {
        if (item.fileId == id) {
          _self.attachmentList.splice(i, 1);
          return true;
        }
      });
      _self.fileCount = _self.attachmentList.length;
      if (_self.fileCount == 0) {
        _self.isShowFile = false;
        _self.isShow = false;
      }
    },
    previewFile(id, fileName) {
      let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    },
    downloadFile(id, fileName) {
      let filePath = `${this.$config.BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      this.$downloadFile.downloadFile(filePath);
    },
    //启动开关
    onSwitchChange(event) {
      let data = event.currentTarget.dataset;
      this.info[data.tap] = event.detail.value ? 'on' : 'off';
    },
    releaseSubmit() {
      let _self = this,
        data = _self.info;
      if (_self.isSubmit) {
        return false;
      }
      _self.isSubmit = true;

      _self.attachmentList.forEach(e => {
        data.uploadedFile += `${e.fileId},`;
      });
      //发送邮件需判断必填项
      if (null == data.channelId || '' == data.channelId) {
        uni.showToast({
          title: '请选择栏目',
          duration: 2000,
          icon: 'none'
        });
        return;
      } else if (null == data.informationTitle || '' == data.informationTitle) {
        uni.showToast({
          title: '请输入标题',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      _self.ajax.saveInformationApply(data).then(res => {
        uni
          .showToast({
            icon: 'none',
            title: '提交成功',
            complete: () => {
              setTimeout(() => {
                _self.isSubmit = false;
                uni.redirectTo({
                  url: '/pages/information/my-information-list'
                });
              }, 1500);
            }
          })
          .catch(e => {
            _self.isSubmit = false;
          })
          .finally(e => {
            _self.isSubmit = false;
          });
      });
    },
    returnBack() {
      let _self = this;
      if (_self.fromPage === 'workflowList') {
        uni.redirectTo({
          url: '/pages/workflow/workflow-list'
        });
      } else {
        this.$parentTypeFun({
          type: 'jumpPage',
          path: '/workbench'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  .dis_flex {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
  .form_row {
    position: relative;
    background-color: #ffffff;
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      left: 30rpx;
      right: 0;
      transform: scaleY(-0.5);
      height: 1px;
      background-color: #eee;
    }
    .row_lable {
      width: 240rpx;
      padding: 22rpx 30rpx;
      box-sizing: border-box;
      position: relative;
      color: #333;
      .required_red {
        color: #f00;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 4rpx;
        font-size: 24rpx;
      }
      .row_lable_placeholder {
        color: #bbb;
        float: right;
        margin-right: 60rpx;
      }
      .add_icon {
        font-size: 40rpx;
        padding: 0 30rpx;
        line-height: 1;
        color: #005bac;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      .slectedInp {
        color: #666666;
      }
      & ~ .row_value {
        flex: 1;
        font-size: 32rpx;
        color: #666;
        padding: 22rpx 30rpx;
        padding-left: 0;
        box-sizing: border-box;
        text-align: right;
        .markInRed {
          border: 1px solid #bbbbbb;
          color: #bbbbbb;
          font-size: 28rpx;
          padding: 0 8px;
          border-radius: 28rpx;
          margin-left: 10px;
          position: absolute;
          bottom: 22rpx;
          right: 30rpx;
        }
        .markInRed.selected {
          font-weight: bold;
          border: 1px solid red;
          color: red;
        }
      }
      & ~ .row_value_input {
        display: flex;
        justify-content: center;
        align-items: center;
        .row_value_input_text {
          text-align: right;
          flex: 1;
          font-size: 32rpx;
        }
        .doc_num_item {
          padding: 2px 20rpx;
          border: 1px solid #dddddd;
          color: #999;
          font-size: 24rpx;
          border-radius: 40rpx;
          margin: 0 20rpx;
        }
        .doc_num_item.selected {
          background-color: #e5edff;
          border: 1px solid #b5ccff;
          color: #005bac;
        }
      }
      & ~ .row_value_textarea {
        width: 100%;
        padding-left: 30rpx;
        padding-top: 0;
        text-align: left;
        .row_value_textarea_text {
          width: 100%;
          min-height: 160rpx;
          font-size: 32rpx;
        }
        .textarea-placeholder {
          color: #bbb;
        }
      }
      & ~ .row_value_personal {
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        text-align: left;
        padding: 0 30rpx 22rpx;
        .personNameStr {
          flex: 1;
        }
      }
    }
  }
  .main_text {
    width: 100%;
    background-color: #ffffff;
    .textarea {
      width: 100%;
      padding: 20rpx;
      color: #666666;
      box-sizing: border-box;
      height: 600rpx;
    }
  }
  .opt-row {
    position: fixed;
    bottom: 90rpx;
    left: 0;
    right: 0;
    background-color: #ffffff;
    box-shadow: 0 1px 6px #ddd;
    .mask {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #333333;
      opacity: 0.2;
      z-index: 9;
    }
    .attachment {
      z-index: 10;
      background-color: #ffffff;
      position: relative;
      height: 100%;
      .file_icon {
        color: #10b47f;
        font-size: 28rpx;
        text-align: center;
        padding: 16rpx 0;
        z-index: 10;
        background-color: #ffffff;
        position: relative;
      }
      .attachment_list {
        background-color: #ffffff;
        z-index: 10;
        position: relative;
        height: 100%;
        padding: 10rpx 20rpx 20rpx;
        .attachment_item {
          text-decoration: none;
          display: block;
          font-size: 28rpx;
          color: #333333;
          padding: 6rpx 20rpx;
          border: 1px solid #dddddd;
          border-radius: 5px;
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;
          .attachment_item_content {
            flex: 1;
            display: flex;
            align-items: center;
          }
          .oa-icon {
            font-size: 40rpx;
            margin-right: 20rpx;
            color: $theme-color;
          }
          .attachment_item_info {
            flex: 1;
            .original_name {
              font-size: 28rpx;
              color: #333333;
              margin-right: 20rpx;
            }
            .file_size {
              color: #999999;
              font-size: 24rpx;
            }
          }
        }
      }
    }
  }
  .bottomBtn {
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 6px #ddd;
    .btn-item {
      flex: 1;
      height: 90rpx;
      line-height: 90rpx;
      box-sizing: border-box;
      text-align: center;
      font-size: 32rpx;
      color: #333333;
      position: relative;
      &::before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #dddddd;
      }
      &:first-child::before {
        width: 0;
      }
    }
  }
  .popup-content {
    .contact-list {
      background-color: #ffffff;
      .contact-item {
        padding: 22rpx 30rpx;
        font-size: 28rpx;
        color: #333333;
        position: relative;
        display: flex;
        &::after {
          position: absolute;
          z-index: 10;
          right: 0;
          left: 30rpx;
          bottom: 0;
          height: 1px;
          content: '';
          -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
          background-color: #dddddd;
        }
        &:last-child::after {
          height: 0;
        }
        .contact-item-text {
          flex: 1;
        }
      }
    }
    .scroll_list {
      max-height: 800rpx;
      overflow: auto;
    }
  }
  .pop_btn-wrap {
    padding: 22rpx 30rpx;
    .pop_colse_btn {
      background-color: #005bac;
      color: #ffffff;
      border-radius: 10rpx;
      font-size: 28rpx;
    }
  }
}
</style>
