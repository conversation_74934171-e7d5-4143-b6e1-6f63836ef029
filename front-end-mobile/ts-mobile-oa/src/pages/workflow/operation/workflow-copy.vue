<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="抄送"></page-head>
    <view class="form">
      <form @submit="formSubmit">
        <view class="row">
          <view class="row-lable" style="width: 100%;">
            <view>
              <text class="required-red oa-icon oa-icon-asterisks"></text>
              <text class="row-lable_text">抄送人</text>
            </view>
            <text
              class="add-icon oa-icon oa-icon-tianjiachaosong"
              @click="choosePerson"
            ></text>
            <input
              v-show="false"
              class="row-value_input_text"
              placeholder="请选择抄送人"
              name="copytoUserCode"
              v-model="copyForm.copytoUserCode"
            />
          </view>
          <view class="row-value row-value_personal">
            <text class="personNameStr">{{ copytoUserNameStr }}</text>
            <uni-icons
              v-if="copytoUserNameStr"
              :size="40"
              class="uni-icon-wrapper"
              color="#bbb"
              type="closeempty"
              @tap="emptyAttendee"
            />
          </view>
        </view>
        <view class="bottom-btn">
          <button class="btn-item uni-bg-blue" form-type="submit">提交</button>
        </view>
      </form>
    </view>
  </view>
</template>

<script>
import graceChecker from '@/common/js/graceChecker.js';
export default {
  data() {
    return {
      pagePramas: {},

      workflowInfo: {},
      copyForm: {
        copytoUserCode: '',
        copytoUserName: '',
        remark: ''
      },
      copytoUserNameStr: '',
      rule: [
        {
          filedKey: 'copytoUserCode',
          required: true,
          checkType: 'notnull',
          errorMsg: '请选择抄送人'
        }
      ],
      personlist: [],

      isSubmit: false
    };
  },
  onLoad(opt) {
    this.pagePramas = opt;

    this.workflowInfo = uni.getStorageSync('workflow_info');
  },
  methods: {
    //选择会签人
    choosePerson() {
      let _self = this;
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        _self.personlist = data;
        let participantArr = [];
        _self.copyForm.copytoUserCode = '';
        _self.copyForm.copytoUserName = '';
        data.forEach((item, index) => {
          _self.copyForm.copytoUserCode += `${item.id},`;
          _self.copyForm.copytoUserName += `${item.name},`;
          if (index < 4) {
            participantArr.push(item.name);
          }
        });
        _self.copytoUserNameStr = participantArr.join('、');
        if (data.length > 3) {
          _self.copytoUserNameStr += `等${data.length}人`;
        }
        _self.copyForm.copytoUserCode = _self.copyForm.copytoUserCode.substring(
          0,
          _self.copyForm.copytoUserCode.length - 1
        );
        _self.copyForm.copytoUserName = _self.copyForm.copytoUserName.substring(
          0,
          _self.copyForm.copytoUserName.length - 1
        );
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      uni.setStorageSync('person_list', JSON.stringify(_self.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    //清空会签人
    emptyAttendee() {
      this.personlist = [];
      this.copyForm.copytoUserCode = '';
      this.copyForm.copytoUserName = '';
      this.copytoUserNameStr = '';
    },
    formSubmit(e) {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;

      //进行表单检查
      let checkRes = graceChecker.check(e.detail.value, this.rule);
      if (checkRes) {
        const data = {
          ...this.copyForm,
          wfInstanceId: this.workflowInfo.wfInstanceId,
          wfStepId: this.workflowInfo.currentStepNo,
          taskId: this.workflowInfo.taskId
        };
        this.ajax.handleWorkflowCopy(data).then(res => {
          uni.showToast({
            title: '抄送成功',
            icon: 'none',
            complete: () => {
              setTimeout(() => {
                let pagePramasStr = this.$common.convertObj(this.pagePramas);
                let jumpUrl = '';
                if (this.pagePramas.name == 'copy') {
                  jumpUrl = '/pages/workflow/my-workflow-detail';
                } else {
                  jumpUrl = '/pages/workflow/approval-custom-detail';
                }
                uni.redirectTo({
                  url: `${jumpUrl}?${pagePramasStr}`
                });
              }, 1500);
            }
          });
        });
      } else {
        this.isSubmit = false;
        uni.showToast({ title: graceChecker.error, icon: 'none' });
      }
    },
    //返回上一层
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .form {
    position: relative;
    &::before {
      position: absolute;
      content: '';
      top: 0;
      left: 0;
      right: 0;
      transform: scaleY(-0.5);
      height: 1px;
      background-color: #eee;
      z-index: 10;
    }
    .row {
      width: 100%;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 30rpx;
        right: 0;
        transform: scaleY(-0.5);
        height: 1px;
        background-color: #eee;
      }
      &:last-child::after {
        height: 0;
      }
      .row-lable {
        width: 240rpx;
        font-size: 32rpx;
        color: #333;
        padding: 22rpx 30rpx;
        box-sizing: border-box;
        position: relative;
        .row-lable_text {
          padding-right: 20rpx;
          box-sizing: border-box;
        }
        .required-red {
          color: #f00;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 4rpx;
          font-size: 24rpx;
        }
        .add-icon {
          font-size: 40rpx;
          padding: 0 30rpx;
          line-height: 1;
          color: #005bac;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .row-lable ~ .row-value {
        flex: 1;
        font-size: 32rpx;
        color: #666;
        padding: 22rpx 30rpx;
        padding-left: 0;
        box-sizing: border-box;
        text-align: right;
      }
      .row-lable ~ .row-value_input {
        display: flex;
        justify-content: center;
        align-items: center;
        .row-value_input_text {
          text-align: right;
          flex: 1;
          font-size: 32rpx;
        }
      }
      .row-lable ~ .row-value_textarea {
        width: 100%;
        padding-left: 30rpx;
        padding-top: 0;
        text-align: left;
        .row-value_textarea_text {
          width: 100%;
          min-height: 180rpx;
          font-size: 32rpx;
          border-color: #eee;
          background: #f4f4f4;
          padding: 8rpx;
        }
        .textarea-placeholder {
          color: #bbb;
        }
      }
      .row-lable ~ .row-value_personal {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        text-align: left;
        padding: 0 15px 11px;
      }
    }
    .bottom-btn {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #ffffff;
      box-shadow: 0 1px 6px #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 22rpx 30rpx;
      box-sizing: border-box;
      z-index: 10;
      .btn-item {
        flex: 1;
        box-sizing: border-box;
        text-align: center;
        font-size: 32rpx;
        position: relative;
        border-radius: 10rpx;
      }
    }
  }
}
</style>
