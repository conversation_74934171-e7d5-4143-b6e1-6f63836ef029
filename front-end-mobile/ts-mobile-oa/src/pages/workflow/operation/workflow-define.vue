<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="审批流程"></page-head>
    <view class="content-wrap">
      <view class="content">
        <view class="step-list">
          <view
            class="step-item"
            v-for="(item, index) in nextNodeList"
            :key="item.wfStepNo"
          >
            <view class="step-item-name">
              {{ item.wfStepName }}
              <text
                class="required-red oa-icon oa-icon-asterisks"
                v-if="item.promoterChoice == 1"
              ></text>
            </view>
            <view class="step-item-type">
              {{ multiInstanceType[item.multiInstanceType]['text'] }}
              <text class="step-item-type_explain">
                {{ multiInstanceType[item.multiInstanceType]['explainText'] }}
              </text>
            </view>
            <view class="step-item-tip" v-if="item.promoterChoice == 0">
              管理员设置由该节点审批人选择
            </view>
            <view class="step-item-user-list" v-else>
              <view
                class="user-item"
                v-for="(personItem, personIndex) in item.selectedUserList"
                :key="personItem.id"
              >
                {{ personItem.name }}
                <text
                  class="user-item-delete oa-icon oa-icon-guanbi"
                  v-if="item.userList.length != 1"
                  @click="deleteNodeUsers(index, personIndex)"
                ></text>
              </view>
              <view
                class="add-step-user-btn"
                v-if="item.isAllUser == 'Y' || item.userList.length > 1"
                @click="chooseNodeUsers(index)"
              >
                <text>+</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="bottom-btn">
      <button class="btn-item uni-bg-blue" @tap="approvalSubmit">
        提交办理
      </button>
    </view>
  </view>
</template>

<script>
import { multiInstanceType } from '../mixins/dictionary.js';
export default {
  data() {
    return {
      multiInstanceType,
      nextNodeList: [],
      formInfo: {},
      isSubmit: false,
      type: ''
    };
  },
  onLoad(opt) {
    this.type = opt.fromPage;
    let nodeList = uni.getStorageSync('node_list');
    this.formInfo = uni.getStorageSync('form_info');
    if (nodeList.length) {
      this.nextNodeList = nodeList.map(item => {
        item.userList = this.handleUserList(item.userList);
        let selectedUserList = [];
        if (item.isAllUser == 'Y') {
          selectedUserList = [];
        } else if (item.userList.length == 1 || item.approverFill == 1) {
          selectedUserList = item.userList;
        } else {
          selectedUserList = [];
        }
        return {
          ...item,
          selectedUserList
        };
      });
    }
  },
  methods: {
    handleUserList(rows) {
      let list = [];
      rows.forEach(item => {
        list.push({
          id: item.usercode,
          name: item.username,
          empHeadImg: item.empHeadImg,
          empDeptName: item.deptname,
          empDutyName: item.dutyCode,
          sex: item.sex
        });
      });
      return list;
    },
    //选择办理人
    chooseNodeUsers(index) {
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', res => {
        this.$set(this.nextNodeList[index], 'selectedUserList', res);
        uni.removeStorageSync('person_list');
        uni.removeStorageSync('approval_person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      this.jumpPageToSelectPerson(
        this.nextNodeList[index].selectedUserList,
        this.nextNodeList[index].userList
      );
    },
    jumpPageToSelectPerson(selectedUserList, userList) {
      uni.setStorageSync('person_list', JSON.stringify(selectedUserList));
      if (userList?.length) {
        uni.setStorageSync('approval_person_list', JSON.stringify(userList));
      }
      uni.navigateTo({
        url: `/pages/selectPerson/select-person?checkType=checkBox${
          userList?.length ? '&approval=true' : ''
        }`
      });
    },
    deleteNodeUsers(index, personIndex) {
      this.nextNodeList[index].selectedUserList.splice(personIndex, 1);
    },
    approvalSubmit() {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;
      var nodeList = [];
      for (let i = 0; i < this.nextNodeList.length; i++) {
        const nextNodeItem = this.nextNodeList[i];
        if (nextNodeItem.promoterChoice == 1) {
          if (nextNodeItem.selectedUserList.length > 0) {
            nodeList.push({
              stepId: nextNodeItem.wfStepId,
              stepName: nextNodeItem.wfStepName,
              stepApproveCode: nextNodeItem.selectedUserList
                .map(item => {
                  return item.id;
                })
                .join(','),
              stepApproveName: nextNodeItem.selectedUserList
                .map(item => {
                  return item.name;
                })
                .join(','),
              wfDefinitionId: nextNodeItem.wfDefinitionId
            });
          } else {
            uni.showToast({
              title: `${nextNodeItem.wfStepName}节点办理人不能为空`,
              icon: 'none'
            });
            this.isSubmit = false;
            return false;
          }
        }
      }
      this.ajax
        .saveApproveChoice(nodeList)
        .then(res => {
          let allData = {
            ...this.formInfo,
            choiceId: res.object,
            wfStepId: nodeList[0].stepId,
            users: nodeList[0].stepApproveCode,
            names: nodeList[0].stepApproveName
          };
          if (this.type == 'reinitWorkflow') {
            this.reSubmitApply(allData);
          } else {
            this.submitApply(allData);
          }
        })
        .catch(e => {
          this.isSubmit = false;
        });
    },
    //提交申请到服务器
    submitApply(allData) {
      this.ajax
        .startProcessInstance(allData)
        .then(res => {
          uni.showToast({ title: '提交成功!', icon: 'none' });
          uni.removeStorageSync('node_list');
          uni.removeStorageSync('form_info');
          this.$nextTick(() => {
            uni.redirectTo({
              url: '/pages/workflow/my-workflow-list'
            });
          });
        })
        .catch(e => {
          this.isSubmit = false;
        });
    },
    reSubmitApply(allData) {
      let ajaxPath = '/ts-form/form/api/examination';
      this.ajax
        .saveApply(ajaxPath, allData)
        .then(res => {
          uni.removeStorageSync('workflow_info');
          this.$nextTick(() => {
            uni.redirectTo({
              url: '/pages/workflow/my-workflow-list'
            });
          });
        })
        .catch(e => {
          this.isSubmit = false;
        });
    },
    //返回上一层
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.ts-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  .required-red {
    color: #f00;
    font-size: 32rpx;
  }
  .content-wrap {
    flex: 1;
    overflow: hidden;
    .content {
      height: 100%;
      overflow: auto;
      .step-list {
        background: #fff;
        padding: 20rpx 30rpx;
        margin-bottom: 16rpx;
      }
    }
    .step-item {
      padding-bottom: 20rpx;
      position: relative;
      padding-left: 68rpx;
      color: #333;
      .step-item-name {
        font-weight: bold;
      }
      .step-item-type {
        font-size: 26rpx;
        color: #666;
        .step-item-type_explain {
          color: #999;
        }
      }
      .step-item-tip {
        font-size: 26rpx;
        color: #999;
        padding: 12rpx;
        background-color: #f8f8f8;
        border-radius: 8rpx;
      }
      .step-item-user-list {
        display: flex;
        align-content: center;
        flex-wrap: wrap;
        .user-item {
          display: inline-block;
          padding: 0 12rpx;
          background-color: #ebf2f8;
          color: #666;
          border-radius: 8rpx;
          font-size: 28rpx;
          height: 60rpx;
          line-height: 60rpx;
          margin-bottom: 16rpx;
          margin-right: 16rpx;
          .user-item-delete {
            line-height: 1;
            padding-left: 12rpx;
            color: #999;
          }
        }
      }
      .add-step-user-btn {
        background-color: #ebf2f8;
        color: #005bac;
        height: 60rpx;
        width: 60rpx;
        display: inline-block;
        text-align: center;
        font-size: 40rpx;
        border-radius: 8rpx;
      }
      &::before {
        content: '\e6a2';
        position: absolute;
        background: #fff;
        left: 0;
        font-size: 48rpx;
        font-family: oa-icon;
        line-height: 60rpx;
        color: #d3d6dd;
        z-index: 9;
      }
      &::after {
        content: '';
        position: absolute;
        left: 22rpx;
        top: 0;
        bottom: 0;
        width: 4rpx;
        background-color: #eee;
      }
      &:last-child::after {
        width: 0;
      }
    }
  }
  .bottom-btn {
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 22rpx 30rpx;
    box-sizing: border-box;
    .btn-item {
      flex: 1;
      box-sizing: border-box;
      text-align: center;
      font-size: 32rpx;
      position: relative;
      border-radius: 10rpx;
    }
  }
}
</style>
