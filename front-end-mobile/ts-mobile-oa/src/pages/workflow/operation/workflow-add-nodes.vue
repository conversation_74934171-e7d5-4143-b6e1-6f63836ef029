<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="加签"></page-head>
    <view class="form" v-if="showContent">
      <form @submit="formSubmit">
        <view v-if="isHandledBy" class="row dis-flex">
          <view class="row-lable">
            <text class="required-red oa-icon oa-icon-asterisks"></text>
            <text class="row-lable_text">加签节点</text>
          </view>
          <view class="row-value row-value_input">
            <input
              class="row-value_input_text"
              placeholder="请选择加签节点"
              name="wfStepName"
              v-model="countersignForm.wfStepName"
              disabled
              @click="handleSelectSignatureNode"
            />
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
            />
          </view>
        </view>
        <view v-else class="row dis-flex">
          <view class="row-lable">
            <text class="required-red oa-icon oa-icon-asterisks"></text>
            <text class="row-lable_text">加签节点</text>
          </view>
          <view class="row-value row-value_input">
            <input
              class="row-value_input_text"
              placeholder="请选择加签节点"
              name="wfStepName"
              v-model="countersignForm.wfStepName"
              disabled
            />
          </view>
        </view>
        <view class="row">
          <view class="row-lable" style="width: 100%;">
            <view>
              <text class="required-red oa-icon oa-icon-asterisks"></text>
              <text class="row-lable_text">加签人</text>
            </view>
            <text
              class="add-icon oa-icon oa-icon-tianjiachaosong"
              @click="choosePerson"
            ></text>
            <input
              v-show="false"
              class="row-value_input_text"
              placeholder="请选择加签人"
              name="assigneeNo"
              v-model="countersignForm.assigneeNo"
            />
          </view>
          <view class="row-value row-value_personal">
            <text class="personNameStr" @click="choosePerson">
              {{ assigneeNameStr }}
            </text>
            <uni-icons
              v-if="assigneeNameStr"
              :size="40"
              class="uni-icon-wrapper"
              color="#bbb"
              type="closeempty"
              @tap="emptyAttendee"
            />
          </view>
        </view>
        <view class="row">
          <view class="row-lable">
            <text class="row-lable_text">备注</text>
          </view>
          <view class="row-value row-value_textarea">
            <textarea
              class="row-value_textarea_text"
              placeholder="请输入"
              auto-height
              name="remark"
              v-model="countersignForm.remark"
            />
          </view>
        </view>
        <view class="bottom-btn">
          <button class="btn-item uni-bg-blue" form-type="submit">提交</button>
        </view>
      </form>
    </view>

    <uni-popup type="bottom" popup-choice="radio" top-height="45%" ref="popup">
      <view class="contact-list scroll_list">
        <view
          class="contact-item"
          v-for="(item, index) in wfStepListOption"
          :key="index"
          :data-column-value="item.wfStepId"
          :data-column-text="item.wfStepName"
          data-ref="popup"
          @tap="handleClickSelectWfStep"
        >
          <text class="contact-item_text">{{ item.wfStepName }}</text>
          <view class="contact-item_icon">
            <uni-icons
              v-if="countersignForm.wfStepId === item.wfStepId"
              type="checkmarkempty"
              color="#005BAC"
              size="44"
            />
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import graceChecker from '@/common/js/graceChecker.js';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
export default {
  components: {
    uniPopup
  },
  data() {
    return {
      showContent: false,

      pagePramas: {},

      typeName: '',
      wfInstanceId: '',
      taskId: '',
      wfStepListOption: [],

      countersignForm: {
        wfStepId: '',
        wfStepName: '',
        wfStepNo: '',

        assigneeNo: '',
        assigneeName: '',
        taskId: '',
        remark: ''
      },
      assigneeNameStr: '',
      rule: [
        {
          filedKey: 'assigneeNo',
          required: true,
          checkType: 'notnull',
          errorMsg: '请选择加签人'
        }
      ],
      personlist: [],

      isSubmit: false
    };
  },
  computed: {
    isHandledBy() {
      return (
        this.typeName === 'approvalDone' &&
        this.taskId === null &&
        this.wfInstanceId
      );
    }
  },
  async onLoad(opt) {
    this.pagePramas = opt;

    this.typeName = opt.name;
    this.workflowInfo = uni.getStorageSync('workflow_info');
    this.countersignForm.taskId = this.workflowInfo.taskId;
    this.wfInstanceId = this.workflowInfo.wfInstanceId;
    this.taskId = this.workflowInfo.taskId;
    if (this.isHandledBy) {
      this.countersignForm.wfInstanceId = this.workflowInfo.wfInstanceId;
      await this.ajax
        .getWfStepListByWfDefIdToAddSignature({
          wfInstanceId: this.workflowInfo.wfInstanceId
        })
        .then(res => {
          this.wfStepListOption = res.object;
        });
    } else {
      await this.ajax
        .getAddSignatureStepName({
          taskId: this.taskId
        })
        .then(res => {
          if (res.object && res.object.wfStepName) {
            this.countersignForm.wfStepName = res.object.wfStepName;
          } else {
            this.countersignForm.wfStepName = this.workflowInfo.currentStepName;
          }
        });
    }
    this.showContent = true;
  },
  methods: {
    handleSelectSignatureNode(e) {
      this.$nextTick(() => {
        this.$refs['popup'].open();
      });
    },

    handleClickSelectWfStep(e) {
      let data = e.currentTarget.dataset;
      this.countersignForm.wfStepId = data.columnValue;
      this.countersignForm.wfStepName = data.columnText;

      const selectStepItem =
        this.wfStepListOption.find(
          item => item.wfStepId === data.columnValue
        ) || {};
      this.countersignForm.wfStepNo = selectStepItem.wfStepNo;

      this.$nextTick(() => {
        this.$refs['popup'].close();
      });
    },
    //选择会签人
    choosePerson() {
      let _self = this;
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        _self.personlist = data;
        let participantArr = [];
        _self.countersignForm.assigneeNo = '';
        _self.countersignForm.assigneeName = '';
        data.forEach((item, index) => {
          _self.countersignForm.assigneeNo += `${item.id},`;
          _self.countersignForm.assigneeName += `${item.name},`;
          if (index < 4) {
            participantArr.push(item.name);
          }
        });
        _self.assigneeNameStr = participantArr.join('、');
        if (data.length > 3) {
          _self.assigneeNameStr += `等${data.length}人`;
        }
        _self.countersignForm.assigneeNo = _self.countersignForm.assigneeNo.substring(
          0,
          _self.countersignForm.assigneeNo.length - 1
        );
        _self.countersignForm.assigneeName = _self.countersignForm.assigneeName.substring(
          0,
          _self.countersignForm.assigneeName.length - 1
        );
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      uni.setStorageSync('person_list', JSON.stringify(_self.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    //清空会签人
    emptyAttendee() {
      this.personlist = [];
      this.countersignForm.assigneeNo = '';
      this.countersignForm.assigneeName = '';
      this.assigneeNameStr = '';
    },
    formSubmit(e) {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;

      let ruleList = [...this.rule];
      if (this.isHandledBy) {
        ruleList.unshift({
          filedKey: 'wfStepName',
          required: true,
          checkType: 'notnull',
          errorMsg: '请选择加签节点'
        });
      }
      //进行表单检查
      let checkRes = graceChecker.check(e.detail.value, ruleList);
      if (checkRes) {
        let API = null;
        // 经办加签接口
        if (this.isHandledBy) {
          API = this.ajax.addSignatureByStep;
        } else {
          API = this.ajax.handleAddNodes;
        }

        API(this.countersignForm).then(res => {
          uni.showToast({
            title: '加签成功',
            icon: 'none',
            complete: () => {
              setTimeout(() => {
                let pagePramasStr = this.$common.convertObj(this.pagePramas);
                uni.redirectTo({
                  url: `/pages/workflow/approval-custom-detail?${pagePramasStr}`
                });
              }, 1500);
            }
          });
        });
      } else {
        this.isSubmit = false;
        uni.showToast({ title: graceChecker.error, icon: 'none' });
      }
    },
    //返回上一层
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .scroll_list {
    max-height: 800rpx;
    overflow: auto;
  }
  .contact-list {
    background-color: #ffffff;
    width: 100%;
    height: 100%;
    overflow: auto;
    .contact-item {
      padding: 22rpx 30rpx;
      font-size: 28rpx;
      color: #333333;
      position: relative;
      display: flex;
      align-items: center;
      &::after {
        position: absolute;
        z-index: 10;
        right: 0;
        left: 30rpx;
        bottom: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #eeeeee;
      }
      &:last-child::after {
        height: 0;
      }
      .contact-item_text {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
      }
      .contact-item_icon {
        line-height: 1;
      }
    }
  }
  .form {
    position: relative;
    &::before {
      position: absolute;
      content: '';
      top: 0;
      left: 0;
      right: 0;
      transform: scaleY(-0.5);
      height: 1px;
      background-color: #eee;
      z-index: 10;
    }
    .dis-flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .row {
      width: 100%;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 30rpx;
        right: 0;
        transform: scaleY(-0.5);
        height: 1px;
        background-color: #eee;
      }
      &:last-child::after {
        height: 0;
      }
      .row-lable {
        width: 240rpx;
        font-size: 32rpx;
        color: #333;
        padding: 22rpx 30rpx;
        box-sizing: border-box;
        position: relative;
        .row-lable_text {
          padding-right: 20rpx;
          box-sizing: border-box;
        }
        .required-red {
          color: #f00;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 4rpx;
          font-size: 24rpx;
        }
        .add-icon {
          font-size: 40rpx;
          padding: 0 30rpx;
          line-height: 1;
          color: #005bac;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .row-lable ~ .row-value {
        flex: 1;
        font-size: 32rpx;
        color: #666;
        padding: 22rpx 30rpx;
        padding-left: 0;
        box-sizing: border-box;
        text-align: right;
      }
      .row-lable ~ .row-value_input {
        display: flex;
        justify-content: center;
        align-items: center;
        .row-value_input_text {
          text-align: right;
          flex: 1;
          font-size: 32rpx;
        }
      }
      .row-lable ~ .row-value_personal {
        display: -webkit-box;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        -webkit-box-align: center;
        -webkit-align-items: center;
        align-items: center;
        text-align: left;
        padding: 0 15px 11px;
      }
      .row-lable ~ .row-value_textarea {
        width: 100%;
        padding-left: 30rpx;
        padding-top: 0;
        text-align: left;
        .row-value_textarea_text {
          width: 100%;
          min-height: 180rpx;
          font-size: 32rpx;
        }
        .textarea-placeholder {
          color: #bbb;
        }
      }
    }
    .bottom-btn {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #ffffff;
      box-shadow: 0 1px 6px #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 22rpx 30rpx;
      box-sizing: border-box;
      z-index: 10;
      .btn-item {
        flex: 1;
        box-sizing: border-box;
        text-align: center;
        font-size: 32rpx;
        position: relative;
        border-radius: 10rpx;
      }
    }
  }
}
</style>
