<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="确认退回"></page-head>
    <view class="content_wrap">
      <view class="approval_info">
        <view class="approval_row dis_flex">
          <view class="row_lable">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            <text class="row_lable_text">退回节点</text>
          </view>
          <view class="row_value row_value_input" @click="showPopup">
            <text
              class="row_value_input_text"
              :class="{ 'none-data': !node['text'] }"
            >
              {{ node['text'] || '请选择退回节点' }}
            </text>
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
            />
          </view>
        </view>
        <view class="approval_row">
          <view class="row_lable">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            <text class="row_lable_text">退回说明</text>
          </view>
          <view class="row_value row_value_textarea">
            <textarea
              class="row_value_textarea_text"
              v-model="handleMarkedWords"
              placeholder="请说明退回原因"
            />
          </view>
        </view>
        <!-- <view class="approval_row dis_flex">
          <view class="row_lable">
            <text class="row_lable_text">短信提醒</text>
          </view>
          <view class="row_value">
            <switch class="row_value_switch" @change="onSwitchChange" />
          </view>
        </view> -->
      </view>
    </view>
    <view class="bottom_btn">
      <button class="btn-item uni-bg-blue" @tap="approvalSubmit">
        提交
      </button>
    </view>
    <uni-popup type="bottom" ref="popup">
      <view class="contact-list scroll_list">
        <view
          class="contact-item"
          v-for="(item, index) in nodeList"
          :key="index"
          :data-column-value="item.wfStepId"
          :data-column-text="item.wfStepName"
          data-ref="popup"
          @tap="singleColumn"
        >
          <text class="contact-item-text">{{ item.wfStepName }}</text>
          <view class="contact-item-icon">
            <uni-icons
              v-if="node['text'] === item.wfStepName"
              type="checkmarkempty"
              color="#005BAC"
              size="44"
            />
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import uniPopup from '@/components/uni-popup/uni-popup.vue';
export default {
  components: {
    uniPopup
  },
  data() {
    return {
      pagePramas: {},

      workflowInfo: {},
      formInfo: {},
      node: {
        text: '',
        value: ''
      },
      nodeList: [],
      handleMarkedWords: '',
      switchChecked: false,
      isSubmit: false
    };
  },
  onLoad(opt) {
    this.pagePramas = opt;
    this.workflowInfo = uni.getStorageSync('workflow_info');
    this.formInfo = uni.getStorageSync('form_info');
    this.$nextTick(() => {
      this.getReturnNode();
    });
  },
  onUnload() {
    uni.removeStorageSync('form_info');
  },
  methods: {
    //获取退回节点
    getReturnNode() {
      let _self = this;
      _self.ajax
        .getReturnNode({
          taskId: _self.workflowInfo.taskId,
          wfInstanceId: _self.workflowInfo.wfInstanceId
        })
        .then(res => {
          _self.nodeList = res.object;
          if (res.object.length === 1) {
            _self.node = {
              value: res.object[0].wfStepId,
              text: res.object[0].wfStepName
            };
          }
        });
    },
    //显示弹出层
    showPopup() {
      this.$nextTick(() => {
        this.$refs['popup'].open();
      });
    },
    //单选
    singleColumn(e) {
      let data = e.currentTarget.dataset;
      this.node = {
        value: data.columnValue,
        text: data.columnText
      };
      this.$nextTick(() => {
        this.$refs['popup'].close();
      });
    },
    //点击确认退回按钮
    approvalSubmit() {
      let _self = this;
      if (_self.isSubmit) {
        return false;
      }
      _self.isSubmit = true;
      if (!_self.node['value']) {
        uni.showToast({ title: '请选择退回节点', icon: 'none' });
        _self.isSubmit = false;
      } else if (!_self.handleMarkedWords) {
        uni.showToast({ title: '请说明退回原因', icon: 'none' });
        _self.isSubmit = false;
      } else {
        let ajaxPath = '',
          data = {};
        if (
          _self.pagePramas &&
          _self.pagePramas.fromPage === 'approvalInformationReturn'
        ) {
          ajaxPath = '/ts-information/information/completeTask';
          data = {
            handleMarkedWords: _self.handleMarkedWords,
            remark: _self.handleMarkedWords,
            wfStepId: _self.node['value'],
            wfStepName: _self.node['text']
          };
        } else {
          ajaxPath = '/ts-form/form/api/travelReject';
          data = {
            handleMarkedWords: _self.handleMarkedWords,
            wfStepId: _self.node['value'],
            wfStepName: _self.node['text']
          };
        }
        _self.formInfo = {
          ..._self.formInfo,
          ...data
        };
        _self.submitApply(ajaxPath);
      }
    },
    //提交申请到服务器
    submitApply(ajaxPath) {
      let _self = this;
      _self.ajax
        .saveApply(ajaxPath, _self.formInfo)
        .then(res => {
          uni.setStorageSync(
            'info',
            JSON.stringify({
              title: _self.workflowInfo.workflowTitle,
              opinion: _self.handleMarkedWords,
              type: 'return'
            })
          );
          uni.removeStorageSync('workflow_info');
          _self.$nextTick(() => {
            uni.redirectTo({
              url: `/pages/workflow/operation/approval-succeeded?formListPage=${this.pagePramas.formListPage}`
            });
          });
        })
        .catch(e => {
          _self.isSubmit = false;
        });
    },
    //返回上一层
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .content_wrap {
    flex: 1;
    overflow: hidden;
    .approval_info {
      flex: 1;
      .dis_flex {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
      }
      .approval_row {
        position: relative;
        background-color: #ffffff;
        &::after {
          position: absolute;
          content: '';
          bottom: 0;
          left: 30rpx;
          right: 0;
          transform: scaleY(-0.5);
          height: 1px;
          background-color: #eee;
        }
        &:last-child {
          &::after {
            height: 0;
          }
        }
        .row_title {
          color: #333;
          margin: 30rpx 30rpx 20rpx;
        }
        .row_lable {
          width: 240rpx;
          padding: 22rpx 30rpx;
          box-sizing: border-box;
          position: relative;
          .required_red {
            color: #f00;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 4rpx;
            font-size: 24rpx;
          }
          .add_icon {
            font-size: 40rpx;
            padding: 0 30rpx;
            line-height: 1;
            color: #005bac;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
          }
          & ~ .row_value {
            flex: 1;
            font-size: 32rpx;
            color: #666;
            padding: 22rpx 30rpx;
            padding-left: 0;
            box-sizing: border-box;
            text-align: right;
          }
          & ~ .row_value_input {
            display: flex;
            justify-content: center;
            align-items: center;
            .row_value_input_text {
              text-align: right;
              flex: 1;
              font-size: 32rpx;
            }
          }
          & ~ .row_value_textarea {
            width: 100%;
            padding-left: 30rpx;
            padding-top: 0;
            text-align: left;
            .row_value_textarea_text {
              width: 100%;
              min-height: 180rpx;
              font-size: 32rpx;
            }
            .textarea-placeholder {
              color: #bbb;
            }
          }
          & ~ .row_value_personal {
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-sizing: border-box;
            text-align: left;
            padding: 0 30rpx 22rpx;
            .personNameStr {
              flex: 1;
            }
          }
        }
      }
    }
  }
  .none-data {
    color: #bbb;
  }
  .bottom_btn {
    background-color: #ffffff;
    box-shadow: 0 1px 6px #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 22rpx 30rpx;
    box-sizing: border-box;
    z-index: 10;
    .btn-item {
      flex: 1;
      box-sizing: border-box;
      text-align: center;
      font-size: 32rpx;
      position: relative;
      border-radius: 10rpx;
    }
  }
  .scroll_list {
    max-height: 800rpx;
    overflow: auto;
  }
  .contact-list {
    background-color: #ffffff;
    width: 100%;
    height: 100%;
    overflow: auto;
    .contact-item {
      padding: 22rpx 30rpx;
      font-size: 28rpx;
      color: #333333;
      position: relative;
      display: flex;
      align-items: center;
      &::after {
        position: absolute;
        z-index: 10;
        right: 0;
        left: 30rpx;
        bottom: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #eeeeee;
      }
      &:last-child::after {
        height: 0;
      }
      .contact-item-text {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
      }
      .contact-item-icon {
        line-height: 1;
      }
    }
  }
}
</style>
