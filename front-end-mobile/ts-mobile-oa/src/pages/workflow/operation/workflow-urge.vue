<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="确认催办"></page-head>
    <view class="content_wrap">
      <view class="row dis_flex">
        <view class="row_lable">催办标题</view>
        <view class="row_value">
          <text class="row_value_text">{{ urgeTitle }}</text>
        </view>
      </view>
      <view class="row dis_flex">
        <view class="row_lable">未办理人</view>
        <view class="row_value">
          <text class="row_value_text">{{ urgeUser.assigneeName || '' }}</text>
        </view>
      </view>
      <view class="row">
        <view class="row_lable">
          <text class="required_red oa-icon oa-icon-asterisks"></text>
          催办说明
        </view>
        <view class="row_value row_value_textarea">
          <textarea
            class="row_value_textarea_text"
            v-model="reason"
            placeholder="请输入催办说明"
          />
        </view>
      </view>
    </view>
    <view class="bottom_btn">
      <button class="btn-item uni-bg-blue" @tap="urgeSubmit">提交</button>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';
export default {
  data() {
    return {
      workflowInfo: {},
      urgeTitle: '',
      reason: '',
      urgeUser: {},

      isSubmit: false
    };
  },
  computed: {
    ...mapState(['empcode', 'username'])
  },
  onLoad() {
    this.workflowInfo = uni.getStorageSync('workflow_info');
    this.urgeTitle = `催办：${this.username} ${this.workflowInfo.workflowName}等待您的办理`;
    this.getTaskHisListById();
  },
  methods: {
    //获取流程信息
    async getTaskHisListById() {
      await this.ajax
        .getTaskHisList({ wfInstId: this.workflowInfo.wfInstanceId })
        .then(res => {
          this.urgeUser = (res.rows && res.rows[0]) || {};
        });
    },
    //催办操作
    async urgeSubmit() {
      let _self = this;
      if (_self.isSubmit) {
        return false;
      }
      _self.isSubmit = true;
      if (!_self.reason) {
        uni.showToast({
          icon: 'none',
          title: '请输入催办说明'
        });
        _self.isSubmit = false;
        return false;
      }
      await _self.ajax
        .pressWorkflow({
          wfInstId: _self.workflowInfo.wfInstanceId,
          wfDefId: _self.workflowInfo.wfDefinitionId,
          userCodes: _self.urgeUser.assigneeNo,
          userNames: _self.urgeUser.assigneeName,
          title: _self.urgeTitle,
          content: _self.reason
        })
        .then(res => {
          uni.showModal({
            content: '催办操作成功',
            confirmText: '知道了',
            showCancel: false,
            confirmColor: '#005BAC',
            success: showResult => {
              if (showResult.confirm) {
                uni.removeStorageSync('workflow_info');
                uni.redirectTo({
                  url: '/pages/workflow/my-workflow-list'
                });
              }
            }
          });
        })
        .catch(e => {
          _self.isSubmit = false;
        });
    },
    //返回上一层
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .dis_flex {
    display: flex;
    justify-content: center;
  }
  .content_wrap {
    flex: 1;
    .row {
      width: 100%;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 30rpx;
        right: 0;
        transform: scaleY(-0.5);
        height: 1px;
        background-color: #eee;
      }
      &:last-child::after {
        height: 0;
      }
      .row_lable {
        padding: 22rpx 30rpx;
        position: relative;
        .required_red {
          color: #f00;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 4rpx;
          font-size: 24rpx;
        }
      }
      .row_value {
        flex: 1;
        font-size: 32rpx;
        color: #666;
        padding: 22rpx 30rpx;
        padding-left: 0;
        box-sizing: border-box;
        text-align: right;
      }
      .row_value_textarea {
        width: 100%;
        padding-left: 30rpx;
        padding-top: 0;
        text-align: left;
        .row_value_textarea_text {
          width: 100%;
          min-height: 160rpx;
          font-size: 32rpx;
        }
      }
    }
  }
  .bottom_btn {
    background-color: #ffffff;
    box-shadow: 0 1px 6px #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 22rpx 30rpx;
    box-sizing: border-box;
    z-index: 10;
    .btn-item {
      flex: 1;
      box-sizing: border-box;
      text-align: center;
      font-size: 32rpx;
      position: relative;
      border-radius: 10rpx;
    }
  }
}
</style>
