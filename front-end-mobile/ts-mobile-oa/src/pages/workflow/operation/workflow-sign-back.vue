<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="加签退回"></page-head>
    <view class="content_wrap">
      <view class="content">
        <view class="approval_info">
          <view class="approval_row">
            <view class="row_lable">
              <text class="required_red oa-icon oa-icon-asterisks"></text>
              <text class="row_lable_text">退回说明</text>
            </view>
            <view class="row_value row_value_textarea">
              <textarea
                class="row_value_textarea_text"
                v-model="remark"
                placeholder="请说明退回原因"
              />
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="bottom_btn">
      <button class="btn-item uni-bg-blue" @tap="save">提交</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      pagePramas: {},

      workflowInfo: {},

      showContent: false,
      remark: '',

      isSubmit: false
    };
  },
  onLoad(opt) {
    this.pagePramas = opt;

    this.workflowInfo = uni.getStorageSync('workflow_info');
  },
  methods: {
    save() {
      let _self = this;
      if (_self.isSubmit) {
        return false;
      }
      _self.isSubmit = true;

      if (!_self.remark) {
        uni.showToast({ title: '请说明退回原因', icon: 'none' });
        _self.isSubmit = false;
        return false;
      }

      const data = {
        taskId: _self.workflowInfo.taskId,
        wfInstanceId: _self.workflowInfo.wfInstanceId,
        remark: _self.remark
      };

      _self.ajax
        .addSignatureReturn(data)
        .then(res => {
          uni.removeStorageSync('workflow_info');
          uni.setStorageSync(
            'info',
            JSON.stringify({
              title: _self.workflowInfo.workflowTitle,
              opinion: _self.remark,
              type: 'return'
            })
          );
          _self.$nextTick(() => {
            uni.redirectTo({
              url: `/pages/workflow/operation/approval-succeeded?formListPage=${this.pagePramas.formListPage}`
            });
          });
        })
        .catch(e => {
          _self.isSubmit = false;
        });
    },
    //返回上一层
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack({
          delta: 1
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .content_wrap {
    flex: 1;
    overflow: hidden;
    .content {
      height: 100%;
      overflow: auto;
      .approval_info {
        flex: 1;
        .dis_flex {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
        }
        .multi-next-node-item {
          margin: 20rpx 0;
          /deep/ uni-checkbox .uni-checkbox-input.uni-checkbox-input-disabled {
            background-color: unset;
            &:before {
              color: unset;
            }
          }
        }
        .single-next-node-item {
          margin: 20rpx 0;
          /deep/ uni-radio .uni-radio-input {
            width: 18px;
            height: 18px;
          }
          .nextStepRadio {
            display: inline-block;
            margin: 10rpx 0;
          }
        }
        .approval_row {
          position: relative;
          background-color: #ffffff;
          &::after {
            position: absolute;
            content: '';
            bottom: 0;
            left: 30rpx;
            right: 0;
            transform: scaleY(-0.5);
            height: 1px;
            background-color: #eee;
          }
          &:last-child {
            &::after {
              height: 0;
            }
          }
          .row_title {
            color: #333;
            margin: 30rpx 30rpx 20rpx;
          }
          .row_lable {
            width: 240rpx;
            padding: 22rpx 30rpx;
            box-sizing: border-box;
            position: relative;
            font-size: 28rpx;
            .required_red {
              color: #f00;
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              left: 4rpx;
              font-size: 24rpx;
            }
            .add_icon {
              font-size: 40rpx;
              padding: 0 30rpx;
              line-height: 1;
              color: #005bac;
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
            }
            .file_add_icon {
              padding: 0 30rpx;
              font-size: 56rpx;
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
              color: #bbb;
            }
            .common_words {
              padding: 0 30rpx;
              font-size: 28rpx;
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
              color: #005bac;
            }
            & ~ .row_value {
              flex: 1;
              font-size: 28rpx;
              color: #666;
              padding: 22rpx 30rpx;
              padding-left: 0;
              box-sizing: border-box;
              text-align: right;
            }
            & ~ .row_value_input {
              display: flex;
              justify-content: center;
              align-items: center;
              .row_value_input_text {
                text-align: right;
                flex: 1;
                font-size: 28rpx;
              }
            }
            & ~ .row_value_textarea {
              width: 100%;
              padding-left: 30rpx;
              padding-top: 0;
              text-align: left;
              .row_value_textarea_text {
                width: 100%;
                min-height: 160rpx;
                font-size: 28rpx;
              }
              .textarea-placeholder {
                color: #bbb;
              }
            }
            & ~ .row_value_personal {
              display: flex;
              justify-content: space-between;
              align-items: center;
              box-sizing: border-box;
              text-align: left;
              padding: 0 30rpx 22rpx;
              .personNameStr {
                flex: 1;
              }
            }
          }
        }
      }
    }
  }
  .bottom_btn {
    background-color: #ffffff;
    box-shadow: 0 1px 6px #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 22rpx 30rpx;
    box-sizing: border-box;
    z-index: 10;
    .btn-item {
      flex: 1;
      box-sizing: border-box;
      text-align: center;
      font-size: 32rpx;
      position: relative;
      border-radius: 10rpx;
    }
  }
}
</style>
