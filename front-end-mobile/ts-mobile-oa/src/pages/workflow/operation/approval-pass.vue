<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" :title="formTitle"></page-head>
    <view class="content_wrap" v-if="showContent">
      <view class="content">
        <view class="approval_info">
          <form-input
            v-if="writeFormTemplate.length"
            ref="writeForm"
            class="approval-write-form"
            :formTemplate="writeFormTemplate"
            :formDatas="writeFormDatas"
            @afterCheck="writeFormAfterCheck"
          ></form-input>
          <view
            class="single-next-node-item"
            v-if="nodetype == 'radio' && showNextNode"
          >
            <view class="approval_row dis_flex">
              <view class="row_lable">
                <text class="required_red oa-icon oa-icon-asterisks"></text>
                <text class="row_lable_text">下步节点</text>
              </view>
              <view class="row_value row_value_input">
                <radio-group @change="nextStepRadioChange">
                  <label
                    class="nextStepRadio"
                    v-for="item in nextNodeList"
                    :key="item.wfStepNo"
                  >
                    <radio
                      :value="item.wfStepNo"
                      :checked="item.wfStepNo === singleSelectedNode.wfStepNo"
                    />{{ item.wfStepName }}</label
                  >
                </radio-group>
              </view>
            </view>
            <view class="approval_row dis_flex" v-if="!isEnd">
              <view class="row_lable">
                <text class="row_lable_text">办理方式</text>
              </view>
              <view class="row_value row_value_input">
                <view
                  style="font-size: 28rpx; color: #333;"
                  v-html="singleSelectedNode.explainText"
                ></view>
              </view>
            </view>
            <view class="approval_row" v-if="!isEnd">
              <view class="row_lable" style="width: 100%;">
                <text class="required_red oa-icon oa-icon-asterisks"></text>
                <text class="row_lable_text">办理人</text>
                <text
                  class="add_icon oa-icon oa-icon-tianjiachaosong"
                  @tap="chooseSingleNodeUsers"
                ></text>
              </view>
              <view class="row_value row_value_personal">
                <text class="personNameStr" @tap="chooseSingleNodeUsers">
                  {{ singleSelectedNode.selectedUserList | personFilter }}
                </text>
                <uni-icons
                  v-if="singleSelectedNode.selectedUserList.length > 0"
                  :size="40"
                  class="uni-icon-wrapper"
                  color="#bbb"
                  type="closeempty"
                  @tap="emptySingleNodeUsers"
                />
              </view>
            </view>
          </view>
          <view
            class="multi-next-node-box"
            v-if="nodetype == 'checkbox' && showNextNode"
          >
            <checkbox-group>
              <view
                class="multi-next-node-item"
                v-for="(nodeItem, index) in nextNodeList"
                :key="index"
              >
                <view class="approval_row dis_flex">
                  <view class="row_lable">
                    <text class="required_red oa-icon oa-icon-asterisks"></text>
                    <text class="row_lable_text">下步节点</text>
                  </view>
                  <view class="row_value row_value_input">
                    <checkbox
                      :value="nodeItem.wfStepNo"
                      :checked="true"
                      :disabled="true"
                    />
                    {{ nodeItem.wfStepName }}
                  </view>
                </view>
                <view
                  class="approval_row dis_flex"
                  v-if="nodeItem['text'] != '结束'"
                >
                  <view class="row_lable">
                    <text class="row_lable_text">办理方式</text>
                  </view>
                  <view class="row_value row_value_input">
                    <view
                      style="font-size: 28rpx; color: #333;"
                      v-html="
                        handleMultiInstanceExplainText(
                          nodeItem.multiInstanceType
                        )
                      "
                    ></view>
                  </view>
                </view>
                <view class="approval_row" v-if="nodeItem['text'] != '结束'">
                  <view class="row_lable" style="width: 100%;">
                    <!-- <text class="required_red oa-icon oa-icon-asterisks"></text> -->
                    <text class="row_lable_text">办理人</text>
                    <text
                      class="add_icon oa-icon oa-icon-tianjiachaosong"
                      @tap="chooseMultiNodeUsers(index)"
                    ></text>
                  </view>
                  <view class="row_value row_value_personal">
                    <text
                      class="personNameStr"
                      @tap="chooseMultiNodeUsers(index)"
                      >{{ nodeItem.selectedUserList | personFilter }}</text
                    >
                    <uni-icons
                      v-if="nodeItem.selectedUserList.length > 0"
                      :size="40"
                      class="uni-icon-wrapper"
                      color="#bbb"
                      type="closeempty"
                      @tap="emptyMultiNodeUsers(index)"
                    />
                  </view>
                </view>
              </view>
            </checkbox-group>
          </view>
          <view style="margin-bottom: 20rpx" v-if="isHandwrittenSignature">
            <view class="approval_row">
              <view class="row_lable" style="width: 100%;">
                <text class="required_red oa-icon oa-icon-asterisks"></text>
                <text class="row_lable_text">手写签字</text>
                <text
                  class="add_icon oa-icon oa-icon-bianji2"
                  @tap="showHandwrittenSignature = true"
                ></text>
              </view>
              <view
                class="row_value row_value_signature"
                v-if="formInfo.signatureImg"
              >
                <image
                  class="signature-img"
                  mode="scaleToFill"
                  :src="$config.BASE_HOST + formInfo.signatureImg"
                  style="width: 300rpx; height: 120rpx"
                ></image>
              </view>
            </view>
          </view>
          <view v-if="!isEnd && showNextNode">
            <view class="approval_row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">办理期限</text>
              </view>
              <view class="row_value row_value_input" @tap="showPicker">
                <input
                  class="row_value_input_text"
                  :disabled="true"
                  type="text"
                  :value="handleAllottedTime"
                  placeholder="请选择办理期限"
                />
                <uni-icons
                  :size="30"
                  class="uni-icon-wrapper"
                  color="#bbb"
                  type="arrowright"
                />
              </view>
            </view>
            <view class="approval_row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">紧急程度</text>
              </view>
              <view class="row_value row_value_input" @tap="showPopup">
                <input
                  class="row_value_input_text"
                  :disabled="true"
                  placeholder="请选择紧急程度"
                  :value="urgency['text']"
                />
                <uni-icons
                  :size="30"
                  class="uni-icon-wrapper"
                  color="#bbb"
                  type="arrowright"
                />
              </view>
            </view>
            <view class="approval_row">
              <view class="row_lable">
                <text class="row_lable_text">办理提示</text>
              </view>
              <view class="row_value row_value_textarea">
                <textarea
                  class="row_value_textarea_text"
                  v-model="handleMarkedWords"
                  placeholder="请输入提示内容"
                />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="bottom_btn">
      <button class="btn-item uni-bg-blue" @tap="approvalSubmit">提交</button>
    </view>
    <date-picker
      ref="datePicker"
      endDate="2100-12-31 23:59"
      mode="date"
      fields="day"
      :value="handleAllottedTime"
      @confirm="onConfirmPicker"
    ></date-picker>
    <data-popup-select
      ref="popupSelect"
      :value="urgency.vulue"
      :selectList="urgencyList"
      fontSize="14"
      @change="popupSelectOnChange"
    ></data-popup-select>
    <handwritten-signature
      v-model="showHandwrittenSignature"
      @ok="changeHandwrittenSignature"
    ></handwritten-signature>
  </view>
</template>

<script>
import { urgencyList, multiInstanceType } from '../mixins/dictionary.js';
import { handleMultiInstanceExplainText } from '../mixins/workflowFliter.js';

import DatePicker from '@/components/picker/date-picker.vue';
import FormInput from '@/components/form-components/form-input.vue';
import HandwrittenSignature from '../components/handwritten-signature.vue';
export default {
  components: {
    FormInput,
    DatePicker,
    HandwrittenSignature
  },
  data() {
    return {
      pagePramas: {},

      urgencyList,
      multiInstanceType,
      handleMultiInstanceExplainText,

      showContent: false,
      formTitle: '流程审核',
      isAddSignature: '',
      fromPage: '', //上一页类型 reinitWorkflow：重新发起提交流程；approvalCustom：自定义表单审核；initAgainWorkflow：再次发起
      isHaveParallelTask: false,
      writeFormTemplate: [],
      writeFormDatas: {},
      workflowInfo: {},
      nextNodeList: [],
      formInfo: {},
      nodetype: 'radio',
      picker: {},
      taskFileList: [], //审批意见附件
      singleSelectedNode: {
        explainText: '',
        wfStepNo: '',
        wfStepName: '',
        isAllUser: '',
        userList: [],
        selectedUserList: []
      }, //单节点所选中的节点
      urgency: {
        value: '',
        text: ''
      },
      handleMarkedWords: '',
      handleAllottedTime: '',
      copyUserList: [],
      isHandwrittenSignature: false,
      showHandwrittenSignature: false,
      isSubmit: false
    };
  },
  computed: {
    showNextNode() {
      // 判断是否有并行任务未完成，有则无需选择下步节点；或者当前节点是否为加签节点，是加签节点也无需选择下步节点
      return !this.isHaveParallelTask && this.isAddSignature != 1;
    },
    isEnd() {
      return this.singleSelectedNode['wfStepName'] == '结束';
    }
  },
  async onLoad(opt) {
    this.pagePramas = opt;

    this.isAddSignature = opt.isAddSignature ? opt.isAddSignature : '';
    this.fromPage = opt.fromPage;
    this.isHaveParallelTask =
      opt.isHaveParallelTask && opt.isHaveParallelTask === 'true'
        ? true
        : false;
    this.workflowInfo = uni.getStorageSync('workflow_info');
    this.formInfo = uni.getStorageSync('form_info');
    this.isHandwrittenSignature = await this.getWorkflowConfiguration();
    this.writeFormTemplate = uni.getStorageSync('comment_field') || [];
    if (
      this.fromPage === 'approvalMeetingPass' ||
      this.fromPage === 'approvalMeetingReturn' ||
      this.fromPage === 'approvalInformationPass' ||
      this.fromPage === 'approvalInformationReturn'
    ) {
      this.formTitle =
        this.fromPage === 'approvalMeetingPass' ||
        this.fromPage === 'approvalInformationPass'
          ? '审核通过'
          : '审核不通过';
      let nodeParam = {
        taskId: this.formInfo.taskId,
        id: this.formInfo.id
      };
      this.getNodeInfoByTaskId(nodeParam);
    } else {
      this.formTitle =
        this.fromPage === 'reinitWorkflow' ? '确认提交' : '确认通过';
      let nodeParam = {
        ...this.formInfo.dataMap,
        taskId: this.formInfo.taskId,
        id: this.formInfo.id
      };
      this.getNodeInfoByTaskId(nodeParam);
    }

    this.handleAllottedTime = this.workflowInfo?.handleAllottedTime || '';
    this.urgency =
      this.urgencyList.find(f => f.value == this.workflowInfo?.urgencyLevel) ||
      this.urgencyList[0];
  },
  onUnload() {
    uni.removeStorageSync('comment_field');
    uni.removeStorageSync('form_info');
  },
  methods: {
    async getWorkflowConfiguration() {
      let workflowDefinition = 0;
      await this.ajax
        .getWorkflowDefinition(this.workflowInfo.workflowNo)
        .then(res => {
          workflowDefinition = res.object;
        });
      return workflowDefinition.exploitConfiguration.indexOf('5') !== -1;
    },
    //根据流程节点id获取节点信息
    getNodeInfoByTaskId(nodeParam) {
      this.ajax.getNextWfStepListByTaskId(nodeParam).then(res => {
        if (res.object.length) {
          let firstNode = res.object[0];
          this.nodetype = this.multiInstanceType[firstNode.multiInstanceType][
            'inputType'
          ];
          if (this.nodetype == 'radio') {
            this.singleSelectedNode = this.handleNodeInfo(firstNode);
            this.nextNodeList = res.object;
          } else {
            this.nextNodeList = res.object.map(item => {
              item.userList = this.handleUserList(item.userList);
              if (item.isAllUser == 'Y') {
                item.selectedUserList = [];
              } else if (item.userList.length == 1 || item.approverFill == 1) {
                item.selectedUserList = item.userList;
              } else {
                item.selectedUserList = [];
              }
              return item;
            });
          }
        }
        this.showContent = true;
      });
    },
    handleNodeInfo(node) {
      let userList = this.handleUserList(node.userList);
      let selectedUserList = [];
      if (userList.length === 1 || node.approverFill == 1) {
        selectedUserList = userList;
      }
      let nodeInfo = {
        explainText: handleMultiInstanceExplainText(node.multiInstanceType),
        wfStepNo: node.wfStepNo,
        wfStepName: node.wfStepName,
        isAllUser: node.isAllUser,
        userList,
        selectedUserList
      };
      return nodeInfo;
    },
    handleUserList(rows) {
      let list = [];
      rows.forEach(item => {
        list.push({
          id: item.usercode,
          name: item.username,
          empHeadImg: item.empHeadImg,
          empDeptName: item.deptname,
          empDutyName: item.dutyCode,
          sex: item.sex
        });
      });
      return list;
    },
    //显示时间弹出层
    showPicker() {
      this.$refs['datePicker'].show();
    },
    //时间选择确认
    onConfirmPicker(res) {
      this.handleAllottedTime = res.value;
    },
    //显示弹出层
    showPopup() {
      this.$refs['popupSelect'].open();
    },
    popupSelectOnChange(e) {
      this.urgency = e.detail.data;
    },
    //单节点切换
    nextStepRadioChange(e) {
      let node = this.nextNodeList.find(i => i.wfStepNo == e.detail.value);
      this.singleSelectedNode = this.handleNodeInfo(node);
    },
    //单节点选择办理人
    chooseSingleNodeUsers() {
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', res => {
        this.$set(this.singleSelectedNode, 'selectedUserList', res);
        uni.removeStorageSync('person_list');
        uni.removeStorageSync('approval_person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      this.jumpPageToSelectPerson(
        this.singleSelectedNode.selectedUserList,
        this.singleSelectedNode.userList
      );
    },
    //清空单节点办理人
    emptySingleNodeUsers() {
      this.singleSelectedNode.selectedUserList = [];
    },
    //多节点选择办理人
    chooseMultiNodeUsers(index) {
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', res => {
        this.$set(this.nextNodeList[index], 'selectedUserList', res);
        uni.removeStorageSync('person_list');
        uni.removeStorageSync('approval_person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      this.jumpPageToSelectPerson(
        this.nextNodeList[index].selectedUserList,
        this.nextNodeList[index].userList
      );
    },
    //清空多节点办理人
    emptyMultiNodeUsers(index) {
      this.$set(this.nextNodeList[index], 'selectedUserList', []);
    },
    jumpPageToSelectPerson(selectedUserList, userList) {
      uni.setStorageSync('person_list', JSON.stringify(selectedUserList));
      if (userList?.length) {
        uni.setStorageSync('approval_person_list', JSON.stringify(userList));
      }
      uni.navigateTo({
        url: `/pages/selectPerson/select-person?checkType=checkBox${
          userList?.length ? '&approval=true' : ''
        }`
      });
    },
    changeHandwrittenSignature(path) {
      this.formInfo.signatureImg = path;
    },
    //点击提交确定按钮
    approvalSubmit() {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;
      if (this.writeFormTemplate.length) {
        this.$refs['writeForm'].checkFrom();
      } else {
        this.validNextForm();
      }
    },
    writeFormAfterCheck(valid, itemData, commonData) {
      if (!valid) {
        return false;
      }
      this.formInfo = { ...this.formInfo, ...commonData };
      this.validNextForm();
    },
    validNextForm() {
      let nextStepParam = {};
      //没有下个节点，即不存在并行任务且不是加签任务时需要选择下部节点办理人
      if (this.showNextNode) {
        if (this.nodetype == 'radio') {
          //单节点处理下个节点和下个办理人
          if (
            !this.isEnd &&
            this.singleSelectedNode.selectedUserList.length == 0
          ) {
            uni.showToast({ title: '请选择办理人', icon: 'none' });
            this.isSubmit = false;
            return false;
          } else {
            let nextStepUser = this.getUserInfo(
              this.singleSelectedNode.selectedUserList
            );
            nextStepParam = {
              users: nextStepUser.codeList.join(','),
              names: nextStepUser.nameList.join(','),
              wfStepId: this.singleSelectedNode.wfStepNo
            };
          }
        } else if (this.nodetype == 'checkbox') {
          let selectedNextStepList = [];
          this.nextNodeList.forEach(item => {
            if (item.selectedUserList.length) {
              let nextStepUser = this.getUserInfo(item.selectedUserList);
              selectedNextStepList.push({
                users: nextStepUser.codeList.join(','),
                names: nextStepUser.nameList.join(','),
                wfStepId: item.wfStepNo,
                wfDefinitionId: this.formInfo.wfDefinitionId,
                workflowNo: this.formInfo.workflowNo,
                taskId: this.formInfo.taskId
              });
            }
          });
          if (selectedNextStepList.length == 0) {
            uni.showToast({ title: '请选择办理人', icon: 'none' });
            this.isSubmit = false;
            return false;
          } else {
            nextStepParam = {
              nextTaskManyStepList: selectedNextStepList
            };
          }
        }
        if (this.isHandwrittenSignature && !this.formInfo.signatureImg) {
          uni.showToast({ title: '请手写签名', icon: 'none' });
          this.isSubmit = false;
          return false;
        }
        nextStepParam = {
          ...nextStepParam,
          handleAllottedTime: this.handleAllottedTime,
          urgencyLevel: this.urgency.value,
          handleMarkedWords: this.handleMarkedWords
        };
      }
      this.validuccessCallBack(nextStepParam);
    },
    validuccessCallBack(nextStepParam) {
      this.formInfo = {
        ...this.formInfo,
        ...nextStepParam
      };
      let ajaxPath = '';
      if (
        this.fromPage === 'approvalMeetingPass' ||
        this.fromPage === 'approvalMeetingReturn'
      ) {
        this.formInfo.opinion = this.formInfo.remark;
        let api = {
          approvalMeetingPass: '/ts-oa/boardRoomApply/pass',
          approvalMeetingReturn: '/ts-oa/boardRoomApply/fail'
        };
        ajaxPath = api[this.fromPage];
      } else if (
        this.fromPage === 'approvalInformationPass' ||
        this.fromPage === 'approvalInformationReturn'
      ) {
        this.formInfo.informationComment = this.formInfo.remark;
        ajaxPath = '/ts-information/information/completeTask';
      } else {
        ajaxPath = '/ts-form/form/api/examination';
      }
      this.submitApproval(ajaxPath);
    },
    //分别获取办理人id数组和姓名数组
    getUserInfo(row) {
      let user = {
        codeList: [],
        nameList: []
      };
      row.forEach(item => {
        user.codeList.push(item.id);
        user.nameList.push(item.name);
      });
      return user;
    },
    //提交审核信息到服务器
    submitApproval(ajaxPath) {
      this.ajax
        .saveApply(ajaxPath, this.formInfo)
        .then(res => {
          uni.setStorageSync(
            'info',
            JSON.stringify({
              title: this.workflowInfo.workflowTitle,
              opinion: this.formInfo.remark,
              type: 'approval'
            })
          );
          uni.removeStorageSync('workflow_info');
          let pagePath = '';
          if (this.fromPage === 'reinitWorkflow') {
            pagePath = '/pages/workflow/my-workflow-list';
          } else {
            pagePath = `/pages/workflow/operation/approval-succeeded?formListPage=${this.pagePramas.formListPage}`;
          }
          this.$nextTick(() => {
            uni.redirectTo({
              url: pagePath
            });
          });
        })
        .catch(e => {
          this.isSubmit = false;
        });
    },
    //返回上一层
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .content_wrap {
    flex: 1;
    overflow: hidden;
    .content {
      height: 100%;
      overflow: auto;
      .approval_info {
        flex: 1;
        .dis_flex {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
        }
        .multi-next-node-item {
          margin: 20rpx 0;
          /deep/ uni-checkbox .uni-checkbox-input.uni-checkbox-input-disabled {
            background-color: unset;
            &:before {
              color: unset;
            }
          }
        }
        .single-next-node-item {
          margin: 20rpx 0;
          /deep/ uni-radio .uni-radio-input {
            width: 18px;
            height: 18px;
          }
          .nextStepRadio {
            display: inline-block;
            margin: 10rpx 0;
          }
        }
        .approval_row {
          position: relative;
          background-color: #ffffff;
          &::after {
            position: absolute;
            content: '';
            bottom: 0;
            left: 30rpx;
            right: 0;
            transform: scaleY(-0.5);
            height: 1px;
            background-color: #eee;
          }
          &:last-child {
            &::after {
              height: 0;
            }
          }
          .row_title {
            color: #333;
            margin: 30rpx 30rpx 20rpx;
          }
          .row_lable {
            width: 240rpx;
            padding: 22rpx 30rpx;
            box-sizing: border-box;
            position: relative;
            font-size: 28rpx;
            .required_red {
              color: #f00;
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              left: 4rpx;
              font-size: 24rpx;
            }
            .add_icon {
              font-size: 40rpx;
              padding: 0 30rpx;
              line-height: 1;
              color: #005bac;
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
            }
            .file_add_icon {
              padding: 0 30rpx;
              font-size: 56rpx;
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
              color: #bbb;
            }
            .common_words {
              padding: 0 30rpx;
              font-size: 28rpx;
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
              color: #005bac;
            }
            & ~ .row_value {
              flex: 1;
              font-size: 28rpx;
              color: #666;
              padding: 22rpx 30rpx;
              padding-left: 0;
              box-sizing: border-box;
              text-align: right;
            }
            & ~ .row_value_input {
              display: flex;
              justify-content: center;
              align-items: center;
              .row_value_input_text {
                text-align: right;
                flex: 1;
                font-size: 28rpx;
              }
            }
            & ~ .row_value_signature {
              width: 100%;
              padding: 0 16rpx 16rpx;
              text-align: center;
            }
            & ~ .row_value_textarea {
              width: 100%;
              padding-left: 30rpx;
              padding-top: 0;
              text-align: left;
              .row_value_textarea_text {
                width: 100%;
                min-height: 160rpx;
                font-size: 28rpx;
              }
              .textarea-placeholder {
                color: #bbb;
              }
            }
            & ~ .row_value_personal {
              display: flex;
              justify-content: space-between;
              align-items: center;
              box-sizing: border-box;
              text-align: left;
              padding: 0 30rpx 22rpx;
              .personNameStr {
                flex: 1;
              }
            }
          }
          .file_list {
            .file_item {
              text-decoration: none;
              font-size: 28rpx;
              color: #333333;
              margin: 10rpx 20rpx 20rpx;
              padding: 6rpx 20rpx;
              border: 1px solid #eeeeee;
              border-radius: 5px;
              margin-bottom: 20rpx;
              display: flex;
              align-items: center;
              .file_item_info {
                text-decoration: none;
                flex: 1;
                text-align: left;
                display: flex;
                align-items: center;
                .file_item_name {
                  flex: 1;
                  margin: 0 20rpx;
                }
              }
              .oa-icon {
                font-size: 40rpx;
                color: $theme-color;
              }
              .file_name {
                font-size: 28rpx;
                color: #333333;
              }
              .file_size {
                color: #999999;
                font-size: 24rpx;
                margin-left: 20rpx;
              }
            }
          }
        }
      }
    }
  }
  .bottom_btn {
    background-color: #ffffff;
    box-shadow: 0 1px 6px #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 22rpx 30rpx;
    box-sizing: border-box;
    z-index: 10;
    .btn-item {
      flex: 1;
      box-sizing: border-box;
      text-align: center;
      font-size: 32rpx;
      position: relative;
      border-radius: 10rpx;
    }
  }
  .scroll_list {
    max-height: 800rpx;
    overflow: auto;
  }
  .contact_list {
    background-color: #ffffff;
    width: 100%;
    height: 100%;
    overflow: auto;
    .contact_item {
      padding: 22rpx 30rpx;
      font-size: 28rpx;
      color: #333333;
      position: relative;
      display: flex;
      align-items: center;
      &::after {
        position: absolute;
        z-index: 10;
        right: 0;
        left: 30rpx;
        bottom: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #eeeeee;
      }
      &:last-child::after {
        height: 0;
      }
      .contact_item_text {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
      }
      .contact_item_icon {
        line-height: 1;
      }
    }
  }
}
</style>
