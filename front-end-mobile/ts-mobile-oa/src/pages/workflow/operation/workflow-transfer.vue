<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="转办"></page-head>
    <view class="form">
      <form @submit="formSubmit">
        <view class="row dis-flex">
          <view class="row-lable">
            <text class="required-red oa-icon oa-icon-asterisks"></text>
            <text class="row-lable_text">转办人</text>
          </view>
          <view class="row-value row-value_input" @tap="choosePerson">
            <input
              v-show="false"
              placeholder="请选择转办人"
              name="transferUserCode"
              v-model="transferForm.transferUserCode"
            />
            <view
              class="row-value_input_text"
              v-if="transferForm.transferUserName"
              v-text="transferForm.transferUserName"
            />
            <view class="row-value_input_text" v-else>请选择转办人</view>
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
            />
          </view>
        </view>
        <view class="row">
          <view class="row-lable">
            <text class="row-lable_text">备注</text>
          </view>
          <view class="row-value row-value_textarea">
            <textarea
              class="row-value_textarea_text"
              placeholder="请输入"
              auto-height
              name="remark"
              v-model="transferForm.remark"
            />
          </view>
        </view>
        <view class="bottom-btn">
          <button class="btn-item uni-bg-blue" form-type="submit">提交</button>
        </view>
      </form>
    </view>
  </view>
</template>

<script>
import graceChecker from '@/common/js/graceChecker.js';
export default {
  data() {
    return {
      pagePramas: {},

      workflowInfo: {},
      transferForm: {
        transferUserCode: '',
        transferUserName: '',
        remark: ''
      },
      personlist: [],
      rule: [
        {
          filedKey: 'transferUserCode',
          required: true,
          checkType: 'notnull',
          errorMsg: '请选择转办人'
        }
      ],

      isSubmit: false
    };
  },
  onLoad(opt) {
    this.pagePramas = opt;

    this.workflowInfo = uni.getStorageSync('workflow_info');
  },
  methods: {
    //选择转办人
    choosePerson() {
      let _self = this;
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        _self.personlist = data;
        _self.transferForm.transferUserCode = data[0] ? data[0].id : '';
        _self.transferForm.transferUserName = data[0] ? data[0].name : '';
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      uni.setStorageSync('person_list', JSON.stringify(_self.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=radio'
      });
    },
    //清空转办人
    emptyTransferUser() {
      this.personlist = [];
      this.transferForm.transferUserCode = '';
      this.transferForm.transferUserName = '';
    },
    formSubmit(e) {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;

      //进行表单检查
      let checkRes = graceChecker.check(e.detail.value, this.rule);
      if (checkRes) {
        const data = {
          ...this.transferForm,
          taskId: this.workflowInfo.taskId
        };
        this.ajax.handleTransfer(data).then(res => {
          uni.showToast({
            title: '转办成功',
            icon: 'none',
            complete: () => {
              setTimeout(() => {
                uni.removeStorageSync('workflow_info');
                uni.reLaunch({
                  url: this.getJumpPage()
                });
              }, 1500);
            }
          });
        });
      } else {
        this.isSubmit = false;
        uni.showToast({ title: graceChecker.error, icon: 'none' });
      }
    },
    getJumpPage() {
      let pagePath = '';
      switch (this.pagePramas.formListPage) {
        case 'unreadList':
          pagePath = '/pages/index/work-unread-list';
          break;
        default:
          pagePath = `/pages/workflow/workflow-approval-list?index=0`;
      }
      return pagePath;
    },
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .form {
    position: relative;
    &::before {
      position: absolute;
      content: '';
      top: 0;
      left: 0;
      right: 0;
      transform: scaleY(-0.5);
      height: 1px;
      background-color: #eee;
      z-index: 10;
    }
    .dis-flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .row {
      width: 100%;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 30rpx;
        right: 0;
        transform: scaleY(-0.5);
        height: 1px;
        background-color: #eee;
      }
      &:last-child::after {
        height: 0;
      }
      .row-lable {
        width: 240rpx;
        font-size: 32rpx;
        color: #333;
        padding: 22rpx 30rpx;
        box-sizing: border-box;
        position: relative;
        .row-lable_text {
          padding-right: 20rpx;
          box-sizing: border-box;
        }
        .required-red {
          color: #f00;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 4rpx;
          font-size: 24rpx;
        }
      }
      .row-lable ~ .row-value {
        flex: 1;
        font-size: 32rpx;
        color: #666;
        padding: 22rpx 30rpx;
        padding-left: 0;
        box-sizing: border-box;
        text-align: right;

        .uni-icon-wrapper {
          transform: translate(0, -4rpx);
        }
      }
      .row-lable ~ .row-value_input {
        display: flex;
        justify-content: center;
        align-items: center;
        .row-value_input_text {
          text-align: right;
          flex: 1;
          font-size: 32rpx;
        }
      }
      .row-lable ~ .row-value_textarea {
        width: 100%;
        padding-left: 30rpx;
        padding-top: 0;
        text-align: left;
        .row-value_textarea_text {
          width: 100%;
          min-height: 180rpx;
          font-size: 32rpx;
        }
        .textarea-placeholder {
          color: #bbb;
        }
      }
    }
    .bottom-btn {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #ffffff;
      box-shadow: 0 1px 6px #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 22rpx 30rpx;
      box-sizing: border-box;
      z-index: 10;
      .btn-item {
        flex: 1;
        box-sizing: border-box;
        text-align: center;
        font-size: 32rpx;
        position: relative;
        border-radius: 10rpx;
      }
    }
  }
}
</style>
