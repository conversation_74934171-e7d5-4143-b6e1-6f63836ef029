<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="确认结束"></page-head>
    <view class="content_wrap">
      <view class="row">
        <view class="row_lable">
          <text class="required_red oa-icon oa-icon-asterisks"></text>
          结束原因
        </view>
        <view class="row_value row_value_textarea">
          <textarea
            class="row_value_textarea_text"
            v-model="reason"
            placeholder="请输入结束原因"
          />
        </view>
      </view>
    </view>
    <view class="bottom_btn">
      <button class="btn-item uni-bg-blue" @tap="forcedEndSubmit">
        提交
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      workflowInfo: {},
      reason: '',

      isSubmit: false
    };
  },
  onLoad() {
    this.workflowInfo = uni.getStorageSync('workflow_info');
  },
  methods: {
    //强制结束操作
    async forcedEndSubmit() {
      let _self = this;
      if (_self.isSubmit) {
        return false;
      }
      _self.isSubmit = true;

      if (!_self.reason) {
        uni.showToast({ title: '请输入结束原因', icon: 'none' });
        _self.isSubmit = false;
        return false;
      }

      await _self.ajax
        .forcedEndWorkflow({
          reason: _self.reason,
          workflowInstId: _self.workflowInfo.wfInstanceId
        })
        .then(res => {
          uni.showModal({
            content: '结束流程操作成功',
            confirmText: '知道了',
            showCancel: false,
            confirmColor: '#005BAC',
            success: showResult => {
              if (showResult.confirm) {
                uni.removeStorageSync('workflow_info');
                uni.redirectTo({
                  url: '/pages/workflow/workflow-access-list'
                });
              }
            }
          });
        });
    },
    //返回上一层
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .content_wrap {
    flex: 1;
    .row {
      background-color: #fff;
      .row_lable {
        padding: 22rpx 30rpx;
        position: relative;
        .required_red {
          color: #f00;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 4rpx;
          font-size: 24rpx;
        }
      }
      .row_value {
        flex: 1;
        font-size: 32rpx;
        color: #666;
        padding: 22rpx 30rpx;
        padding-left: 0;
        box-sizing: border-box;
        text-align: right;
      }
      .row_value_textarea {
        width: 100%;
        padding-left: 30rpx;
        padding-top: 0;
        text-align: left;
        .row_value_textarea_text {
          width: 100%;
          min-height: 160rpx;
          font-size: 32rpx;
        }
      }
    }
  }
  .bottom_btn {
    background-color: #ffffff;
    box-shadow: 0 1px 6px #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 22rpx 30rpx;
    box-sizing: border-box;
    z-index: 10;
    .btn-item {
      flex: 1;
      box-sizing: border-box;
      text-align: center;
      font-size: 32rpx;
      position: relative;
      border-radius: 10rpx;
    }
  }
}
</style>
