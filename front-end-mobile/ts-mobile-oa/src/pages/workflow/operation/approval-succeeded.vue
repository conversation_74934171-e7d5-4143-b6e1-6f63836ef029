<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="提交成功"></page-head>
    <view class="img_tap">
      <image
        class="img"
        :src="
          info.type === 'approval'
            ? '../../static/img/success.png'
            : '../../static/img/return.png'
        "
      ></image>
      <text class="success_text" v-if="info.type === 'approval'"
        >提交成功！</text
      >
      <text class="return_text" v-else>退回成功！</text>
    </view>
    <view class="info_tap">
      <view class="info_icon">
        <text class="oa-icon oa-icon-shenpizhushou"></text>
      </view>
      <view class="info_content_tap">
        <view class="info_title"
          ><text>{{ info.title }}</text></view
        >
        <view class="info_content">
          <text>审批意见：{{ info.opinion }}</text>
        </view>
      </view>
    </view>
    <view class="tips_text">
      <text
        >您还剩<text class="tips_text_num">{{ totalNum }}</text> 条待办</text
      >
    </view>
    <view class="button_tap">
      <button class="button_item workbench_btn" @tap="goToWorkbench">
        前往工作台
      </button>
      <button class="button_item approval_over_btn" @tap="goToApprovalList(1)">
        查看已办
      </button>
      <button
        class="button_item approval_continue_btn"
        @tap="goToApprovalList(0)"
      >
        继续办理
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      pagePramas: {},

      totalNum: 0,
      info: {
        title: '',
        opinion: '',
        type: 'approval'
      }
    };
  },
  onLoad(opt) {
    this.pagePramas = opt;

    this.info = JSON.parse(uni.getStorageSync('info'));
    this.$nextTick(() => {
      this.getListData();
    });
  },
  onUnload() {
    uni.removeStorageSync('info');
  },
  methods: {
    //获取待办总数
    getListData() {
      this.ajax
        .getMyHandleWorkflowList({
          isMobile: true,
          handleStatus: 1,
          pageSize: 10,
          pageNo: 1,
          sidx: 'inst.CREATE_DATE',
          sord: 'desc'
        })
        .then(res => {
          this.totalNum = res.totalCount;
        })
        .catch(e => {
          errorCallback();
        });
    },
    goToWorkbench() {
      this.$nextTick(() => {
        this.$parentTypeFun({
          type: 'jumpPage',
          path: '/workbench'
        });
      });
    },
    goToApprovalList(tabIndex) {
      this.$nextTick(() => {
        uni.reLaunch({
          url: `/pages/workflow/workflow-approval-list?index=${tabIndex}`
        });
      });
    },
    getJumpPage() {
      let pagePath = '';
      switch (this.pagePramas.formListPage) {
        case 'unreadList':
          pagePath = '/pages/index/work-unread-list';
          break;
        default:
          pagePath = `/pages/workflow/workflow-approval-list`;
      }
      return pagePath;
    },
    returnBack() {
      this.$nextTick(() => {
        uni.reLaunch({
          url: `${this.getJumpPage()}`
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.img_tap {
  width: 100%;
  height: 300rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .img {
    width: 120rpx;
    height: 120rpx;
  }
  .return_text,
  .success_text {
    font-size: 36rpx;
    margin-top: 20rpx;
    color: #3aad73;
    font-weight: bold;
  }
  .return_text {
    color: #f59a23;
  }
}
.info_tap {
  margin: 0 30rpx;
  background-color: #fff;
  padding: 22rpx 30rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  .info_icon {
    margin-right: 20rpx;
    .oa-icon {
      font-size: 80rpx;
      color: #005bac;
    }
  }
  .info_content_tap {
    flex: 1;
    .info_title {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
    }
    .info_content {
      font-size: 28rpx;
      color: #666;
    }
  }
}
.tips_text {
  margin: 20rpx 0;
  font-size: 32rpx;
  color: #333;
  text-align: center;
  .tips_text_num {
    color: #005bac;
    font-weight: bold;
    padding: 0 10rpx;
  }
}
.button_tap {
  text-align: center;
  .button_item {
    font-size: 28rpx;
    padding: 10rpx 20rpx;
    display: inline-block;
    line-height: unset;
    margin: 0 14rpx;
    color: #fff;
  }
  .workbench_btn {
    background-color: #3aad73;
    border-color: #3aad73;
  }
  .approval_over_btn {
    background-color: #999;
    border-color: #999;
  }
  .approval_continue_btn {
    background-color: #005bac;
    border-color: #005bac;
  }
}
</style>
