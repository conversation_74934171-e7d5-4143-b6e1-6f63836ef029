<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" :title="formTitle"></page-head>
    <view class="content" v-if="showContent">
      <view class="form_wrap">
        <form-input
          ref="form"
          :isStartWorkflow="true"
          :formTemplate="formTemplate"
          :formId="formId"
          :formDatas="formDatas"
          :fileOpt="fileOpt"
          @afterCheck="afterCheck"
        ></form-input>
      </view>
      <view class="bottom_btn">
        <button class="btn_item uni-bg-blue" @tap="formSubmit">提交</button>
      </view>
    </view>
  </view>
</template>

<script>
import formInput from '@/components/form-components/form-input.vue';
import formAutoCalculat from '@/common/js/formAutoCalculat.js';

export default {
  components: {
    formInput
  },
  data() {
    return {
      showContent: false,
      formTitle: '',
      formDatas: {},
      formTemplate: [],
      formId: '',
      workflowInfo: {},
      tableName: '',
      nodeCondition: {},
      approvalPersonCode: '',
      approvalPersonName: '',
      fileOpt: {},
      isSubmit: false
    };
  },
  onLoad() {
    this.workflowInfo = uni.getStorageSync('workflow_info');
    this.formTitle = this.workflowInfo.workflowName;
    this.getFormDatas();
  },
  methods: {
    //获取表单数据
    async getFormDatas() {
      let _self = this;
      await _self.ajax
        .getFormDatas({
          id: _self.workflowInfo.businessId,
          wfDefinitionId: _self.workflowInfo.wfDefinitionId
        })
        .then(res => {
          _self.formDatas = JSON.parse(JSON.stringify(res['object'][0]));
          _self.getFormFiledInfo();
        });
    },
    //获取表单字段读写
    async getFormFiledInfo() {
      let _self = this;
      await _self.ajax
        .getFiledPermissions({
          wfDefinitionId: _self.workflowInfo.wfDefinitionId,
          wfStepId: _self.workflowInfo.currentStepNo
        })
        .then(async res => {
          await _self.getFormTemplate(res.object);
        });
    },
    //获取表单设计模板
    async getFormTemplate(row) {
      let _self = this;
      await _self.ajax
        .getFormTemplate(_self.workflowInfo.wfDefinitionId)
        .then(async res => {
          let dataList = res.object.toaFieldSetList,
            dataSort = JSON.parse(res.object.formTemplate),
            dataSortList = [];
          dataSort.forEach(item => {
            for (let i = 0; i < dataList.length; i++) {
              if (item == dataList[i].fieldName) {
                dataSortList.push(dataList[i]);
                // _self.formTemplate.push(dataList[i]);
                continue;
              }
            }
          });
          let childFormTempList = dataSortList.filter(
            item => item.fieldType == 'childForm'
          );
          if (childFormTempList.length) {
            let apiList = [],
              fileChildFormIndexList = [];
            childFormTempList.map((temp, index) => {
              apiList.push(
                // 获取 子表单详情
                _self.ajax.getChildFormDetailById(temp.tableId).then(res => {
                  if (res.success) {
                    let dataIndex = dataSortList.findIndex(
                      item => item.fieldName == temp.fieldName
                    );
                    dataSortList[dataIndex].childFormDetail = res.object;
                    dataSortList[dataIndex].childFormColumns = res.object.fields
                      .filter(item => item.pcShow == 1)
                      .map(item => {
                        let newItem = {
                          ...item,
                          title: item.remark,
                          key: item.fieldName,
                          fieldType: item.fieldType,
                          render: true,
                          optionList: (item.optionValue || '')
                            .replaceAll(/[,，;；]/g, ',')
                            .split(',')
                            .filter(item => item)
                            .map(item => ({ text: item, value: item }))
                        };
                        newItem.fieldType == 'EXPRESSION' &&
                          (newItem.formula = formAutoCalculat.computedFormula(
                            item.defaultValue
                          ));

                        item.fieldType == 'FILE' &&
                          fileChildFormIndexList.push({
                            dataIndex,
                            formKey: temp.fieldName
                          });
                        return newItem;
                      });
                  }
                }),
                // 获取子表单数据
                _self.ajax
                  .getChildFormDataById({
                    tableId: temp.tableId,
                    fieldName: temp.fieldName,
                    businessId: _self.workflowInfo.businessId
                  })
                  .then(res => {
                    if (res.success) {
                      let valueList = JSON.parse(JSON.stringify(res.object));
                      _self.$set(_self.formDatas, temp.fieldName, valueList);
                    }
                  })
              );
            });
            await Promise.all(apiList);
            apiList = [];

            // 获取附件数据
            fileChildFormIndexList.map(({ dataIndex, formKey }) => {
              let dataList = _self.formDatas[formKey] || [],
                fileItem =
                  dataSortList[dataIndex].childFormColumns.find(
                    item => item.fieldType == 'FILE'
                  ) || {};

              dataList.map(data => {
                apiList.push(
                  _self.ajax
                    .getFileAttachmentByBusinessId({
                      businessId: data[fileItem.key]
                    })
                    .then(res => {
                      if (!res.success) {
                        return;
                      }
                      let fileList = res.object.map(file => {
                        let { id, realPath, fileExtension } = file,
                          filePath = ` ${_self.$config.DOCUMENT_BASE_HOST}${realPath}?fullfilename=${id}.${fileExtension}&source=mobile`,
                          href = `/pages/webview/webview?url=${
                            _self.$config.BASE_HOST
                          }/ts-preview/onlinePreview?url=${Base64.encode(
                            filePath
                          )}`;

                        return `<view class="child-form-file-item" data-href="${href}" >
                          <text class="oa-icon oa-icon-${_self.$oaModule.formatFileType(
                            file.fileExtension
                          )}"></text>
                          <text>${file.originalName}</text>
                        </view>`;
                      });
                      fileItem.format = function(row, prop) {
                        return fileList.join('');
                      };
                      fileItem.event = {
                        click: function(event, rowData, prop) {
                          let target = event.target;
                          if (
                            !target.classList.contains('child-form-file-item')
                          ) {
                            target = target.parentNode;
                            if (
                              !target.classList.contains('child-form-file-item')
                            ) {
                              return;
                            }
                          }

                          let url = target.getAttribute('data-href');
                          url &&
                            uni.navigateTo({
                              url
                            });
                        }
                      };
                    })
                );
              });
            });

            await Promise.all(apiList);
            let replaceValue = function(
              valueList,
              fieldType,
              fieldName,
              defaultValue
            ) {
              let dateReg = /^((\d{3}[1-9]|\d{2}[1-9]\d|\d[1-9]\d{2}|[1-9]\d{3})\-(((0[13578]|1[02])\-(0[1-9]|[12]\d|3[01]))|((0[469]|11)\-(0[1-9]|[12]\d|30))|(02\-(0[1-9]|[1]\d|2[0-8])))$)|(((\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))\-02\-29)$/,
                numReg = /^(([1-9]{1}\d*)|(0{1}))((\.\d{1,4}|\.))?$/;
              valueList.forEach(val => {
                let value = val[fieldName];
                if (fieldType == 'DATEPICKER' && !dateReg.test(value)) {
                  value = defaultValue || '';
                }
                if (fieldType == 'NUMBER' && !numReg.test(value)) {
                  value = defaultValue || '';
                }
                val[fieldName] = value;
                // return val
              });
            };
            childFormTempList.map(temp => {
              if (temp.isReadonly == 'Y') {
                return;
              }
              let dataIndex = dataSortList.findIndex(
                item => item.fieldName == temp.fieldName
              );
              dataSortList[dataIndex].childFormColumns.map(item => {
                let { fieldName, defaultValue, fieldType } = item;

                if (['DATEPICKER', 'NUMBER'].includes(fieldType)) {
                  replaceValue(
                    _self.formDatas[temp.fieldName],
                    fieldType,
                    fieldName,
                    defaultValue
                  );
                }
              });
            });
          }

          // hrpHyperlink不为地址 则渲染附件组件
          dataSortList.forEach(item => {
            if (item.fieldType === 'hrpHyperlink') {
              let hrpHyperlinkValue = _self.readFormDatas[item.fieldName];
              if (
                hrpHyperlinkValue &&
                hrpHyperlinkValue.indexOf('http:') === -1
              ) {
                item.fieldType = 'file';
              }
            }
          });

          let field = _self.fileFieldFilter(dataList);
          if (field.file.length > 0) {
            field.file.forEach(async item => {
              await _self.getFiles(item.fieldName);
            });
          }
          _self.$nextTick(async () => {
            _self.showContent = true;
            _self.formId = res.object.id;
            _self.formTemplate = dataList;
            _self.tableName = res.object.tableName;
            _self.nodeCondition = {
              taskId: _self.workflowInfo.taskId,
              id: res.object.id,
              wfDefId: _self.workflowInfo.wfDefinitionId
            };

            await setTimeout(() => {
              const scriptText = res.object.javascriptText;
              if (scriptText) {
                let fn = new Function(scriptText);
                fn.call(_self);
                fn = null;
                _self.$forceUpdate();
              }
            }, 1000);
          });
        });
    },
    //文件过滤
    fileFieldFilter(dataList) {
      let fileList = [];
      dataList.forEach(item => {
        if (item.fieldType === 'file' && item.isReadonly != 'Y') {
          fileList.push(item);
        }
      });
      let fieldList = {
        file: fileList
      };
      return fieldList;
    },
    //获取附件
    async getFiles(fieldName) {
      let _self = this;
      await _self.ajax
        .getFiles({
          idsStr: _self.formDatas[fieldName]
        })
        .then(res => {
          _self.$set(_self.formDatas, fieldName, res.object);
          _self.$set(_self.fileOpt, fieldName, res.object);
          _self.showContent = true;
        });
    },
    formSubmit() {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;
      this.$refs['form'].checkFrom();
    },
    afterCheck(valid, itemData, commentData) {
      if (!valid) {
        this.isSubmit = false;
        return false;
      }
      this.getNodeInfo(itemData, commentData);
    },
    //获取节点信息
    getNodeInfo(itemData, commentData) {
      let _self = this;
      uni.setStorageSync('comment_field', null);
      uni.setStorageSync('form_info', {
        ...commentData,
        ...{
          wfDefinitionId: _self.workflowInfo.wfDefinitionId,
          dataMap: { ..._self.formDatas, ...itemData },
          tableName: _self.tableName,
          workflowNo: _self.workflowInfo.workflowNo,
          taskId: _self.workflowInfo.taskId,
          templateId: _self.nodeCondition.id,
          id: _self.workflowInfo.businessId
        }
      });
      _self.ajax
        .getNextWfStepListByWfDefId({
          ..._self.nodeCondition,
          ...itemData,
          workflowStart: 'Y'
        })
        .then(res => {
          if (typeof res === 'string') {
            uni.showToast({
              icon: 'none',
              duration: 2500,
              title: res
            });
            return false;
          }
          let list = res.object;
          let isPromoterChoice = list.some(item => {
            return item.promoterChoice == 1;
          });
          let openPageUrl = '';
          if (isPromoterChoice) {
            list = list.filter(item => {
              return item.wfStepName != '结束';
            });
            openPageUrl = `/pages/workflow/operation/workflow-define?fromPage=reinitWorkflow`;
            uni.setStorageSync('node_list', list);
          } else {
            openPageUrl = `/pages/workflow/operation/approval-pass?fromPage=reinitWorkflow`;
          }
          _self.$nextTick(() => {
            _self.isSubmit = false;
            uni.navigateTo({
              url: openPageUrl
            });
          });
        });
    },
    //返回上一层
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  .content {
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    .form_wrap {
      flex: 1;
      overflow: hidden;
    }
    .bottom_btn {
      background-color: #ffffff;
      box-shadow: 0 1px 6px #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 22rpx 30rpx;
      box-sizing: border-box;
      z-index: 10;
      .btn_item {
        flex: 1;
        box-sizing: border-box;
        text-align: center;
        font-size: 32rpx;
        position: relative;
        border-radius: 10rpx;
      }
    }
  }
}
</style>
