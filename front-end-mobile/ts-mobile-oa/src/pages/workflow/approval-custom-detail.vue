<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" :title="formTitle"></page-head>
    <wf-tab-bar
      v-if="showContent"
      :tabBarList="tabBars"
      v-model="tabIndex"
      class="autoOver"
    />
    <view class="content-wrap" v-if="showContent">
      <scroll-view
        class="content"
        :class="{
          'is-ios': isIOS
        }"
        scroll-y="true"
        :scroll-into-view="scrollViewId"
        :scroll-with-animation="true"
        @scroll="scroll"
      >
        <view class="form-wrap scroll-view-item" id="form">
          <wf-head-info :workflowInfo="workflowInfo"></wf-head-info>
          <form-check
            ref="readForm"
            :formTemplate="readFormTemplate"
            :formDatas="readFormDatas"
          ></form-check>
        </view>
        <view
          class="form-wrap scroll-view-item write-form-wrap"
          id="writeForm"
          v-if="writeFormTemplate.length && workflowType == 'approvalToDo'"
        >
          <view class="form-title">填写/修改信息</view>
          <form-input
            v-if="writeFormTemplate.length"
            ref="writeForm"
            class="approval-write-form"
            :formTemplate="writeFormTemplate"
            :formDatas="writeFormDatas"
            :fileOpt="fileOpt"
            currentStepNo="examine"
            @afterCheck="writeFormAfterCheck"
          ></form-input>
        </view>
        <view class="form-wrap scroll-view-item" id="opinion">
          <view class="form-title">审批意见</view>
          <wf-comment-list
            v-if="optionList.length > 0"
            :commentList="optionList"
          ></wf-comment-list>
          <view class="nothing" v-else>
            <view class="img-content">
              <image
                class="nothing-img"
                src="../../static/img/nothing.png"
                mode="aspectFit"
              ></image>
            </view>
          </view>
        </view>
        <view class="form-wrap scroll-view-item" id="record">
          <view class="form-title">流程信息</view>
          <view class="task-history-wrap">
            <data-process-history
              :processHistoryList="taskHistoryList"
            ></data-process-history>
          </view>
        </view>
        <view
          class="form-wrap scroll-view-item"
          id="copyToUser"
          v-if="taskCopytoUserList.length"
        >
          <view class="form-title">抄送信息</view>
          <view class="task-history-wrap">
            <data-copyToUser-list
              :processHistoryList="taskCopytoUserList"
            ></data-copyToUser-list>
          </view>
        </view>
      </scroll-view>
      <view class="btn-wrap">
        <view
          class="bottom-btn"
          v-if="workflowType === 'approvalToDo' && isAddSignature == 1"
        >
          <button
            v-if="normalFlowButtonIds.includes('10')"
            class="btn-item"
            @tap="handleWorkflowCopy"
          >
            <text class="oa-icon oa-icon-tijiao1 theme-color"></text>
            抄送
          </button>
          <button class="btn-item" @tap="handleAddSignatureBack">
            <text class="oa-icon oa-icon-liuchengtuihui theme-color"></text>
            加签退回
          </button>
          <button class="btn-item" @tap="checkPermissions">
            <text class="oa-icon oa-icon-tijiaobanli theme-color"></text>
            审批提交
          </button>
        </view>
        <view
          class="bottom-btn"
          v-else-if="workflowType === 'approvalToDo' && isAddSignature != 1"
        >
          <button
            v-if="normalFlowButtonIds.includes('10')"
            class="btn-item"
            @tap="handleWorkflowCopy"
          >
            <text class="oa-icon oa-icon-tijiao1 theme-color"></text>
            抄送
          </button>
          <button
            v-if="normalFlowButtonIds.includes('11')"
            class="btn-item"
            @tap="handleTransfer"
          >
            <text class="oa-icon oa-icon-dakai theme-color"></text>
            转办
          </button>
          <button
            v-if="normalFlowButtonIds.includes('12')"
            class="btn-item "
            @tap="handleAddNodes"
          >
            <text class="oa-icon oa-icon-tianjiachaosong theme-color"></text>
            加签
          </button>
          <button
            v-if="normalFlowButtonIds.includes('13')"
            class="btn-item"
            @tap="handleReturn"
          >
            <text class="oa-icon oa-icon-liuchengtuihui grey-color"></text>
            退回
          </button>
          <button class="btn-item" @tap="checkPermissions">
            <text class="oa-icon oa-icon-tijiaobanli theme-color"></text>
            {{ flowIsMyself ? '提交' : '同意' }}
          </button>
        </view>
        <view class="bottom-btn" v-else-if="workflowType === 'approvalDone'">
          <button v-if="showSignBtn" class="btn-item" @tap="handleRevoke">
            <text class="oa-icon oa-icon-liuchengtuihui grey-color"></text>
            撤销
          </button>
          <button
            v-if="normalFlowButtonIds.includes('10')"
            class="btn-item"
            @tap="handleWorkflowCopy"
          >
            <text class="oa-icon oa-icon-tijiao1 theme-color"></text>
            抄送
          </button>
          <button
            v-if="showSignBtn && normalFlowButtonIds.includes('12')"
            class="btn-item"
            @tap="handleAddNodes"
          >
            <text class="oa-icon oa-icon-tianjiachaosong theme-color"></text>
            加签
          </button>
        </view>
        <view class="bottom-btn" v-if="workflowType === 'myReturn'">
          <button class="btn-item" @tap="handleRelaunch">
            <text class="oa-icon oa-icon-zhuanfa theme-color"></text>
            重新发起
          </button>
        </view>
      </view>
    </view>
    <view
      class="page-office-box"
      v-if="workflowInfo && workflowInfo.templateType == '1' && showContent"
    >
      <view
        class="file-item"
        @tap="downloadFile(readFormDatas['page_office_id'], '')"
        >查看正文</view
      >
    </view>
    <view class="no-permission-box" v-if="showContent != null && !showContent">
      <image
        class="no-permission-img"
        mode="aspectFit"
        src="../../static/img/noPermission.png"
      ></image>
      <view class="no-permission-text">暂无查看权限</view>
    </view>
    <handwritten-signature
      v-model="showHandwrittenSignature"
      title="签字提交"
      @ok="changeHandwrittenSignature"
    ></handwritten-signature>
  </view>
</template>

<script>
import approvalCustomDetail from './approval-custom-detail.js';
import FormCheck from '@/components/form-components/form-check.vue';
import FormInput from '@/components/form-components/form-input.vue';
import WfTabBar from './components/wf-tab-bar.vue';
import WfHeadInfo from './components/wf-head-info.vue';
import WfCommentList from './components/wf-comment-list.vue';
import HandwrittenSignature from './components/handwritten-signature.vue';

export default {
  mixins: [approvalCustomDetail],
  components: {
    FormCheck,
    FormInput,
    WfTabBar,
    WfHeadInfo,
    WfCommentList,
    HandwrittenSignature
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .content-box {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .write-form-wrap {
    /deep/ .form_content .form {
      margin-bottom: 0;
      .row_tab .form_row {
        background-color: rgba(0, 91, 172, 0.15);
      }
    }
  }
  .form-title {
    color: #999;
    padding: 10rpx 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
    background-color: #f7f8f8;
  }
  .content-wrap {
    flex: 1;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .content {
      flex: 1;
      overflow: hidden;
      width: inherit;
      background-color: #fff;
      //margin-bottom: 90rpx;

      &.is-ios /deep/ .uni-scroll-view {
        -webkit-overflow-scrolling: unset !important;
      }
      .nothing {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        .img-content {
          width: 300rpx;
          height: 300rpx;
          .nothing-img {
            width: 100%;
            height: 100%;
          }
        }
      }

      .task-history-wrap {
        padding: 20rpx 30rpx 20rpx 40rpx;
      }

      /deep/ .child-form-file-item {
        overflow: hidden;
        display: -webkit-box; //特别显示模式
        -webkit-line-clamp: 2; //行数
        line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
      }
    }
    .theme-color {
      color: $theme-color !important;
    }
    .grey-color {
      color: $uni-text-color-subtitle !important;
    }
    .btn-wrap {
      z-index: 10;
      .bottom-btn {
        background-color: #ffffff;
        box-shadow: 0 1px 6px #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        .btn-item {
          padding: 0;
          height: 90rpx;
          flex: 1;
          box-sizing: border-box;
          text-align: center;
          font-size: 32rpx;
          position: relative;
          background-color: transparent;
          color: #333;
          display: flex;
          align-items: center;
          justify-content: center;
          &::after {
            border: 0;
            top: 20rpx;
            bottom: 20rpx;
            right: 0;
            left: unset;
            transform: scaleX(-0.5);
            width: 1px;
            height: unset;
            background-color: #ccc;
          }
          &:last-child::after {
            width: 0;
          }
          .oa-icon {
            margin: 0 6rpx;
            font-size: 36rpx;
          }
        }
      }
    }
  }
  .page-office-box {
    font-size: 26rpx;
    color: #ffffff;
    text-align: right;
    position: fixed;
    right: 0;
    top: 35%;
    background-color: #75a5f2;
    padding: 10rpx 20rpx;
    border-radius: 40rpx 0 0 40rpx;
    z-index: 2;
    .file-item {
      text-decoration: none;
      color: #ffffff;
    }
  }

  .no-permission-box {
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, calc(-50% - 45px));
    .no-permission-img {
      width: 420rpx;
      height: 230rpx;
    }
    .no-permission-text {
      color: #b4b8bf;
      font-size: 28rpx;
    }
  }
}
/deep/ .child-form-item.disabled {
  .row_lable,
  .row_value .row_value_input_text {
    color: #999 !important;
  }
}
</style>
