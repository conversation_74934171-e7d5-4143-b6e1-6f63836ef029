import { mapState, mapMutations } from 'vuex';
import Base64 from '@/common/js/base64.min.js';
import loginModule from '@/common/js/loginModule.js';
import formAutoCalculat from '@/common/js/formAutoCalculat.js';
import { operationItemColumn } from './mixins/dictionary.js';

export default {
  data() {
    return {
      isIOS: false,
      flowIsMyself: false,
      pagePramasStr: '',
      formListPage: '',
      formListTabIndex: 0,

      showContent: null,
      formTitle: '',
      workflowType: '',
      isAddSignature: '',
      workflowInfo: {},
      tabIndex: 0,
      tabBars: [
        {
          name: '申请详情',
          viewId: 'form'
        },
        {
          name: '填写/修改信息',
          viewId: 'writeForm'
        },
        {
          name: '审批意见',
          viewId: 'opinion'
        },
        {
          name: '流程信息',
          viewId: 'record'
        },
        {
          name: '抄送信息',
          viewId: 'copyToUser'
        }
      ],
      readFormTemplate: [],
      writeFormTemplate: [],
      tableName: '',
      nodeCondition: {},
      readFormDatas: {},
      writeFormDatas: {},
      commentFormDatas: {},
      returnFormDatas: {},
      commentList: [],
      optionList: [],
      taskHistoryList: [],
      taskCopytoUserList: [],
      scrollViewId: '',
      nodeHeight: [], //存储categoryList的top
      windowHeight: 0,
      windowTop: 0,
      permissionsList: [],
      returnBtn: true,
      fileOpt: {},
      isHandwrittenSignature: false,
      normalFlowButtonList: [],
      showHandwrittenSignature: false
    };
  },
  computed: {
    ...mapState(['empcode']),
    showSignBtn() {
      const { currentStepName = '' } = this.workflowInfo;
      return (
        currentStepName !== '申请人撤销' &&
        currentStepName !== '重新提交' &&
        currentStepName !== '【结束】' &&
        currentStepName !== '办结'
      );
    },
    normalFlowButtonIds() {
      let list = this.normalFlowButtonList.filter(item => item.status);
      if (!list.length) {
        return this.normalFlowButtonList.map(item => item.wfBaseButtonId);
      }
      return this.normalFlowButtonList
        .filter(item => item.status == 1)
        .map(item => String(item.wfBaseButtonId));
    }
  },
  watch: {
    tabIndex(newVal) {
      this.scrollViewId = this.tabBars[newVal].viewId;
    }
  },
  async onLoad(opt) {
    this.isIOS = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
    this.pagePramasStr = this.$common.convertObj(opt);

    if (opt?.token && !this.$config.ENABLE_ACCOUNT_LOGIN) {
      let userInfo = await loginModule.loginInfo(opt);
      if (JSON.stringify(userInfo) != '{}') this.changeState(userInfo);
    }
    if (opt.isMobile) {
      await this.getWorkflowData(opt.wfInstId);
    } else {
      if (opt.formListTabIndex) this.formListTabIndex = opt.formListTabIndex;
      this.formListPage = opt.formListPage ? opt.formListPage : '';
      this.workflowInfo = uni.getStorageSync('workflow_info');
      this.formTitle = this.workflowInfo.workflowName;
      if (opt.name && opt.name != null && opt.name != undefined)
        this.workflowType = opt.name;
      if (
        opt.isAddSignature &&
        opt.isAddSignature != null &&
        opt.isAddSignature != undefined
      )
        this.isAddSignature = opt.isAddSignature;
    }
    //当前节点为多节点流程时，当前审批人是将所有当前节点拼接起来的，所以需要单独获取当前审批人所在的节点
    if (this.workflowInfo.taskId) {
      await this.getApproverCurrentStepNo(this.workflowInfo.taskId);
    } else {
      this.workflowInfo.approverCurrentStepNo = this.workflowInfo.currentStepNo;
    }
    await this.getFormDatas();
  },
  onUnload() {
    uni.removeStorageSync('workflow_info');
  },
  filters: {
    copyNameFileter(row) {
      let copy = [];
      row.forEach(i => {
        copy.push(i.username);
      });
      return copy.join(',');
    }
  },
  methods: {
    ...mapMutations(['changeState']),

    //获取当前审批人所在的节点
    async getApproverCurrentStepNo(taskId) {
      await this.ajax.getCurrentStepNoByTaskId(taskId).then(async res => {
        this.workflowInfo.approverCurrentStepNo = res.object.wfStepNo;
      });
    },
    //获取流程信息数据
    async getWorkflowData(wfInstanceId) {
      let _self = this;
      await _self.ajax
        .getWorkflowData({
          wfInstId: wfInstanceId
        })
        .then(async res => {
          _self.workflowInfo = res.object;
          uni.setStorageSync('workflow_info', res.object);
          //解决推送到微信的流程在已审批后，再次点击链接还会显示处理按钮的问题
          let assigneeNo = res.object.assigneeNo
              ? res.object.assigneeNo.split(',')
              : '',
            permission = assigneeNo.includes(_self.empcode);
          if (res.object.status == 1 && permission) {
            //status：待办/重新提交-1，办结-2
            _self.workflowType = 'approvalToDo';
            //重新提交
            if (res.object.statusName == '退回重办') {
              _self.workflowType = 'myReturn';
            }
          } else if (res.object.status == 2) {
            _self.workflowType = 'approvalDone';
          } else if (res.object.status == 3 && permission) {
            _self.workflowType = 'myReturn';
          }
          _self.isAddSignature = res.object.isAddSignature;
          if (_self.pagePramasStr.indexOf('isAddSignature') == -1) {
            _self.pagePramasStr += `&isAddSignature=${_self.isAddSignature}`;
          }
          _self.formTitle = _self.workflowInfo.workflowName;
        });
    },
    //获取表单数据
    async getFormDatas() {
      let _self = this;
      await _self.ajax
        .getFormDatas({
          id: _self.workflowInfo.businessId,
          wfDefinitionId: _self.workflowInfo.wfDefinitionId,
          source: 'mobile'
        })
        .then(async res => {
          _self.readFormDatas = res['object'][0];
          _self.writeFormDatas = JSON.parse(JSON.stringify(res['object'][0]));
          _self.returnFormDatas = JSON.parse(JSON.stringify(res['object'][0]));
          await _self.getFormFiledInfo();
        });
    },
    //获取表单字段读写
    async getFormFiledInfo() {
      let _self = this;
      await _self.ajax
        .getFiledPermissions({
          wfDefinitionId: _self.workflowInfo.wfDefinitionId,
          wfStepId: _self.workflowInfo.approverCurrentStepNo
        })
        .then(async res => {
          await _self.getFormTemplate(res.object);
        });
    },
    //获取表单设计模板
    async getFormTemplate(controlList) {
      let _self = this;
      await _self.ajax
        .getFormTemplate(_self.workflowInfo.wfDefinitionId)
        .then(async res => {
          let dataList = res.object.toaFieldSetList,
            dataSort = JSON.parse(res.object.formTemplate),
            dataSortList = [];
          dataSort.forEach(item => {
            for (let i = 0; i < dataList.length; i++) {
              if (item == dataList[i].fieldName) {
                dataSortList.push(dataList[i]);
                break;
              }
            }
          });

          //格式化子表单数据
          let childFormTempList = dataSortList.filter(
            item => item.fieldType == 'childForm'
          );
          if (childFormTempList.length) {
            let apiList = [],
              fileChildFormIndexList = [],
              limitColumnList = await this.ajax.getChildFOrmApprovalLimit({
                wfDefinitionId: _self.workflowInfo.wfDefinitionId,
                wfStepId: _self.workflowInfo.approverCurrentStepNo,
                formId: _self.workflowInfo.formId
              }); // 子表单编辑权限

            limitColumnList = limitColumnList.success
              ? limitColumnList.object
              : [];

            childFormTempList.map((temp, index) => {
              apiList.push(
                // 获取 子表单详情
                this.ajax.getChildFormDetailById(temp.tableId).then(res => {
                  if (res.success) {
                    let dataIndex = dataSortList.findIndex(
                        item => item.fieldName == temp.fieldName
                      ),
                      childFormLimit = limitColumnList.filter(
                        item => item.tableId == temp.tableId
                      );
                    dataSortList[dataIndex].childFormDetail = res.object;
                    dataSortList[dataIndex].childFormColumns = res.object.fields
                      .filter(item => item.pcShow == 1)
                      .map(item => {
                        const tWidth = this.$common.measureText(item.remark);
                        let newItem = {
                            ...item,
                            title: item.remark,
                            key: item.fieldName,
                            fieldType: item.fieldType,
                            render: true,
                            width: (tWidth > 50 ? tWidth + 10 : 50) + 'px',
                            optionList: (item.optionValue || '')
                              .replaceAll(/[,，;；]/g, ',')
                              .split(',')
                              .filter(item => item)
                              .map(item => ({ text: item, value: item }))
                          },
                          limit = childFormLimit.find(
                            limit => limit.fieldName == item.fieldName
                          );
                        if (limit && item.fieldType != 'EXPRESSION') {
                          newItem.isNull = limit.isRequired == 1;
                          newItem.isReadOnly = limit.isEditor == 2;
                        }
                        newItem.fieldType == 'EXPRESSION' &&
                          (newItem.formula = formAutoCalculat.computedFormula(
                            item.defaultValue
                          ));

                        item.fieldType == 'FILE' &&
                          fileChildFormIndexList.push({
                            dataIndex,
                            formKey: temp.fieldName
                          });
                        return newItem;
                      });
                  }
                }),
                // 获取子表单数据
                this.ajax
                  .getChildFormDataById({
                    tableId: temp.tableId,
                    fieldName: temp.fieldName,
                    businessId: this.workflowInfo.businessId
                  })
                  .then(res => {
                    if (res.success) {
                      this.readFormDatas[temp.fieldName] = res.object;
                      if (temp.isReadonly != 'Y') {
                        let valueList = JSON.parse(JSON.stringify(res.object));
                        this.writeFormDatas[temp.fieldName] = valueList;
                      }
                    }
                  })
              );
            });
            await Promise.all(apiList);
            apiList = [];

            // 获取附件数据
            fileChildFormIndexList.map(({ dataIndex, formKey }) => {
              let dataList = this.writeFormDatas[formKey] || [],
                fileItem =
                  dataSortList[dataIndex].childFormColumns.find(
                    item => item.fieldType == 'FILE'
                  ) || {};

              dataList.map(data => {
                apiList.push(
                  this.ajax
                    .getFileAttachmentByBusinessId({
                      businessId: data[fileItem.key]
                    })
                    .then(res => {
                      if (!res.success) {
                        return;
                      }
                      let fileList = res.object.map(file => {
                        let { id, realPath, fileExtension } = file,
                          filePath = `${this.$config.DOCUMENT_BASE_HOST}${realPath}?fullfilename=${id}.${fileExtension}&source=mobile`,
                          href = `/pages/webview/webview?url=${
                            this.$config.BASE_HOST
                          }/ts-preview/onlinePreview?url=${Base64.encode(
                            filePath
                          )}`;

                        return `<view class="child-form-file-item" data-href="${href}" >
                          <text class="oa-icon oa-icon-${this.$oaModule.formatFileType(
                            file.fileExtension
                          )}"></text>
                          <text>${file.originalName}</text>
                        </view>`;
                      });
                      fileItem.format = function(row, prop) {
                        return fileList.join('');
                      };
                      fileItem.event = {
                        click: function(event, rowData, prop) {
                          let target = event.target;
                          if (
                            !target.classList.contains('child-form-file-item')
                          ) {
                            target = target.parentNode;
                            if (
                              !target.classList.contains('child-form-file-item')
                            ) {
                              return;
                            }
                          }

                          let url = target.getAttribute('data-href');
                          url &&
                            uni.navigateTo({
                              url
                            });
                        }
                      };
                    })
                );
              });
            });
            await Promise.all(apiList);
            let replaceValue = function(
              valueList,
              fieldType,
              fieldName,
              defaultValue
            ) {
              let dateReg = /^((\d{3}[1-9]|\d{2}[1-9]\d|\d[1-9]\d{2}|[1-9]\d{3})\-(((0[13578]|1[02])\-(0[1-9]|[12]\d|3[01]))|((0[469]|11)\-(0[1-9]|[12]\d|30))|(02\-(0[1-9]|[1]\d|2[0-8])))$)|(((\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))\-02\-29)$/,
                numReg = /^(([1-9]{1}\d*)|(0{1}))((\.\d{1,4}|\.))?$/;
              valueList.forEach(val => {
                let value = val[fieldName];
                if (fieldType == 'DATEPICKER' && !dateReg.test(value)) {
                  value = defaultValue || '';
                }
                if (fieldType == 'NUMBER' && !numReg.test(value)) {
                  value = defaultValue || '';
                }
                val[fieldName] = value;
                // return val
              });
            };
            childFormTempList.map(temp => {
              if (temp.isReadonly == 'Y') {
                return;
              }
              let dataIndex = dataSortList.findIndex(
                item => item.fieldName == temp.fieldName
              );
              dataSortList[dataIndex].childFormColumns.map(item => {
                let { fieldName, defaultValue, fieldType } = item;

                if (['DATEPICKER', 'NUMBER'].includes(fieldType)) {
                  replaceValue(
                    this.writeFormDatas[temp.fieldName],
                    fieldType,
                    fieldName,
                    defaultValue
                  );
                  replaceValue(
                    this.readFormDatas[temp.fieldName],
                    fieldType,
                    fieldName,
                    defaultValue
                  );
                }
              });
            });
          }

          // 格式化手术项目数据
          let operationItemList = dataSortList.filter(
            item => item.fieldType == 'operationItem'
          );
          if (operationItemList.length) {
            operationItemList.forEach(temp => {
              let operationItemData = JSON.parse(
                _self.readFormDatas[temp.fieldName]
              );
              _self.readFormDatas[temp.fieldName] = operationItemData;
              let dataIndex = dataSortList.findIndex(
                item => item.fieldName == temp.fieldName
              );
              dataSortList[dataIndex].columns = operationItemColumn
                .filter(col => operationItemData[0][col.key] !== undefined)
                .map(item => {
                  const tWidth = this.$common.measureText(item.title);
                  let itemSetting = {
                    width: (tWidth > 50 ? tWidth + 20 : 50) + 'px',
                    ...item
                  };
                  return itemSetting;
                });
            });
          }

          // hrpHyperlink不为地址 则渲染附件组件
          dataSortList.forEach(item => {
            if (item.fieldType === 'hrpHyperlink') {
              let hrpHyperlinkValue = _self.readFormDatas[item.fieldName];
              if (
                hrpHyperlinkValue &&
                hrpHyperlinkValue.indexOf('http:') === -1
              ) {
                item.fieldType = 'file';
              }
            }
          });

          let field = _self.fieldFilter(
            controlList,
            dataSortList,
            _self.readFormDatas
          );
          _self.returnBtn = _self.readFormDatas[field.documentNoKey]
            ? false
            : true; //判断是否已有发文文号，有则不能退回
          if (field.file.length > 0) {
            field.file.forEach(async item => {
              await _self.getFiles(item.fieldName);
            });
          }
          _self.$nextTick(async () => {
            _self.readFormTemplate = field.read;
            _self.writeFormTemplate = field.write;
            _self.commentList = field.comment;
            _self.tableName = res.object.tableName;
            _self.nodeCondition = {
              taskId: _self.workflowInfo.taskId,
              id: res.object.id
            };
            _self.tabBars[1].hidden =
              field.write.length == 0 && _self.workflowType != 'approvalToDo';
            await _self.getApprovalOpinion();

            await setTimeout(() => {
              const scriptText = res.object.javascriptText;
              if (scriptText) {
                let fn = new Function(scriptText);
                fn.call(_self);
                fn = null;
                _self.$forceUpdate();
              }
            }, 1000);
          });
        });
    },

    fieldFilter(controlList, dataList, data) {
      let readList = [],
        writeList = [],
        commentList = [],
        fileList = [],
        documentNoKey = '';
      dataList.forEach(item => {
        controlList.forEach(one => {
          if (one.isHide != 1) {
            if (item.fieldName === one.fieldName) {
              //isShow 控制节点是否显示字段
              //if(item.fieldName === one.fieldName && one.isShow === 1){
              if (one.isEditor === 1) {
                //可编辑
                item.isMust = one.isRequired === 1 ? 'Y' : '';
                item.isReadonly = 'N';
                writeList.push(item);
                if (
                  data[one.fieldName] != null &&
                  item.fieldType != 'comment'
                ) {
                  readList.push(item);
                  if (item.fieldType === 'file') {
                    fileList.push(item);
                  } else if (item.fieldType === 'FWWH') {
                    //发文文号
                    documentNoKey = item.fieldName;
                  }
                }
              } else if (one.isEditor === 2) {
                //只读，不可编辑
                if (item.fieldType != 'comment') {
                  readList.push(item);
                  if (item.fieldType === 'file') {
                    fileList.push(item);
                  } else if (item.fieldType === 'FWWH') {
                    //发文文号
                    documentNoKey = item.fieldName;
                  }
                }
              }
              if (item.fieldType == 'comment') {
                commentList.push(item);
              }
            }
          }
        });
      });
      let fieldList = {
        read: readList,
        write: writeList,
        comment: commentList,
        file: fileList,
        documentNoKey: documentNoKey
      };
      return fieldList;
    },
    //获取附件
    async getFiles(fieldName) {
      let _self = this;
      await _self.ajax
        .getFiles({
          idsStr: _self.readFormDatas[fieldName]
        })
        .then(res => {
          _self.$set(_self.readFormDatas, fieldName, res.object);
          _self.$set(_self.fileOpt, fieldName, res.object);
        });
    },
    //获取审批意见
    async getApprovalOpinion() {
      let _self = this;
      await _self.ajax
        .getApprovalOpinion(_self.workflowInfo.wfInstanceId)
        .then(async res => {
          if (res.object.length > 0) {
            res.object.forEach(item => {
              _self.commentList.forEach(one => {
                if (item.approvalFiled === one.fieldName) {
                  if (one.hideApprovalTime) {
                    item.finishedDate = '';
                  }
                  _self.optionList.push({
                    ...item,
                    ...{ approvalFiledName: one.showName, fileList: [] }
                  });
                }
              });
            });
          }
          await this.getTaskFileList();
        });
    },
    //获取流程节点附件
    async getTaskFileList() {
      let _self = this;
      await _self.ajax
        .getTaskFileList({
          wfInstanceId: _self.workflowInfo.wfInstanceId,
          pageNo: 1,
          pageSize: 999
        })
        .then(async res => {
          res.rows.forEach(row => {
            for (let i = 0; i < _self.optionList.length; i++) {
              if (row.taskId == _self.optionList[i].taskId) {
                row.fileUrl = row.fileUrl.replace(/\\/g, '/');

                row.fileId = row.fileUrl.split('/')[0];
                row.fileExtension = row.fileUrl.split('.')[1];
                _self.optionList[i].fileList.push(row);
                break;
              }
            }
          });
          await _self.getTaskHisList();
          await _self.getCopyUserList();
        });
    },

    //获取流程信息
    async getTaskHisList() {
      let _self = this;
      await _self.ajax
        .getTaskHisList({
          wfInstId: _self.workflowInfo.wfInstanceId,
          pageNo: 1,
          pageSize: 100,
          sidx: 'finished_date',
          sord: 'desc'
        })
        .then(async res => {
          _self.taskHistoryList = _self.$oaModule.taskHisList(res.rows);
          await _self.getcurrentNodeTips();
        });
    },
    //获取抄送信息
    async getCopyUserList() {
      let _self = this;
      await _self.ajax
        .getCopyUserList({
          wfInstId: _self.workflowInfo.wfInstanceId,
          pageNo: 1,
          pageSize: 100,
          sidx: 'finished_date',
          sord: 'desc'
        })
        .then(async res => {
          _self.taskCopytoUserList = res.object || [];
        });
    },
    //获取流程节点提示信息
    async getcurrentNodeTips() {
      let _self = this;
      await _self.ajax
        .getCurrentNodeTips(_self.workflowInfo.wfInstanceId)
        .then(async res => {
          this.flowIsMyself = _self.empcode === res.object.createUser;
          let isHideContent = await this.getIsHideContent();
          if (
            isHideContent == 1 &&
            res.object.createUser == _self.empcode &&
            _self.workflowType != 'myReturn'
          ) {
            this.showContent = false;
            return;
          } else {
            this.showContent = true;
          }
          if (res.object.backToStepStr) {
            uni.showModal({
              content: `${res.object.backToStepStr}\n退回说明：${res.object.handleMarkedWords}`,
              confirmText: '知道了',
              showCancel: false,
              confirmColor: '#005BAC'
            });
          } else if (
            res.object.handleMarkedWords &&
            _self.workflowType != 'approvalDone'
          ) {
            uni.showModal({
              content: `办理提示：${res.object.handleMarkedWords}`,
              confirmText: '知道了',
              showCancel: false,
              confirmColor: '#005BAC'
            });
          }
          //判断是否为公文流程，是公文流程获取按钮权限templateType发文-1，收文-2
          if (
            _self.workflowInfo.templateType == '1' ||
            _self.workflowInfo.templateType == '2'
          ) {
            await _self.getButtonPermissions();
          }
          if (
            ['1', '2', '9'].includes(String(_self.workflowInfo.templateType))
          ) {
            await _self.ajax
              .getDocumentButtonPermissions({
                wfDefinitionId: _self.workflowInfo.wfDefinitionId,
                wfStepId: _self.workflowInfo.approverCurrentStepNo,
                buttonType: 9,
                status: 1
              })
              .then(res => {
                if (res.success) {
                  this.normalFlowButtonList = res.object;
                }
              });
          }
        });
    },
    //表单信息是否对发起人隐藏
    async getIsHideContent() {
      let workflowDefinition = 0;
      await this.ajax
        .getWorkflowDefinition(this.workflowInfo.workflowNo)
        .then(res => {
          workflowDefinition = res.object;
          this.isHandwrittenSignature =
            workflowDefinition.exploitConfiguration.indexOf('5') !== -1;
        });
      return workflowDefinition.isHideContent;
    },
    //获取公文按钮权限，buttonType发文-2，收文-3
    async getButtonPermissions() {
      let _self = this;
      await _self.ajax
        .getDocumentButtonPermissions({
          wfDefinitionId: _self.workflowInfo.wfDefinitionId,
          wfStepId: _self.workflowInfo.approverCurrentStepNo,
          buttonType: _self.workflowInfo.templateType == '1' ? 2 : 3,
          status: 1
        })
        .then(res => {
          res.object.forEach(item => {
            if (item.status === 1) {
              _self.permissionsList.push(item);
            }
          });
        });
    },
    scroll(e) {},
    //查看附件详情
    downloadFile(id) {
      let _self = this;
      _self.ajax.getFiles({ idsStr: id }).then(res => {
        let filePath = `${
          _self.$config.ENABLE_FILE_PREVIEW
            ? _self.$config.DOCUMENT_BASE_HOST
            : _self.$config.BASE_HOST
        }/ts-document/attachment/downloadFile/${id}?fullfilename=${
          res.object[0].fileName
        }&source=mobile`;
        if (_self.$config.ENABLE_FILE_PREVIEW) {
          uni.navigateTo({
            url: `/pages/webview/webview?url=${
              _self.$config.BASE_HOST
            }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
          });
        } else {
          _self.$downloadFile.downloadFile(filePath);
        }
      });
    },
    //流程抄送
    handleWorkflowCopy() {
      uni.navigateTo({
        url: `/pages/workflow/operation/workflow-copy?${this.pagePramasStr}`
      });
    },
    //转办
    handleTransfer() {
      uni.navigateTo({
        url: `/pages/workflow/operation/workflow-transfer?formListPage=${this.formListPage}`
      });
    },
    //流程加签
    handleAddNodes() {
      uni.navigateTo({
        url: `/pages/workflow/operation/workflow-add-nodes?${this.pagePramasStr}`
      });
    },
    //审批人撤销
    handleRevoke() {
      if (this.returnBtn) {
        uni.navigateTo({
          url: `/pages/workflow/operation/workflow-handled-revoke`
        });
      } else {
        uni.showToast({
          icon: 'none',
          duration: 2500,
          title: '该发文流程文件已套红，不能进行撤销操作'
        });
      }
    },
    //流程退回
    handleReturn() {
      let _self = this;
      if (_self.returnBtn) {
        uni.setStorageSync('form_info', {
          wfDefinitionId: _self.workflowInfo.wfDefinitionId,
          dataMap: _self.returnFormDatas,
          tableName: _self.tableName,
          workflowNo: _self.workflowInfo.workflowNo,
          taskId: _self.workflowInfo.taskId,
          templateId: _self.nodeCondition.id,
          id: _self.workflowInfo.businessId
        });
        _self.$nextTick(() => {
          uni.navigateTo({
            url: `/pages/workflow/operation/approval-return?${this.pagePramasStr}`
          });
        });
      } else {
        uni.showToast({
          icon: 'none',
          duration: 2500,
          title: '该发文流程文件已套红，不能进行退回操作'
        });
      }
    },
    //加签退回
    handleAddSignatureBack() {
      uni.navigateTo({
        url: `/pages/workflow/operation/workflow-sign-back?formListPage=${this.formListPage}`
      });
    },
    checkPermissions() {
      //判断流程类型templateType发文-1，收文-2
      if (
        this.workflowInfo.templateType == '1' ||
        this.workflowInfo.templateType == '2'
      ) {
        for (var i = 0; i < this.permissionsList.length; i++) {
          var id = this.permissionsList[i].wfBaseButtonId;
          if (id == 1 || id == 3 || id == 4 || id == 5) {
            this.$common.toast(
              `该环节需${this.permissionsList[i].buttonName}，请前往电脑端进行操作`,
              2500
            );
            return false;
          }
        }
      }
      if (this.writeFormTemplate.length) {
        this.$refs['writeForm'].checkFrom();
      } else {
        this.checkTaskType();
      }
    },
    writeFormAfterCheck(valid, itemData, commonData) {
      if (!valid) {
        return false;
      }
      this.writeFormDatas = {
        ...this.writeFormDatas,
        ...itemData
      };
      let childFormNameList = this.writeFormTemplate
        .filter(item => item.fieldType == 'childForm')
        .map(item => item.fieldName);
      childFormNameList.map(key => {
        delete this.writeFormDatas[key];
      });
      this.commentFormDatas = { ...commonData };
      this.checkTaskType();
    },
    //查询流程类型(stepType:1-串行、2-无序并行、4-有序并行)
    checkTaskType() {
      let _self = this;
      _self.ajax
        .getTaskType({
          stepId: _self.workflowInfo.approverCurrentStepNo,
          wfInstId: _self.workflowInfo.wfInstanceId,
          taskId: _self.workflowInfo.taskId
        })
        .then(res => {
          if (res.object.isAddSignature) {
            //判断是否为加签节点
            _self.formSubmit(true);
            return false;
          }
          //判断是否有加签审批未完成
          if (res.object.isHaveAddSignature) {
            let toastText = `您对该流程已加签，其中${res.object.notExamineUser}还未审批，您确定要审批该流程吗？`;
            uni.showModal({
              title: '提示',
              content: toastText,
              success: function(e) {
                if (e.confirm) {
                  _self.judgeWorkflowSort(res.object);
                }
              }
            });
          } else {
            _self.judgeWorkflowSort(res.object);
          }
        });
    },
    //stepType: 1-串行，2-并行，3-多节点串行，4-多节点并行，6:并行(任务审批不阻断流程);7:并行汇合(与第6点联动使用);9:加签
    judgeWorkflowSort(data) {
      let _self = this;
      if (data.stepType === 1 || data.stepType == 6 || data.stepType === 9) {
        _self.formSubmit();
      } else if (data.stepType == 7) {
        if (data.isHaveBeforeParallelTask) {
          uni.showModal({
            title: '提示',
            showCancel: false,
            confirmColor: '#005BAC',
            content: data.message || '暂时无法审批'
          });
          return false;
        }
        _self.formSubmit();
      } else if (
        data.stepType === 2 ||
        data.stepType === 3 ||
        data.stepType === 4
      ) {
        _self.formSubmit(data.isHaveParallelTask);
      } else if (data.stepType === 5) {
        if (data.message) {
          uni.showModal({
            title: '提示',
            showCancel: false,
            confirmColor: '#005BAC',
            content: data.message || '暂时无法审批'
          });
          return false;
        }
        _self.formSubmit(data.isHaveParallelTask);
      }
    },
    //流程通过提交
    formSubmit(isHaveParallelTask = false) {
      let _self = this;
      let formInfo = _self.getFormInfo();
      if (isHaveParallelTask) {
        uni.showModal({
          title: '提示',
          content: '您确定要审批该流程吗？',
          success: function(res) {
            if (res.confirm) {
              if (_self.isHandwrittenSignature) {
                _self.showHandwrittenSignature = true;
              } else {
                _self.submitApproval(formInfo);
              }
            }
          }
        });
        return false;
      }
      uni.setStorageSync('node_condition', _self.nodeCondition);
      uni.setStorageSync('form_info', formInfo);
      _self.$nextTick(() => {
        uni.navigateTo({
          url: `/pages/workflow/operation/approval-pass?${_self.pagePramasStr}&isHaveParallelTask=${isHaveParallelTask}`
        });
      });
    },
    getFormInfo() {
      return {
        ...this.commentFormDatas,
        wfDefinitionId: this.workflowInfo.wfDefinitionId,
        dataMap: this.writeFormDatas,
        fileOpt: this.fileOpt,
        tableName: this.tableName,
        workflowNo: this.workflowInfo.workflowNo,
        taskId: this.workflowInfo.taskId,
        templateId: this.nodeCondition.id,
        id: this.workflowInfo.businessId
      };
    },
    //提交审核信息到服务器
    submitApproval(formInfo) {
      let ajaxPath = '/ts-form/form/api/examination';
      this.ajax.saveApply(ajaxPath, formInfo).then(res => {
        uni.setStorageSync(
          'info',
          JSON.stringify({
            title: this.workflowInfo.workflowTitle,
            opinion: formInfo.remark || '',
            type: 'approval'
          })
        );
        uni.removeStorageSync('workflow_info');
        this.$nextTick(() => {
          uni.redirectTo({
            url: `/pages/workflow/operation/approval-succeeded?formListPage=${this.formListPage}`
          });
        });
      });
    },
    changeHandwrittenSignature(path) {
      let formInfo = this.getFormInfo();
      formInfo.signatureImg = path;
      this.submitApproval(formInfo);
    },
    //重新发起
    handleRelaunch() {
      uni.navigateTo({
        url: `/pages/workflow/operation/reinit-custom-workflow`
      });
    },
    //返回上一层
    returnBack() {
      let pagePath = '';
      uni.removeStorageSync('workflow_info');
      switch (this.formListPage) {
        case 'unreadList':
          pagePath = '/pages/index/work-unread-list';
          break;
        default:
          pagePath = `/pages/workflow/workflow-approval-list?index=${this.formListTabIndex}`;
      }
      this.$nextTick(function() {
        uni.reLaunch({
          url: pagePath
        });
      });
    }
  }
};
