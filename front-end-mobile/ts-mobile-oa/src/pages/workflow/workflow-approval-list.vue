<template>
  <view class="ts-content" v-if="showContent">
    <page-head
      title="流程审批"
      :rightIcon="rightIcon"
      :rightText="rightText"
      @clickLeft="returnBack"
      @clickRight="tabIndex == 0 ? isShowFoot() : ''"
    ></page-head>
    <uni-search-bar
      radius="100"
      bgColor="#FFFFFF"
      searchBgColor="#eeeeee"
      borderColor="transparent"
      cancelButton="none"
      @confirm="search"
      placeholder="请输入流程名称或发起人"
    ></uni-search-bar>
    <scroll-view class="swiper-head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tab, index) in tabBars"
        class="uni-tab-item"
        :key="index"
        :data-current="index"
        @click="ontabtap"
      >
        <view :class="tabIndex === index ? 'uni-tab-item-title-active' : ''">
          <text class="uni-tab-item-title">{{ tab.name }}</text>
          <text
            class="uni-tab-item-num"
            v-if="tab.total != null && tab.total != 0 && index != 1"
            >{{ tab.total >= 100 ? '99+' : tab.total }}</text
          >
        </view>
      </view>
    </scroll-view>
    <swiper
      :current="tabIndex"
      class="swiper-box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in tabBars"
        :key="index"
      >
        <mescroll
          :ref="'mescroll' + index"
          :mescrollIndex="index"
          :down="item.downOption"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view class="contact-list">
            <view
              class="contact-item"
              v-for="row in item['list']"
              :key="row.workflowNumber"
              @tap="chooseItem(row, item['workflowType'], index)"
            >
              <uni-icons
                v-if="isShowCheck"
                :type="handleStatus(row.taskId) ? 'checkbox-filled' : 'circle'"
                :color="handleStatus(row.taskId) ? '#005BAC' : '#aaa'"
                size="48"
                style="margin-right: 20rpx;"
              />
              <view style="flex: 1; width: 100%;">
                <view class="contact-item-info">
                  <text v-if="row.isPress === 1" class="contact-item-speed"
                    >[催办]</text
                  >
                  <text
                    v-if="row.urgencyLevel && row.urgencyLevel != 1"
                    class="contact-item-urge"
                    >[{{ $oaModule.getUrgencyLevel(row.urgencyLevel) }}]</text
                  >
                  <view class="contact-item-title">
                    <text class="title">{{ row.workflowTitle }}</text>
                  </view>
                </view>
                <view class="contact-item-info">
                  <text class="contact-item-time">
                    发起人:{{ row.createUserName }}
                  </text>
                  <text class="contact-item-time">
                    {{ row.updateDate | formatTime }}
                  </text>
                </view>
                <view class="contact-item-info">
                  <text class="contact-item-node">{{
                    row.status === 2 ? '' : '当前节点：' + row.currentStepName
                  }}</text>
                  <text class="contact-item-status" :style="[row.style]">{{
                    row.statusName
                  }}</text>
                </view>
              </view>
            </view>
          </view>
        </mescroll>
      </swiper-item>
    </swiper>
    <transition name="slide-fade">
      <view class="operation-box" v-show="isShowCheck">
        <view
          class="btn-item"
          v-for="(item, index) in footBtnList"
          :key="index"
          :class="item.class"
          @click="footBtnOperate(item.type)"
        >
          <text class="oa-icon" :class="item.iconClass"></text>
          {{ item.name }}
        </view>
      </view>
    </transition>
    <transfer-modal
      ref="transferModal"
      @confirm="handleTransferConfirm"
    ></transfer-modal>
  </view>
</template>

<script>
import TransferModal from './components/transfer-modal.vue';
import Mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    TransferModal,
    Mescroll
  },
  data() {
    return {
      showContent: false,
      keywords: '',
      isShowCheck: false,
      selectedList: [],
      footBtnList: [
        {
          name: '转办',
          class: 'transfer-btn',
          iconClass: 'oa-icon-dakai theme-color',
          type: 'showTransferModal'
        }
      ],
      tabIndex: 0, //当前选中的tab索引值，从0计数
      tabBars: [
        {
          name: '待办',
          workflowType: 'approvalToDo',
          handleStatus: 1, //办理状态
          downOption: false,
          isInit: false,
          total: null,
          list: []
        },
        {
          name: '已办',
          workflowType: 'approvalDone',
          handleStatus: 2,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        }
      ]
    };
  },
  onLoad(opt) {
    if (opt.index && opt.index != 'undefined')
      this.tabIndex = Number(opt.index);
    this.tabBars.map((item, index) => {
      if (this.tabIndex == index) {
        item.downOption = true;
        item.isInit = true;
      } else {
        item.downOption = false;
        item.isInit = false;
      }
    });
    this.showContent = true;
  },
  computed: {
    rightIcon() {
      if (this.tabIndex == 0 && !this.isShowCheck) {
        return 'checkbox';
      }
      return '';
    },
    rightText() {
      if (this.tabIndex == 0 && this.isShowCheck) {
        return '完成';
      }
      return '';
    }
  },
  methods: {
    //搜索
    search(res) {
      this.keywords = res.value;
      this.tabBars.forEach(item => {
        item.list = [];
        item.isInit = false;
      });
      this.$nextTick(() => {
        this.tabBars[this.tabIndex]['isInit'] = true;
        this.$refs[`mescroll${this.tabIndex}`][0].downCallback();
      });
    },
    //tab点解切换
    ontabtap(e) {
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      this.switchTab(Number(index));
    },
    //tab滑动切换
    async ontabchange(e) {
      let index = e.target.current || e.detail.current;
      await this.switchTab(Number(index));
    },
    async switchTab(index) {
      if (this.tabIndex === index) return;
      else if (!this.tabBars[index]['isInit']) {
        this.tabBars[index]['isInit'] = true;
        await this.$refs[`mescroll${index}`][0].downCallback();
      }
      this.tabIndex = index;
      if (this.tabIndex != 0) {
        this.isShowFoot(false);
      }
    },
    async getListData(page, successCallback, errorCallback, keywords, index) {
      await this.ajax
        .getMyHandleWorkflowList({
          isMobile: true,
          handleStatus: this.tabBars[index]['handleStatus'],
          pageSize: page.size,
          pageNo: page.num,
          workflowTitle: this.keywords,
          sidx: 'inst.CREATE_DATE',
          sord: 'desc'
        })
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      this.tabBars[index]['total'] = totalCount;
      rows.forEach(item => {
        rows.checked = false;
        item.style = this.initStyle(item.statusName);
      });
      this.tabBars[index]['list'] = this.tabBars[index]['list'].concat(rows);
    },
    datasInit(keywords, index) {
      this.tabBars[index]['list'] = [];
    },
    initStyle(value) {
      let style = {};
      switch (value) {
        case '待我办理':
          style.color = '#005BAC';
          style.backgroundColor = '#e5edff';
          break;
        case '退回上一步':
          style.color = '#ff8100';
          style.backgroundColor = '#fff1da';
          break;
        case '退回重办':
          style.color = '#ff8100';
          style.backgroundColor = '#fff1da';
          break;
        case '我已办理':
          style.color = '#999999';
          style.backgroundColor = '#eee';
          break;
      }
      return style;
    },
    chooseItem(row, workflowType, index) {
      if (this.isShowCheck) {
        let index = this.selectedList.findIndex(one => {
          return one.taskId == row.taskId;
        });
        if (index != -1) {
          this.selectedList.splice(index, 1);
        } else {
          this.selectedList.push(row);
        }
      } else {
        let pagePath = '';
        //判断PC端是否设置审批地址
        if (row.isNormal === 'N') {
          pagePath = '/pages/workflow/approval-custom-detail';
        } else {
          pagePath = row.mobileExaminePageUrl;
        }
        uni.setStorageSync('workflow_info', row);
        this.$nextTick(() => {
          let pagePramas = {
            name: row.statusName === '退回重办' ? 'myReturn' : workflowType,
            isAddSignature: row.isAddSignature,
            formListTabIndex: index
          };
          uni.navigateTo({
            url: `${pagePath}?${this.$common.convertObj(pagePramas)}`
          });
        });
      }
    },
    handleStatus(id) {
      return this.selectedList.some(item => {
        return item.taskId == id;
      });
    },
    isShowFoot(data) {
      if (data != undefined) {
        this.isShowCheck = data;
      } else {
        this.isShowCheck = !this.isShowCheck;
      }
      if (this.isShowCheck) {
        this.selectedList = [];
      }
    },
    footBtnOperate(type) {
      if (this.selectedList.length == 0) {
        uni.showToast({
          title: '请选择要操作的数据',
          icon: 'none'
        });
        return;
      }
      this[type]();
    },
    showTransferModal() {
      this.$refs.transferModal.show();
    },
    handleTransferConfirm(datas) {
      let taskIdArr = this.selectedList.map(item => item.taskId);
      let params = {
        ...datas,
        taskId: taskIdArr.join(',')
      };
      this.ajax.handleTransfer(params).then(res => {
        uni.showToast({
          title: '转办成功',
          icon: 'none'
        });
        this.$refs.transferModal.handleCancel();
        this.isShowFoot(false);
        this.datasInit('', 0);
        this.$refs[`mescroll0`][0].downCallback();
      });
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  /deep/ .uni-navbar__header-container {
    padding-right: 88rpx;
  }
  /deep/ .uni-navbar__header-btns-right {
    position: absolute;
    right: 0;
  }
  .swiper-head {
    position: relative;
    width: 750rpx;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    /* #ifndef APP-PLUS */
    white-space: nowrap;
    /* #endif */
    /* flex-wrap: nowrap; */
    /* border-color: #cccccc;
			border-bottom-style: solid;
			border-bottom-width: 1px; */
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
    .uni-tab-item-title-active {
      color: $theme-color;
      border-bottom: 2px solid $theme-color;
    }
    .uni-tab-item {
      /* #ifndef APP-PLUS */
      display: inline-block;
      /* #endif */
      flex-wrap: nowrap;
      width: 46%;
      padding-left: 34rpx;
      padding-right: 34rpx;
      margin: 0 2%;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title,
      .uni-tab-item-num {
        color: #666;
        font-size: 30rpx;
        height: 100%;
        line-height: 2.5;
        flex-wrap: nowrap;
        box-sizing: border-box;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
      }
      .uni-tab-item-num {
        font-size: 28rpx;
        color: #fff;
        background-color: #f59a23;
        border-radius: 40rpx;
        padding: 0 10rpx;
        margin: 0 10rpx;
      }
    }
  }
  .swiper-box {
    flex: 1;
    .swiper-item {
      flex: 1;
      flex-direction: row;
    }
  }
  .contact-item {
    padding: 22rpx 30rpx;
    background-color: #ffffff;
    position: relative;
    display: flex;
    align-items: center;
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      height: 1px;
      background-color: #eee;
      left: 30rpx;
      right: 0;
    }
    &:last-child::after {
      height: 0;
    }
    .contact-item-info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    .contact-item-title {
      font-size: 32rpx;
      color: #333333;
      font-weight: bold;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex: 1;
      .title {
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
      }
    }
    .contact-item-time {
      font-size: 24rpx;
      color: #999;
      overflow: hidden;
      .contact-item-icon {
        color: #f59a23;
        padding-right: 10rpx;
        font-size: 28rpx;
      }
    }
    .contact-item-node {
      flex: 1;
      font-size: 28rpx;
      color: #666;
    }
    .contact-item-speed {
      color: #dd1f36;
    }
    .contact-item-urge {
      color: #f59a23;
    }
    .contact-item-speed,
    .contact-item-urge {
      font-size: 28rpx;
      font-weight: bold;
    }
    .contact-item-status {
      flex-shrink: unset;
      font-size: 24rpx;
      transform: scale(0.83);
      color: #999;
      background-color: #eee;
      padding: 2rpx 10rpx;
      border-radius: 8rpx;
    }
  }
  .operation-box {
    background: #fff;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    box-shadow: 0 1px 6px #ddd;
    z-index: 10;
    .btn-item {
      flex: 1;
      line-height: 100rpx;
      height: 100rpx;
      text-align: center;
      .oa-icon {
        margin: 0 3px;
        font-size: 18px;
      }
    }
    .theme-color {
      color: #005bac;
    }
  }
}
</style>
