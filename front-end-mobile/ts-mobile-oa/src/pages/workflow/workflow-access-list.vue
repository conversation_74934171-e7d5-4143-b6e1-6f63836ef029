<template>
  <view class="ts-content">
    <page-head
      @clickLeft="returnBack"
      :title="title"
      :rightText="classifiedList.length > 0 ? '分类' : ''"
      @clickRight="classifiedList.length > 0 ? changeDrawer() : ''"
    ></page-head>
    <uni-search-bar
      radius="100"
      bgColor="#FFFFFF"
      searchBgColor="#eeeeee"
      borderColor="transparent"
      cancelButton="none"
      @confirm="search"
      placeholder="请输入流程名称或发起人"
    ></uni-search-bar>
    <scroll-view class="swiper_head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tab, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        :data-current="index"
        @click="ontabtap"
      >
        <view :class="tabIndex == index ? 'uni-tab-item-title-active' : ''">
          <text class="uni-tab-item-title">{{ tab.title }}</text>
          <text class="uni-tab-item-num" v-if="tab.total != null"
            >（{{ tab.total }}）</text
          >
        </view>
      </view>
    </scroll-view>
    <swiper
      :current="tabIndex"
      class="swiper_box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper_item"
        v-for="(item, index) in tabBars"
        :key="index"
      >
        <mescroll
          :ref="'mescroll' + index"
          :mescrollIndex="index"
          :down="item.downOption"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view class="contact_list">
            <view
              class="contact_item"
              v-for="row in item['list']"
              :key="row.wfInstanceId"
              @tap="chooseItem(row, item['workflowType'], index)"
            >
              <view class="contact_item_row">
                <text v-if="row.isPress == 1" class="contact_item_speed"
                  >[催办]</text
                >
                <text
                  v-if="row.urgencyLevel && row.urgencyLevel != 1"
                  class="contact_item_urge"
                  >[{{ $oaModule.getUrgencyLevel(row.urgencyLevel) }}]</text
                >
                <view class="contact_item_title">
                  <text class="title">{{ row.workflowTitle }}</text>
                </view>
              </view>
              <view class="contact_item_row">
                <text class="contact_item_time">
                  发起人:{{ row.createUserName }}
                </text>
                <text class="contact_item_time" v-if="index == 0">
                  {{ row.createDate | formatTime }}
                </text>
                <text class="contact_item_time" v-else-if="index == 1">
                  {{ row.wfFinishedDate | formatTime }}
                </text>
              </view>
              <view class="contact_item_row">
                <text class="contact_item_node">{{
                  row.status === 2 ? '' : '当前节点：' + row.currentStepName
                }}</text>
                <text
                  class="contact_item_status"
                  :style="{
                    color: row.status === 1 ? '#fff' : '#999',
                    'background-color': row.status === 1 ? '#005BAC' : '#eee'
                  }"
                  >{{ row.status | statusFilter }}</text
                >
              </view>
            </view>
          </view>
        </mescroll>
      </swiper-item>
    </swiper>
    <uni-drawer
      v-if="classifiedList.length > 0"
      :visible="showRight"
      mode="right"
      @close="closeDrawer('right')"
    >
      <view class="contact_list">
        <uni-collapse>
          <view class="uni_collapse_cell">
            <view
              class="uni_collapse_cell_title"
              @tap="chooseCollapseItem('流程查阅')"
            >
              <text class="uni_collapse_cell_title_text">{{
                allClassify.name
              }}</text>
            </view>
          </view>
          <uni-collapse-item
            v-for="item in classifiedList"
            :title="item.name"
            :collapse-text-style="collapseTextStyle"
            :custom-icon="item.userData.mobileIcon"
            :custom-icon-color="item.userData.color"
            :key="item.id"
          >
            <view
              class="contact_item collapse_contact_item"
              v-for="row in item['children']"
              :key="row.id"
              @tap="chooseCollapseItem(row.name, row.code)"
            >
              <text class="contact_item_text">{{ row.name }}</text>
            </view>
          </uni-collapse-item>
        </uni-collapse>
      </view>
    </uni-drawer>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import uniDrawer from '@/components/uni-drawer/uni-drawer.vue';
export default {
  components: {
    mescroll,
    uniDrawer
  },
  data() {
    return {
      showRight: false,
      title: '流程查阅',
      keywords: '',
      tabIndex: 0, //当前选中的tab索引值，从0计数
      tabBars: [
        {
          title: '在办',
          workflowType: 'accessDoing',
          handleStatus: 1, //办理状态
          downOption: false, //初始化时是否下拉加载
          isInit: false, //是否已初始化
          total: null,
          list: []
        },
        {
          title: '办结',
          workflowType: 'accessDone',
          handleStatus: 2,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        }
      ],
      classifiedList: [],
      allClassify: {},
      collapseTextStyle: {
        color: '#333',
        'font-weight': 'bold'
      },
      workflowNo: ''
    };
  },
  onLoad(opt) {
    if (opt.index) this.tabIndex = Number(opt.index);
    this.tabBars.map((item, index) => {
      if (this.tabIndex == index) {
        item.downOption = true;
        item.isInit = true;
      } else {
        item.downOption = false;
        item.isInit = false;
      }
    });
    this.getClassifiedDatas();
    this.getInfoNum();
  },
  filters: {
    statusFilter(value) {
      let statusStr = '';
      switch (value) {
        case 1:
          statusStr = '在办';
          break;
        case 2:
          statusStr = '办结';
          break;
        case 3:
          statusStr = '强制结束';
          break;
        case 4:
          statusStr = '撤销';
          break;
      }
      return statusStr;
    }
  },
  methods: {
    getInfoNum() {
      let _self = this;
      _self.ajax
        .getMyConsultWfCountByMobile({
          condition: _self.keywords,
          workflowNo: _self.workflowNo
        })
        .then(res => {
          _self.$set(_self.tabBars[0], 'total', res.object.inProcessCount);
          _self.$set(_self.tabBars[1], 'total', res.object.finishedCount);
        });
    },
    //搜索
    search(res) {
      let _self = this;
      _self.keywords = res.value;
      _self.tabBars.forEach(item => {
        item.list = [];
        item.isInit = false;
      });
      _self.$nextTick(() => {
        _self.tabBars[_self.tabIndex]['isInit'] = true;
        _self.$refs[`mescroll${_self.tabIndex}`][0].downCallback();
      });
    },
    //tab点解切换
    async ontabtap(e) {
      let _self = this;
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      await _self.switchTab(Number(index));
    },
    //tab滑动切换
    async ontabchange(e) {
      let _self = this,
        index = e.target.current || e.detail.current;
      await _self.switchTab(Number(index));
    },
    async switchTab(index) {
      let _self = this;
      if (_self.tabIndex === index) {
        return;
      } else if (!_self.tabBars[index]['isInit']) {
        _self.tabBars[index]['isInit'] = true;
        await _self.$refs[`mescroll${index}`][0].downCallback();
      }
      _self.tabIndex = index;
    },
    async getListData(page, successCallback, errorCallback, keywords, index) {
      let _self = this;
      await _self.ajax
        .getMyConsultWfListByMobile({
          isMobile: true,
          status: _self.tabBars[index]['handleStatus'],
          pageSize: page.size,
          pageNo: page.num,
          condition: _self.keywords,
          sidx: index == 0 ? 'inst.create_date' : 'wf_finished_date',
          sord: 'desc',
          workflowNo: _self.workflowNo
        })
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      let _self = this;
      _self.tabBars[index]['total'] = totalCount;
      let list = rows.filter(item => item.status != 0);
      _self.tabBars[index]['list'] = _self.tabBars[index]['list'].concat(list);
    },
    datasInit(keywords, index) {
      let _self = this;
      _self.tabBars[index]['list'] = [];
    },
    chooseItem(row, workflowType, index) {
      let _self = this;
      let pagePath = '';
      if (row.isNormal === 'N') {
        pagePath = '/pages/workflow/my-workflow-detail';
      } else {
        pagePath = row.mobileDetailPageUrl;
      }
      uni.setStorageSync('workflow_info', row);
      _self.$nextTick(() => {
        let pagePramas = {
          name: workflowType,
          formListPage: 'accessList',
          formListTabIndex: index
        };
        uni.navigateTo({
          url: `${pagePath}?${this.$common.convertObj(pagePramas)}`
        });
      });
    },
    //获取分类统计数据
    getClassifiedDatas() {
      let _self = this;
      _self.ajax
        .getConsultTreeByPermMobile({
          condition: ''
        })
        .then(res => {
          if (res.object.length != 0) {
            _self.allClassify = res.object[0];
            res.object.splice(0, 1);
            _self.classifiedList = res.object;
          }
        })
        .catch(() => {
          errorCallback();
        });
    },
    chooseCollapseItem(name, code) {
      let _self = this;
      _self.workflowNo = code;
      if (name.indexOf('（') != -1) {
        _self.title = name.substring(0, name.indexOf('（'));
      } else {
        _self.title = name;
      }
      _self.tabBars.forEach(item => {
        item.list = [];
        item.isInit = false;
        item.total = null;
      });
      _self.closeDrawer();
      _self.$nextTick(() => {
        _self.tabBars[_self.tabIndex]['isInit'] = true;
        _self.getInfoNum();
        _self.$refs[`mescroll${_self.tabIndex}`][0].downCallback();
      });
    },
    //分类抽屉切换
    changeDrawer() {
      let _self = this;
      _self.showRight = !_self.showRight;
    },
    showDrawer() {
      let _self = this;
      _self.showRight = true;
    },
    closeDrawer() {
      let _self = this;
      _self.showRight = false;
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  .swiper_head {
    position: relative;
    width: 100%;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    /* #ifndef APP-PLUS */
    white-space: nowrap;
    /* #endif */
    /* flex-wrap: nowrap; */
    /* border-color: #cccccc;
			border-bottom-style: solid;
			border-bottom-width: 1px; */
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
    .uni-tab-item {
      /* #ifndef APP-PLUS */
      display: inline-block;
      /* #endif */
      flex-wrap: nowrap;
      width: 50%;
      padding-left: 34rpx;
      padding-right: 34rpx;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title,
      .uni-tab-item-num {
        color: #666;
        font-size: 30rpx;
        height: 100%;
        line-height: 2.5;
        flex-wrap: nowrap;
        box-sizing: border-box;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
      }
      .uni-tab-item-num {
        font-size: 28rpx;
        color: #fff;
        color: #f59a23;
      }
      .uni-tab-item-title-active {
        color: $theme-color;
        border-bottom: 2px solid $theme-color;
      }
    }
  }
  .swiper_box {
    flex: 1;
    .swiper_item {
      flex: 1;
      flex-direction: row;
    }
  }
  .uni_collapse_cell {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    border-color: #e5e5e5;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    height: 48px;
    .uni_collapse_cell_title {
      padding: 12px 12px;
      position: relative;
      display: flex;
      width: 100%;
      box-sizing: border-box;
      height: 48px;
      line-height: 24px;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .uni_collapse_cell_title_text {
        flex: 1;
        font-size: 14px;
        white-space: nowrap;
        color: inherit;
        overflow: hidden;
        text-overflow: ellipsis;
        color: rgb(51, 51, 51);
        font-weight: bold;
      }
    }
  }
  .contact_list {
    height: 100%;
    overflow: scroll;
  }
  .contact_item {
    padding: 22rpx 30rpx;
    background-color: #ffffff;
    position: relative;
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      height: 1px;
      background-color: #eee;
      left: 30rpx;
      right: 0;
    }
    &:last-child::after {
      height: 0;
    }
    .contact_item_row {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    .contact_item_title {
      font-size: 32rpx;
      color: #333333;
      font-weight: bold;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex: 1;
      .title {
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
      }
    }
    .contact_item_time {
      font-size: 24rpx;
      color: #999;
      overflow: hidden;
      .contact_item_icon {
        color: #f59a23;
        padding-right: 10rpx;
        font-size: 28rpx;
      }
    }
    .contact_item_node {
      font-size: 28rpx;
      color: #666;
    }
    .contact_item_speed {
      color: #dd1f36;
    }
    .contact_item_urge {
      color: #f59a23;
    }
    .contact_item_speed,
    .contact_item_urge {
      font-size: 28rpx;
      font-weight: bold;
    }
    .contact_item_status {
      font-size: 24rpx;
      transform: scale(0.83);
      color: #999;
      background-color: #eee;
      padding: 2rpx 10rpx;
      border-radius: 8rpx;
    }
    .contact_item_text {
      font-size: 28rpx;
      color: #333333;
    }
  }
}
</style>
