<template>
  <view class="ts-content">
    <page-head
      @clickLeft="returnBack"
      title="发起申请"
      v-show="showHead"
    ></page-head>
    <uni-search-bar
      radius="100"
      bgColor="#FFFFFF"
      searchBgColor="#eeeeee"
      borderColor="transparent"
      @changeFocus="focusChange"
      @confirm="search"
      placeholder="请输入流程名称"
    ></uni-search-bar>
    <scroll-view
      id="tab-bar"
      class="scroll-h"
      :scroll-x="true"
      :show-scrollbar="false"
      v-if="showTab"
    >
      <view
        v-for="(tab, index) in tabBars"
        :key="tab.id"
        class="uni-tab-item"
        :id="tab.id"
        :data-current="index"
        @click="ontabtap"
      >
        <text
          class="uni-tab-item-title"
          :class="tabIndex == index ? 'uni-tab-item-title-active' : ''"
          >{{ tab.name }}</text
        >
      </view>
    </scroll-view>
    <view class="tab_list">
      <swiper
        :current="tabIndex"
        class="swiper-box"
        :duration="300"
        @change="ontabchange"
      >
        <swiper-item class="swiper-item">
          <view class="contact_list">
            <uni-collapse>
              <uni-collapse-item
                v-for="item in tabBars[0]['list']"
                :open="!showTab"
                :title="item.name"
                :collapse-text-style="collapseTextStyle"
                :custom-icon="item.userData.mobileIcon"
                :custom-icon-color="item.userData.color"
                :key="item.id"
              >
                <view
                  class="contact_item collapse_contact_item"
                  v-for="row in item['children']"
                  :key="row.id"
                  @tap="
                    chooseItem(
                      row.userData.isNormal,
                      row.userData.mobileInitiatePageUrl,
                      row.userData.wfDefinitionId,
                      row.code,
                      row.userData.formId
                    )
                  "
                >
                  <text class="contact_item_text">{{ row.name }}</text>
                </view>
              </uni-collapse-item>
            </uni-collapse>
          </view>
        </swiper-item>
        <swiper-item class="swiper-item">
          <view class="contact_list">
            <view
              class="contact_item"
              v-for="row in tabBars[1]['list']"
              :key="row.wfDefinitionId"
              @tap="
                chooseItem(
                  row.isNormal,
                  row.mobileInitiatePageUrl,
                  row.wfDefinitionId,
                  row.workflowNo,
                  row.formId
                )
              "
            >
              <text class="contact_item_text">{{ row.workflowName }}</text>
            </view>
          </view>
        </swiper-item>
        <swiper-item class="swiper-item">
          <view class="contact_list">
            <view
              class="contact_item"
              v-for="row in tabBars[2]['list']"
              :key="row.wfDefinitionId"
              @tap="
                chooseItem(
                  row.isNormal,
                  row.mobileInitiatePageUrl,
                  row.wfDefinitionId,
                  row.workflowNo,
                  row.formId
                )
              "
            >
              <text class="contact_item_text">{{ row.workflowName }}</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
    <uni-popup
      ref="showpopup"
      :type="popupType"
      top-height="104rpx"
      mask-top-height="104rpx"
    >
      <view class="popup-content">
        <!-- 数据列表 -->
        <view class="contact_list" v-if="resource.length > 0">
          <uni-collapse>
            <uni-collapse-item
              v-for="item in resource"
              :title="item.name"
              :collapse-text-style="collapseTextStyle"
              :custom-icon="item.userData.mobileIcon"
              :custom-icon-color="item.userData.color"
              :open="true"
              :key="item.id"
            >
              <view
                class="contact_item collapse_contact_item"
                v-for="row in item['children']"
                :key="row.id"
                @tap="
                  chooseItem(
                    row.userData.isNormal,
                    row.userData.mobileInitiatePageUrl,
                    row.userData.wfDefinitionId,
                    row.code,
                    row.userData.formId
                  )
                "
              >
                <text class="contact_item_text">{{ row.name }}</text>
              </view>
            </uni-collapse-item>
          </uni-collapse>
        </view>
        <view v-else-if="resource.length == 0 && nothinText" class="nothing">{{
          nothinText
        }}</view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import uniCollapse from '@/components/uni-collapse/uni-collapse.vue';
import uniCollapseItem from '@/components/uni-collapse-item/uni-collapse-item.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
export default {
  components: {
    uniCollapse,
    uniCollapseItem,
    uniPopup
  },
  data() {
    return {
      showHead: true, //是否显示头部导航栏
      showTab: true,
      workflowLabel: '',
      tabIndex: 0, //当前选中的tab索引值，从0计数
      tabBars: [
        {
          name: '全部流程',
          id: 'allProcess',
          list: []
        },
        {
          name: '我的收藏',
          id: 'collectionProcess',
          list: []
        },
        {
          name: '常用流程',
          id: 'commonProcess',
          list: []
        }
      ],
      collapseTextStyle: {
        color: '#333',
        'font-weight': 'bold'
      },
      popupType: '', //弹出层类型
      resource: [],
      nothinText: ''
    };
  },
  computed: {
    ...mapState(['empcode'])
  },
  onLoad({ workflowLabel = '' }) {
    if (workflowLabel) {
      this.showTab = false;
      this.workflowLabel = workflowLabel;
      this.getAllProcessList('tab', '', 0);
      return;
    }
    this.getDefaultTab();
  },
  methods: {
    //tab点解切换
    ontabtap(e) {
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      this.switchTab(Number(index));
    },
    //tab滑动切换
    ontabchange(e) {
      let index = e.target.current || e.detail.current;
      this.switchTab(Number(index));
    },
    switchTab(index) {
      if (this.tabIndex === index) {
        return;
      } else if (
        this.tabBars[index]['list'].length === 0 &&
        this.tabIndex != index
      ) {
        this.getList(index);
      }
      this.tabIndex = index;
    },
    getList(index) {
      switch (index) {
        case 0:
          this.getAllProcessList('tab', '', index);
          break;
        case 1:
          this.getCollectionProcessList(index);
          break;
        case 2:
          this.getCommonProcessList(index);
          break;
      }
    },
    //获取默认页签
    getDefaultTab() {
      this.ajax
        .getDefaultTab({
          userCode: this.empcode,
          isMobile: true
        })
        .then(res => {
          this.tabIndex = res.object ? Number(res.object.dictId) - 1 : 0;
          this.getList(this.tabIndex);
        });
    },
    //获取全部流程列表
    getAllProcessList(type, keyWords, index) {
      this.ajax
        .getAllProcessList({
          isMobile: true,
          permissionsType: 1,
          status: 1,
          condition: keyWords,
          workflowLabel: this.workflowLabel
        })
        .then(res => {
          if (type == 'tab') {
            this.tabBars[index]['list'] = this.tabBars[index]['list'].concat(
              res.object
            );
          } else {
            if (res.object && res.object.length) this.resource = res.object;
            else {
              this.nothinText = '未检索到相关流程';
            }
          }
        });
    },
    //获取收藏流程列表
    getCollectionProcessList(index) {
      this.ajax
        .getCollectionProcessList({
          isMobile: true
        })
        .then(res => {
          this.tabBars[index]['list'] = this.tabBars[index]['list'].concat(
            res.object
          );
        });
    },
    //获取常用流程列表
    getCommonProcessList(index) {
      this.ajax
        .getCommonProcessList({
          source: 'mobile',
          isMobile: true
        })
        .then(res => {
          this.tabBars[index]['list'] = this.tabBars[index]['list'].concat(
            res.rows
          );
        });
    },
    //搜索框获取焦点或失去焦点变化
    focusChange(res) {
      let focus = res.focus;
      if (focus) {
        this.popupType = 'allBottom';
        this.$nextTick(() => {
          this.$refs['showpopup'].open();
        });
      } else {
        this.$refs['showpopup'].close();
        this.resource = [];
        this.nothinText = '';
      }
      this.showHead = !focus;
    },
    //搜索
    search(res) {
      this.$nextTick(() => {
        this.resource = [];
        this.nothinText = '';
        this.getAllProcessList('search', res.value);
      });
    },
    //流程选择跳转
    chooseItem(isNormal, path, wfDefinitionId, code, formId) {
      let pagePath = '';
      if (isNormal === 'N') {
        let pagePramas = {
          wfDefinitionId: wfDefinitionId,
          workflowNo: code,
          formId: formId
        };
        if (this.workflowLabel) pagePramas.workflowLabel = this.workflowLabel;
        pagePath = `/pages/workflow/init-custom-workflow?${this.$common.convertObj(
          pagePramas
        )}`;
      } else {
        pagePath = `${path}?fromPage=workflowList`;
      }
      uni.navigateTo({
        url: pagePath
      });
    },
    //返回上一层
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack({
          delta: 1
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
/* #ifndef APP-PLUS */
page {
  width: 100%;
  min-height: 100%;
  display: flex;
}
/* #endif */
.ts-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  /* #ifdef MP-ALIPAY || MP-BAIDU */
  height: 100vh;
  /* #endif */
  .scroll-h {
    position: relative;
    width: 750rpx;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    /* #ifndef APP-PLUS */
    white-space: nowrap;
    /* #endif */
    /* flex-wrap: nowrap; */
    /* border-color: #cccccc;
			border-bottom-style: solid;
			border-bottom-width: 1px; */
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
  }
  .uni-tab-item {
    /* #ifndef APP-PLUS */
    display: inline-block;
    /* #endif */
    flex-wrap: nowrap;
    width: 28%;
    padding-left: 34rpx;
    padding-right: 34rpx;
    margin: 0 2.5%;
    box-sizing: border-box;
    text-align: center;
    .uni-tab-item-title {
      color: #555;
      font-size: 30rpx;
      height: 80rpx;
      line-height: 76rpx;
      flex-wrap: nowrap;
      display: block;
      box-sizing: border-box;
      /* #ifndef APP-PLUS */
      white-space: nowrap;
      /* #endif */
    }
    .uni-tab-item-title-active {
      color: $theme-color;
      border-bottom: 2px solid $theme-color;
    }
  }
  .tab_list {
    flex: 1;
    position: relative;
    .swiper-box {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 100%;
      .swiper-item {
        flex: 1;
        flex-direction: row;
      }
    }
  }
  .popup-content {
    height: 100%;
  }
  .contact_list {
    height: 100%;
    width: 100%;
    overflow: auto;
    .contact_item {
      padding: 20rpx;
      display: flex;
      font-size: 28rpx;
      align-items: center;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        bottom: 0;
        left: 20rpx;
        right: 0;
        height: 1px;
        transform: scaleY(0.5);
        background-color: #c8c7cc;
        content: '';
      }
      &:last-child::after {
        height: 0;
      }
      .contact_item_text {
        font-size: 28rpx;
        color: #333333;
      }
    }
    .collapse_contact_item {
      padding-left: 60rpx;
      &::after {
        left: 60rpx;
      }
    }
  }
  .nothing {
    text-align: center;
    color: #999;
    font-size: 28rpx;
    margin-top: 10px;
  }
}
</style>
