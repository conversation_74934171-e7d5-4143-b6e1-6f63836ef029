<template>
  <view class="wf-head-info-box">
    <view class="wf-head-info_title">
      {{ workflowInfo.workflowTitle }}
    </view>
    <view class="wf-head-info-row">
      <text v-if="workflowInfo.isPress === 1" class="wf-head-info_speed">
        [催办]
      </text>
      <text
        v-if="workflowInfo.urgencyLevel && workflowInfo.urgencyLevel != 1"
        class="wf-head-info_urge"
      >
        [{{ $oaModule.getUrgencyLevel(workflowInfo.urgencyLevel) }}]
      </text>
      <text class="wf-head-info_time">
        {{ workflowInfo.updateDate | formatTime }}
      </text>
    </view>
    <view class="wf-head-info-row">
      <text class="wf-head-info_node">
        {{
          workflowInfo.status === 2
            ? ''
            : '当前节点：' + workflowInfo.currentStepName
        }}
      </text>
      <text
        class="wf-head-info_status"
        :class="{
          'wf-head-info_status-blue': workflowInfo.statusName === '待我办理',
          'wf-head-info_status-org':
            workflowInfo.statusName === '退回上一步' ||
            workflowInfo.statusName === '退回重办'
        }"
      >
        {{ workflowInfo.statusName }}
      </text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'WfHeadInfo',
  props: {
    workflowInfo: {
      type: Object,
      default() {
        return {};
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.wf-head-info-box {
  padding: 22rpx 30rpx;
  box-sizing: border-box;
  border-bottom: 1px solid #eee;
  .wf-head-info-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  .wf-head-info_title {
    font-size: 32rpx;
    color: #333333;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .wf-head-info_time {
    font-size: 24rpx;
    color: #999;
    overflow: hidden;
    .wf-head-info_icon {
      color: #f59a23;
      padding-right: 10rpx;
      font-size: 28rpx;
    }
  }
  .wf-head-info_node {
    flex: 1;
    font-size: 28rpx;
    color: #666;
  }
  .wf-head-info_speed {
    color: #dd1f36;
  }
  .wf-head-info_urge {
    color: #f59a23;
    flex: 1;
  }
  .wf-head-info_speed,
  .wf-head-info_urge {
    font-size: 28rpx;
    font-weight: bold;
  }
  .wf-head-info_status {
    margin-left: 16rpx;
    font-size: 24rpx;
    color: #999;
    font-weight: bold;
  }
  .wf-head-info_status-blue {
    color: #005bac;
  }
  .wf-head-info_status-org {
    color: #f59a23;
  }
}
</style>
