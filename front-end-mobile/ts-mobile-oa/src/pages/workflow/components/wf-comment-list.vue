<template>
  <view class="wf-comment-list-box">
    <view
      class="wf-comment-item"
      v-for="(item, index) in commentList"
      :key="index"
    >
      <view class="wf-comment-item_icon">
        {{ item.actAssigneeName | nameFilter }}
      </view>
      <view class="wf-comment-item_info">
        <view class="wf-comment-item_title">
          {{ item.approvalFiledName }}
        </view>
        <view class="wf-comment-item_top">
          <view class="wf-comment-item_top-left">
            <text class="wf-comment-item_user">
              {{ item.actAssigneeName }}
            </text>
          </view>
          <text class="wf-comment-item_time" v-if="item.finishedDate">
            {{ formatterFinishedDate(item) }}
          </text>
        </view>
        <!-- <view class="option_item_type">{{item.wfStepName}}</view> -->
        <view class="wf-comment-item_content" v-if="item.remark">
          审批内容：{{ item.remark }}
        </view>
        <view
          class="fiel-list"
          v-if="Array.isArray(item.fileList) && item.fileList.length > 0"
        >
          <view
            class="file-item"
            v-for="(i, index) in item.fileList"
            :key="index"
          >
            <text
              class="oa-icon"
              :class="'oa-icon-' + $oaModule.formatFileType(i.fileExtension)"
            ></text>
            <text
              style="flex: 1;"
              @tap.stop="previewFile(i.fileId, i.fileName)"
            >
              {{ i.fileName }}
            </text>
            <!-- <text
              class="oa-icon oa-icon-wodeshoucang"
              @tap.stop="connectionFile(i.fileId, i.fileName)"
            ></text> -->
            <text @tap.stop="previewFile(i.fileId, i.fileName)">预览</text>
            <text @tap.stop="downloadFile(i.fileId, i.fileName)">下载</text>
            <text
              @tap.stop="connectionFile(i.fileId, i.fileName)"
              style="margin-left: 10rpx;"
              >收藏</text
            >
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Base64 from '@/common/js/base64.min.js';
export default {
  name: 'WfCommentList',
  props: {
    commentList: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  methods: {
    formatterFinishedDate(item) {
      let {
        finishedDate = '',
        approvalTimeFormat = 'YYYY-MM-DD HH:mm:ss'
      } = item;
      if (!finishedDate) {
        return '';
      }
      return this.$dayjs(finishedDate).format(approvalTimeFormat);
    },
    /**@desc 查看附件详情 */
    previewFile(id, fileName) {
      let [filename, type] = fileName.split('.');
      let _self = this,
        filePath = `${_self.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${id}.${type}&source=mobile`;
      if (_self.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            _self.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${encodeURIComponent(
            Base64.encode(filePath)
          )}`
        });
      } else {
        // _self.$downloadFile.downloadFile(filePath);
      }
    },
    /**@desc 附件下载 */
    downloadFile(id, fileName) {
      let _self = this,
        filePath = `${_self.$config.BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      _self.$downloadFile.downloadFile(filePath);
    },
    /**@desc 附件收藏 */
    connectionFile(id, fileName) {
      this.ajax.saveCollect({ collectId: id }).then(res => {
        uni.showToast({
          icon: 'none',
          title: res.message || '收藏成功,已收藏到个人文档'
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.wf-comment-list-box {
  .wf-comment-item {
    box-sizing: border-box;
    position: relative;
    width: 100%;
    padding: 22rpx 30rpx 22rpx 10rpx;
    display: flex;
    align-items: flex-start;
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      bottom: 0;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    .wf-comment-item_icon {
      width: 80rpx;
      height: 80rpx;
      margin: 0 20rpx;
      border-radius: 100%;
      color: #ffffff;
      text-align: center;
      line-height: 2.8;
      background-color: #005bac;
      font-size: 28rpx;
    }
    .wf-comment-item_info {
      flex: 1;
      .wf-comment-item_title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
      .wf-comment-item_top {
        display: flex;
        align-items: flex-end;
        .wf-comment-item_top-left {
          flex: 1;
          .wf-comment-item_user {
            font-size: 28rpx;
            color: #666;
          }
          .wf-comment-item_assent {
            color: #3aad73;
          }
          .wf-comment-item_dissent {
            color: #f59a23;
          }
        }
        .wf-comment-item_time {
          font-size: 24rpx;
          color: #999;
        }
      }
      .wf-comment-item_type {
        font-size: 28rpx;
        color: #666;
      }
      .wf-comment-item_content {
        font-size: 28rpx;
        color: #333;
      }
    }
    .fiel-list {
      .file-item {
        text-decoration: none;
        color: #005bac;
        font-size: 28rpx;
        background-color: #fff;
        display: flex;
        align-items: center;
        .oa-icon {
          font-size: 40rpx;
          margin-right: 10rpx;
        }
      }
    }
  }
}
</style>
