<template>
  <view class="collapse_card">
    <view class="collapse_title" @click="handleCollapse">
      <text class="label text_hidden">
        {{ dataSource.name }}
      </text>
      <!-- oa-icon-fanhui-copy oa-icon-down-copy -->
      <i
        :class="[
          'oa-icon',
          collapse ? 'oa-icon-fanhui-copy' : 'oa-icon-down-copy'
        ]"
      ></i>
    </view>
    <view v-if="collapse" class="collapse_body">
      <view class="basic_card_top mb_4">
        <view class="basic_card_top_content">
          <view class="date">
            <image
              class="icon_16 mr_8"
              src="@/static/img/icon_date.png"
            ></image>
            <view class="mr_24 text_style">
              {{ dataSource.bookingTimeBegin }}-{{ dataSource.bookingTimeEnd }}
            </view>
          </view>
          <view class="capacitance ">
            <image
              class="mr_8 icon_16"
              src="@/static/img/icon_people.png"
            ></image>
            {{ dataSource.capacitance }}
          </view>
          <view class="address">
            <image
              class="icon_16 mr_8"
              src="@/static/img/cion_address.png"
            ></image>
            <view class="address_text text_style">
              {{
                `${dataSource.location || ''} ${
                  dataSource.floor ? '-' + dataSource.floor + '层' : ''
                }`
              }}
            </view>
          </view>
        </view>
        <view
          class="basic_card_top_image"
          @click.stop="previewImage(dataSource.emphasis)"
        >
          <image
            mode="aspectFill"
            :src="
              dataSource.emphasis
                ? $config.BASE_HOST + dataSource.emphasis
                : iconEmpty
            "
            class="img"
          ></image>
          <view class="img-desc">
            {{ dataSource.isVideoLable || '' }}
          </view>
        </view>
      </view>
      <view
        class="basic_card_bottom"
        v-if="dataSource.deviceList && dataSource.deviceList.length > 0"
      >
        <view
          class="device_item mr_8 mt_4"
          v-for="(deviceItem, idx) in dataSource.deviceList"
          :key="idx"
        >
          {{ deviceItem.name }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const iconEmpty = require('@/static/img/icon_empty.png');
export default {
  props: {
    dataSource: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      collapse: false,
      iconEmpty
    };
  },
  methods: {
    handleCollapse() {
      this.collapse = !this.collapse;
    },
    previewImage(emphasis) {
      if (!emphasis) return;
      let urlList = [];
      urlList.push(this.$config.BASE_HOST + emphasis); //push中的参数为 :src="item.img_url" 中的图片地址
      uni.previewImage({
        indicator: 'number',
        loop: true,
        urls: urlList
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.basic_card_top {
  display: flex;
  flex-direction: row;
  height: 120rpx;
  .basic_card_top_content {
    flex: 1;

    display: flex;
    flex-direction: row;
    flex-direction: column;
    justify-content: space-between;
    padding-right: 24rpx;
    .title {
      display: flex;
      flex-direction: row;
      align-items: center;
      & > .name {
        flex: 1;
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        margin-right: 26rpx;
      }
    }
    .date,
    .address {
      display: flex;
      align-items: center;

      .text_style {
        font-size: 24rpx;
        line-height: 24rpx;
        font-weight: 400;
        color: #333333;
      }
      .address_text {
        flex: 1;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
      }
      .text_red {
        color: #d21f1f;
      }
    }
    .capacitance {
      display: flex;
      flex-direction: row;
      width: 100rpx;
      align-items: center;
      font-size: 24rpx;
      white-space: nowrap;
    }
  }

  .basic_card_top_image {
    position: relative;
    width: 208rpx;
    max-width: 208rpx;
    min-width: 208rpx;
    height: 100%;
    .img {
      width: 100%;
      height: 100%;
      border-radius: 8rpx;
    }
    .img-desc {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 24rpx;
      color: #ffffff;
      height: 40rpx;
      background: rgba(32, 32, 32, 0.5);
      line-height: 40rpx;
      border-bottom-left-radius: 8rpx;
      border-bottom-right-radius: 8rpx;
    }
  }
}
.basic_card_bottom {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  .device_item {
    height: 40rpx;
    line-height: 36rpx;
    padding: 0 20rpx;
    background: #fafafa;
    border-radius: 20rpx;
    border: 2rpx solid #eee;
    font-size: 22rpx;
    color: #333333;
    text-align: center;
  }
}
.collapse_card {
  background: white;
  width: 100%;
  padding: 16rpx 32rpx;
  .collapse_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .label {
      flex: 1;
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
      padding-right: 64rpx;
    }
  }
  .collapse_body {
    margin-top: 16rpx;
    .collapse_body_top {
      height: 120rpx;
      width: 100%;
    }
  }
  .text_hidden {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    margin-right: 26rpx;
  }
}
</style>
