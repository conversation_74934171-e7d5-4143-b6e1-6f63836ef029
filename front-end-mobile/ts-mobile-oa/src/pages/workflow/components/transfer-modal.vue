<template>
  <u-popup v-model="visible" mode="center" width="90%" border-radius="8">
    <view class="transfer-modal-container">
      <view class="transfer-modal_head">转办</view>
      <view class="transfer-modal_content">
        <form @submit="formSubmit">
          <view class="row dis-flex">
            <view class="row-lable">
              <text class="required-red oa-icon oa-icon-asterisks"></text>
              <text class="row-lable_text">转办人</text>
            </view>
            <view class="row-value row-value_input">
              <input
                v-show="false"
                placeholder="请选择转办人"
                name="transferUserCode"
                v-model="transferForm.transferUserCode"
              />
              <input
                class="row-value_input_text"
                v-model="transferForm.transferUserName"
                readonly
                @tap="choosePerson"
              />
              <uni-icons
                :size="30"
                class="uni-icon-wrapper"
                color="#bbb"
                type="arrowright"
              />
            </view>
          </view>
          <view class="row">
            <view class="row-lable">
              <text class="row-lable_text">备注</text>
            </view>
            <view class="row-value row-value_textarea">
              <textarea
                class="row-value_textarea_text"
                placeholder="请输入"
                auto-height
                name="remark"
                v-model="transferForm.remark"
              />
            </view>
          </view>
          <view class="transfer-modal_footer">
            <button
              class="transfer-modal_footer_btn"
              style="color: #666;"
              @click="handleCancel"
            >
              取消
            </button>
            <view class="line"></view>
            <button
              class="transfer-modal_footer_btn"
              style="color:#005bac"
              form-type="submit"
            >
              确定
            </button>
          </view>
        </form>
      </view>
    </view>
  </u-popup>
</template>

<script>
import graceChecker from '@/common/js/graceChecker.js';
export default {
  name: 'TransferModal',
  data() {
    return {
      visible: false,
      transferForm: {
        transferUserCode: '',
        transferUserName: '',
        remark: ''
      },
      personlist: [],
      rule: [
        {
          filedKey: 'transferUserCode',
          required: true,
          checkType: 'notnull',
          errorMsg: '请选择转办人'
        }
      ]
    };
  },
  methods: {
    async show() {
      this.visible = true;
    },
    handleCancel() {
      this.emptyTransferUser();
      this.transferForm.remark = '';
      this.visible = false;
    },
    //选择转办人
    choosePerson() {
      let _self = this;
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        _self.personlist = data;
        _self.transferForm.transferUserCode = data[0] ? data[0].id : '';
        _self.transferForm.transferUserName = data[0] ? data[0].name : '';
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      uni.setStorageSync('person_list', JSON.stringify(_self.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=radio'
      });
    },
    //清空转办人
    emptyTransferUser() {
      this.personlist = [];
      this.transferForm.transferUserCode = '';
      this.transferForm.transferUserName = '';
    },
    formSubmit(e) {
      //进行表单检查
      let checkRes = graceChecker.check(e.detail.value, this.rule);
      if (checkRes) {
        this.$emit('confirm', this.transferForm);
      } else {
        uni.showToast({ title: graceChecker.error, icon: 'none' });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.transfer-modal-container {
  padding-top: 20rpx;
  .transfer-modal_head {
    text-align: center;
    font-size: 38rpx;
    color: #333;
    font-weight: bold;
  }
  .dis-flex {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
  .transfer-modal_content {
    .row {
      width: 100%;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 30rpx;
        right: 0;
        transform: scaleY(-0.5);
        height: 1px;
        background-color: #eee;
      }
      &:last-child::after {
        height: 0;
      }
      .row-lable {
        width: 240rpx;
        font-size: 32rpx;
        color: #333;
        padding: 22rpx 30rpx;
        box-sizing: border-box;
        position: relative;
        .row-lable_text {
          padding-right: 20rpx;
          box-sizing: border-box;
        }
        .required-red {
          color: #f00;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 4rpx;
          font-size: 24rpx;
        }
        .add-icon {
          font-size: 40rpx;
          padding: 0 30rpx;
          line-height: 1;
          color: #005bac;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .row-lable ~ .row-value {
        flex: 1;
        font-size: 32rpx;
        color: #666;
        padding: 22rpx 30rpx;
        padding-left: 0;
        box-sizing: border-box;
        text-align: right;
      }
      .row-lable ~ .row-value_input {
        display: flex;
        justify-content: center;
        align-items: center;
        .row-value_input_text {
          text-align: right;
          flex: 1;
          font-size: 32rpx;
        }
      }
      .row-lable ~ .row-value_textarea {
        width: 100%;
        padding-left: 30rpx;
        padding-top: 0;
        text-align: left;
        .row-value_textarea_text {
          width: 100%;
          min-height: 180rpx;
          font-size: 32rpx;
          border-color: #eee;
          background: #f4f4f4;
          padding: 8rpx;
        }
        .textarea-placeholder {
          color: #bbb;
        }
      }
    }
  }
  .transfer-modal_footer {
    padding: 0 24rpx;
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .transfer-modal_footer_btn {
      background: none;
      flex: 1;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      font-size: 32rpx;
      font-weight: 400;
      &:after {
        border: none;
      }
    }
    .line {
      height: 100%;
      width: 2rpx;
      background: #eee;
    }
  }
}
</style>
