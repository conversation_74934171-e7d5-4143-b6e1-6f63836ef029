<template>
  <scroll-view
    class="wf-tab-bar-box"
    :scroll-x="true"
    :show-scrollbar="false"
    :enable-flex="true"
  >
    <view
      v-for="(tab, index) in tabBarList"
      :key="index"
      class="wf-tab-bar-item"
      :data-view-id="tab.viewId"
      :data-current="index"
      v-show="!tab.hidden"
      @click="onClickTabBar"
    >
      <text
        class="wf-tab-bar-item_title"
        :class="{ 'wf-tab-bar-item_active': currentIndex == index }"
      >
        {{ tab.name }}
      </text>
    </view>
  </scroll-view>
</template>

<script>
export default {
  name: 'WfTabBar',
  model: {
    prop: 'currentIndex',
    event: 'change'
  },
  props: {
    tabBarList: {
      type: Array,
      default() {
        return [];
      }
    },
    currentIndex: {
      type: [Number, String],
      default() {
        return 0;
      }
    }
  },
  data() {
    return {
      scrollStatus: true //点击状态，是否能点击
    };
  },
  methods: {
    onClickTabBar(e) {
      if (!this.scrollStatus) return;
      // 200毫秒才能执行下次点击
      if (this.scrollStatus) {
        this.scrollStatus = false;
        let data = e.currentTarget.dataset;
        this.$emit('change', data.current);
        setTimeout(() => {
          // 200毫秒才能执行下次点击
          this.scrollStatus = true;
        }, 200);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.wf-tab-bar-box {
  position: relative;
  width: 100%;
  height: 80rpx;
  background-color: #ffffff;
  flex-direction: row;
  box-sizing: border-box;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
  /* flex-wrap: nowrap; */
  /* border-color: #cccccc;
			border-bottom-style: solid;
			border-bottom-width: 1px; */
  &::before,
  &::after {
    position: absolute;
    z-index: 10;
    right: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #eeeeee;
  }
  &::before {
    top: 0;
  }
  &::after {
    bottom: 0;
  }
  /deep/.uni-scroll-view-content {
    display: flex;
    align-content: center;
    justify-content: space-between;
  }
  .wf-tab-bar-item {
    /* #ifndef APP-PLUS */
    display: inline-block;
    /* #endif */
    flex-wrap: nowrap;
    padding: 0 20rpx;
    box-sizing: border-box;
    text-align: center;
    flex: 1;
    .wf-tab-bar-item_title {
      color: #666;
      font-size: 30rpx;
      height: 80rpx;
      line-height: 76rpx;
      flex-wrap: nowrap;
      display: block;
      box-sizing: border-box;
      /* #ifndef APP-PLUS */
      white-space: nowrap;
      /* #endif */
    }
    .wf-tab-bar-item_active {
      color: $theme-color;
      border-bottom: 2px solid $theme-color;
    }
  }
}
</style>
