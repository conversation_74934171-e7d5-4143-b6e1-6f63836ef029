<template>
  <view class="handwritten-signature-box" v-if="visible">
    <page-head
      class="header-box"
      :title="title"
      rightText="保存"
      @clickRight="save"
      @clickLeft="close"
    ></page-head>
    <view class="signature-box">
      <view class="signature-canvas-box">
        <canvas
          class="signature-canvas"
          :id="canvasId"
          :canvas-id="canvasId"
          @touchmove="move"
          @touchstart="start($event)"
          @touchend="end"
          @touchcancel="cancel"
          @longtap="tap"
          disable-scroll="true"
          @error="error"
          style="width: 100%; height: 100%"
        ></canvas>
      </view>
      <view class="operation-btn-box">
        <view class="operation-btn-item" @tap="clear">
          <view class="oa-icon oa-icon-qingchu"></view>
          清除
        </view>
      </view>
    </view>
    <view class="toast-box" v-if="showToast">
      <view class="simple-toast">
        <text class="simple-toast_text">请先签字</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    show: {
      type: Boolean,
      default: () => false
    },
    title: {
      type: String,
      default: '手写签字'
    }
  },
  data() {
    return {
      visible: false,
      canvasId: 'signature',
      content: null,
      touchs: [],
      canvasw: 0,
      canvash: 0,
      hasDh: false,
      isPseudoHorizontal: null,
      showToast: false
    };
  },
  watch: {
    show: {
      async handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.getSignatureBox();
          });
          window.addEventListener('resize', this.reInitCanvase, false);
        } else {
          this.clear();
          window.removeEventListener('resize', this.reInitCanvase, true);
        }
        this.getCanvase();
        this.visible = val;
      }
    }
  },
  methods: {
    reInitCanvase() {
      this.$nextTick(() => {
        this.getSignatureBox();
        //清除画布
        if (this.isPseudoHorizontal) {
          this.content.clearRect(0, 0, this.canvash, this.canvasw);
        } else {
          this.content.clearRect(0, 0, this.canvasw, this.canvash);
        }
        this.content.draw(true);
        this.hasDh = false;
        this.getCanvase();
      });
    },
    getSignatureBox() {
      const query = uni.createSelectorQuery().in(this);
      query
        .selectAll('.signature-box')
        .boundingClientRect(data => {
          this.canvasw = data[0].width - 30;
          this.canvash = data[0].height - 30;
          this.isPseudoHorizontal = this.canvasw < this.canvash;
        })
        .exec();
    },
    getCanvase() {
      this.content = null;
      //获得Canvas的上下文
      this.content = uni.createCanvasContext(this.canvasId, this);
      //设置线的颜色
      this.content.setStrokeStyle('#333');
      //设置线的宽度
      this.content.setLineWidth(5);
      //设置线两端端点样式更加圆润
      this.content.setLineCap('round');
      //设置两条线连接处更加圆润
      this.content.setLineJoin('round');
    },
    // 画布的触摸移动开始手势响应
    start(e) {
      let point = {};
      if (this.isPseudoHorizontal) {
        point = {
          x: e.touches[0].y,
          y: this.canvasw - e.touches[0].x
        };
      } else {
        point = {
          x: e.touches[0].x,
          y: e.touches[0].y
        };
      }
      this.touchs.push(point);
      this.hasDh = true;
    },
    // 画布的触摸移动手势响应
    move(e) {
      let point = {};
      if (this.isPseudoHorizontal) {
        point = {
          x: e.touches[0].y,
          y: this.canvasw - e.touches[0].x
        };
      } else {
        point = {
          x: e.touches[0].x,
          y: e.touches[0].y
        };
      }
      this.touchs.push(point);
      if (this.touchs.length >= 2) {
        this.draw(this.touchs);
      }
    },
    // 画布的触摸移动结束手势响应
    end(e) {
      //清空轨迹数组
      for (let i = 0; i < this.touchs.length; i++) {
        this.touchs.pop();
      }
    },
    // 画布的触摸取消响应
    cancel(e) {
      // console.log('触摸取消' + e)
    },
    // 画布的长按手势响应
    tap(e) {
      // console.log('长按手势' + e)
    },
    error(e) {
      // console.log('画布触摸错误' + e)
    },
    //绘制
    draw(touchsVal) {
      let point1 = touchsVal[0];
      let point2 = touchsVal[1];
      this.content.moveTo(point1.x, point1.y);
      this.content.lineTo(point2.x, point2.y);
      this.content.stroke();
      this.content.draw(true);
      touchsVal.shift();
    },
    //清除操作
    clear() {
      //清除画布
      if (this.isPseudoHorizontal) {
        this.content.clearRect(0, 0, this.canvash, this.canvasw);
      } else {
        this.content.clearRect(0, 0, this.canvasw, this.canvash);
      }
      this.content.draw(true);
      this.hasDh = false;
    },
    //提交保存
    save() {
      if (!this.hasDh) {
        this.showToast = true;
        let timeOut = setTimeout(() => {
          this.showToast = false;
          clearTimeout(timeOut);
        }, 2000);
        return;
      }
      uni.showLoading();
      uni.canvasToTempFilePath(
        {
          canvasId: this.canvasId,
          quality: 1,
          destWidth: 400,
          destHeight: 160,
          success: res => {
            let signImage = res.tempFilePath; //生成的签章地址
            this.submitcanvans(signImage.split(',')[1]);
          },
          fail: err => {
            uni.hideLoading();
          }
        },
        this
      );
    },
    submitcanvans(imgUrl) {
      this.ajax
        .imageBase64Upload({
          imageBase64: imgUrl,
          module: 'signature'
        })
        .then(res => {
          uni.hideLoading();
          this.$emit('ok', res.object.filePath);
          this.close();
        })
        .catch(e => {
          uni.hideLoading();
        });
    },
    close() {
      this.visible = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss">
.handwritten-signature-box {
  position: fixed;
  z-index: 999999;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.signature-box {
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  .signature-canvas-box {
    // background: url(../../../static/img/signatureArea.png) no-repeat;
    background-size: 100% 100%;
    border-radius: 10px;
    border: 1px dashed #d8d8d8;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    width: 100%;
    height: 100%;
  }
}
.operation-btn-box {
  position: absolute;
  bottom: 30rpx;
  right: 30rpx;
  padding: 20rpx 40rpx;
  .operation-btn-item {
    font-size: 28rpx;
    color: #333333;
    text-align: center;
    color: #666;
  }
}
.toast-box {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: block;
  box-sizing: border-box;
  pointer-events: none;
  font-size: 16px;
}
.simple-toast {
  z-index: 999;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  text-align: center;
  max-width: 80%;
}
.simple-toast_text {
  display: inline-block;
  vertical-align: middle;
  color: #fff;
  background-color: rgba(17, 17, 17, 0.7);
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 13px;
  text-align: center;
  max-width: 100%;
  word-break: break-all;
  white-space: normal;
}

@media screen and (orientation: portrait) {
  /*竖屏 css*/
  .header-box,
  .signature-box {
    position: absolute;
    width: calc(100vh); /*设置元素的宽为视图窗口高度*/
    transform: rotate(90deg); /*设置元素顺时针旋转正90度*/
    transform-origin: 0 100%; /*设置元素以右上角为原点进行旋转*/
    /*因为元素旋转后宽高是相互调换的，旋转后的元素的宽 =  未旋转时整个视图的高 */
  }
  .header-box {
    top: -44px;
    left: calc(100vw - 44px);
  }
  .signature-box {
    top: calc(-100vw + 44px);
    height: calc(100vw - 44px);
  }
  .simple-toast {
    position: fixed;
    transform-origin: 0 100%;
    transform: rotate(90deg);
    top: calc(50vw + 44px);
  }
}

@media screen and (orientation: landscape) {
  /*横屏 css*/
  .signature-box {
    height: calc(100vh - 44px);
  }
}
</style>
