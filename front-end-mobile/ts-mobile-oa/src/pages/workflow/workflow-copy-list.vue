<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="抄送给我的"></page-head>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        placeholder="请输入流程名称或发起人"
        :searchInput="true"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view class="contact_list">
          <view
            class="contact_item"
            v-for="row in dataList"
            :key="row.workflowNumber"
            @tap="chooseItem(row)"
            :class="{
              is_readed: row.readStatus == 1
            }"
          >
            <view class="contact_item_row">
              <text v-if="row.isPress == 1" class="contact_item_speed"
                >[催办]</text
              >
              <text
                v-if="row.urgencyLevel && row.urgencyLevel != 1"
                class="contact_item_urge"
                >[{{ $oaModule.getUrgencyLevel(row.urgencyLevel) }}]</text
              >
              <view class="contact_item_title">
                <text class="title">{{ row.workflowTitle }}</text>
              </view>
            </view>
            <view class="contact_item_row">
              <text class="contact_item_time">
                发起人:{{ row.createUserName }}
              </text>
              <text class="contact_item_time">
                {{ row.updateDate | formatTime }}
              </text>
            </view>
            <view class="contact_item_row">
              <text class="contact_item_time">
                抄送人:{{ row.copySendUserName }}
              </text>
              <text class="contact_item_time">
                {{ row.copySendDate | formatTime }}
              </text>
            </view>
            <view class="contact_item_row">
              <text class="contact_item_node">{{
                row.status === 2 ? '' : '当前节点：' + row.currentStepName
              }}</text>
              <text
                class="contact_item_status"
                :style="{
                  color: row.status === 1 ? '#fff' : '#999',
                  'background-color': row.status === 1 ? '#005BAC' : '#eee'
                }"
                >{{ row.status | statusFilter }}</text
              >
            </view>
          </view>
        </view>
      </mescroll>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      dataList: [], //列表数据
      type: ''
    };
  },
  onLoad() {},
  filters: {
    statusFilter(value) {
      let statusStr = '';
      switch (value) {
        case 1:
          statusStr = '在办';
          break;
        case 2:
          statusStr = '办结';
          break;
        case 3:
          statusStr = '强制结束';
          break;
        case 4:
          statusStr = '撤销';
          break;
      }
      return statusStr;
    }
  },
  methods: {
    async getListData(page, successCallback, errorCallback, keywords) {
      let _self = this;
      await _self.ajax
        .getCopyToMyWorkflowList({
          pageSize: page.size,
          pageNo: page.num,
          condition: keywords,
          sord: 'desc',
          sidx: 'inst.create_date'
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(row) {
      let _self = this;
      _self.dataList = _self.dataList.concat(row);
    },
    datasInit() {
      let _self = this;
      _self.dataList = [];
    },
    chooseItem(row) {
      let _self = this,
        pagePath = '';
      if (row.isNormal === 'N') {
        pagePath = '/pages/workflow/my-workflow-detail';
      } else {
        pagePath = row.mobileExaminePageUrl;
      }
      uni.setStorageSync('workflow_info', row);
      if (row.readStatus == 0) {
        this.ajax.handleMarkWorkflowReaded(row.copyId);
      }
      _self.$nextTick(() => {
        uni.navigateTo({
          url: `${pagePath}?formListPage=copyList&name=copy`
        });
      });
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  .mescroll-content {
    position: absolute;
    width: 100%;
    top: 44px;
    bottom: 0;
    .contact_item {
      padding: 22rpx 30rpx;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        height: 1px;
        background-color: #eee;
        left: 30rpx;
        right: 0;
      }
      &:last-child::after {
        height: 0;
      }
      &.is_readed .contact_item_node,
      &.is_readed .contact_item_title {
        color: #999;
      }
      .contact_item_row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }
      .contact_item_title {
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
        overflow: hidden;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 1;
        .title {
          width: 100%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
        }
      }
      .contact_item_time {
        font-size: 24rpx;
        color: #999;
        overflow: hidden;
        .contact_item_icon {
          color: #f59a23;
          padding-right: 10rpx;
          font-size: 28rpx;
        }
      }
      .contact_item_node {
        font-size: 28rpx;
        color: #666;
      }
      .contact_item_speed {
        color: #dd1f36;
      }
      .contact_item_urge {
        color: #f59a23;
      }
      .contact_item_speed,
      .contact_item_urge {
        font-size: 28rpx;
        font-weight: bold;
      }
      .contact_item_status {
        font-size: 24rpx;
        transform: scale(0.83);
        color: #999;
        background-color: #eee;
        padding: 2rpx 10rpx;
        border-radius: 8rpx;
      }
    }
  }
}
</style>
