<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="详情"></page-head>
    <scroll-view class="swiper_head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tab, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        :data-view-id="tab.viewId"
        :data-current="index"
        @click="ontabtap"
      >
        <text
          class="uni-tab-item-title"
          :class="tabIndex == index ? 'uni-tab-item-title-active' : ''"
          >{{ tab.name }}</text
        >
      </view>
    </scroll-view>
    <view class="content_wrap" v-if="showContent">
      <scroll-view
        class="content"
        scroll-y="true"
        :scroll-into-view="scrollViewId"
        :scroll-with-animation="true"
        @scroll="scroll"
      >
        <view class="form_wrap scroll-view-item" id="form">
          <view class="node_info">
            <view class="node_info_row">
              <text class="node_info_title">{{
                workflowInfo.workflowTitle
              }}</text>
            </view>
            <view class="node_info_row">
              <text v-if="workflowInfo.isPress === 1" class="node_info_speed"
                >[催办]</text
              >
              <text
                v-if="
                  workflowInfo.urgencyLevel && workflowInfo.urgencyLevel != 1
                "
                class="node_info_urge"
                >[{{
                  $oaModule.getUrgencyLevel(workflowInfo.urgencyLevel)
                }}]</text
              >
              <text class="node_info_time">{{
                workflowInfo.updateDate | formatTime
              }}</text>
            </view>
            <view class="node_info_row">
              <text class="node_info_node">{{
                workflowInfo.status === 2
                  ? ''
                  : '当前节点：' + workflowInfo.currentStepName
              }}</text>
              <text
                class="node_info_status"
                :class="{
                  node_info_status_blue: workflowInfo.statusName === '待我办理',
                  node_info_status_org:
                    workflowInfo.statusName === '退回上一步' ||
                    workflowInfo.statusName === '退回重办'
                }"
              >
                {{ workflowInfo.statusName }}
              </text>
            </view>
          </view>
          <view class="row-group" style="margin: 0;">
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">申请人</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  applicationInfo.applyEmpname
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">申请科室</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  applicationInfo.applyOrgname
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">联系人</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  applicationInfo.linktelePerson
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">联系方式</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  applicationInfo.linktelePhone
                }}</text>
              </view>
            </view>
          </view>
          <view class="row-group">
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">会议类型</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  applicationInfo.apptypeid
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">会议主题</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  applicationInfo.linktelePhone
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">参与人</text>
              </view>
              <view class="row_value">
                <text class="personNameStr">{{
                  applicationInfo.attendeeName
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">主持人</text>
              </view>
              <view class="row_value">
                <text class="personNameStr">{{
                  applicationInfo.emceeName
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">会议内容</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  applicationInfo.content
                }}</text>
              </view>
            </view>
          </view>
          <view
            class="row-group"
            v-for="(item, index) in applicationInfo.list"
            :key="index"
          >
            <view class="meeting_title">会议室({{ index + 1 }})</view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">开始时间</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  item.startTime | formatTime
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">结束时间</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  item.endTime | formatTime
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">会议地点</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  item.location + '-' + item.name
                }}</text>
              </view>
            </view>
          </view>
          <view class="row-group">
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">附件</text>
              </view>
              <view class="row_value">
                <view
                  class="fiel_list"
                  v-if="
                    JSON.stringify(applicationInfo) != '{}' &&
                      applicationInfo.fileList.length > 0
                  "
                >
                  <view
                    class="file_item"
                    v-for="(item, index) in applicationInfo.fileList"
                    :key="index"
                    @tap="downloadFile(item.id)"
                  >
                    <text
                      class="oa-icon"
                      :class="'oa-icon-' + $oaModule.formatFileType(item.type)"
                    ></text>
                    <text>{{ item.name }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="form_wrap scroll-view-item" id="opinion">
          <view class="meeting_title">审批意见</view>
          <view v-if="optionList.length > 0" class="option_list">
            <view
              class="option_item"
              v-for="(item, index) in optionList"
              :key="index"
            >
              <view class="option_item_icon">
                {{ item.actAssigneeName | nameFilter }}
              </view>
              <view class="option_item_info">
                <view class="option_item_approval_name"
                  >{{ item.approvalFiledName }}
                </view>
                <view class="option_item_top">
                  <view class="option_item_top_left">
                    <text class="option_item_name">{{
                      item.actAssigneeName
                    }}</text>
                    <text
                      class="option_item_response"
                      :class="
                        item.selectResponse === '【通过】'
                          ? 'option_item_assent'
                          : 'option_item_dissent'
                      "
                      >{{ item.selectResponse }}</text
                    >
                  </view>
                  <text class="option_item_time">{{
                    item.finishedDate | formatTime
                  }}</text>
                </view>
                <!-- <view class="option_item_type">{{item.wfStepName}}</view> -->
                <view class="option_item_content"
                  >审批内容：{{ item.remark }}</view
                >
              </view>
            </view>
          </view>
          <view class="nothing" v-else>
            <view class="img_content">
              <image
                class="nothing_img"
                src="../../static/img/nothing.png"
                mode="aspectFit"
              ></image>
            </view>
          </view>
        </view>
        <view class="form_wrap scroll-view-item" id="record">
          <view class="meeting_title">流程信息</view>
          <view class="task_history_wrap">
            <view class="task_history_list">
              <view
                v-for="(item, index) in taskHistoryList"
                :key="index"
                :class="index === 0 ? 'task_history_item_current' : ''"
              >
                <view
                  class="task_history_item"
                  :class="index === 0 ? 'task_history_item_current' : ''"
                  v-if="index === 0 && item.type === '1'"
                >
                  <view class="task_history_item_wrap">
                    <view class="task_history_item_top">
                      <text class="task_history_item_name">{{
                        '当前审批人：' + item.assigneeName
                      }}</text>
                    </view>
                    <view class="task_history_item_content">{{
                      '审批节点：' + item.wfStepName
                    }}</view>
                  </view>
                </view>
                <view
                  class="task_history_item"
                  :class="index === 0 ? 'task_history_item_current' : ''"
                  v-else
                >
                  <view class="task_history_item_wrap">
                    <view class="task_history_item_name">{{
                      item.wfStepName
                    }}</view>
                    <view class="task_history_item_top">
                      <text class="task_history_item_name">{{
                        item.actAssigneeName
                      }}</text>
                      <text
                        :class="
                          item.examineResult &&
                          item.examineResult === '【通过】'
                            ? 'task_history_item_assent'
                            : 'task_history_item_dissent'
                        "
                        >{{ item.examineResult }}</text
                      >
                      <text class="task_history_item_time">{{
                        item.finishedDate | formatTime
                      }}</text>
                    </view>
                    <!-- <view class="task_history_item_content">{{item.content}}</view> -->
                    <view class="task_history_item_content" v-if="item.remark"
                      >审批内容：{{ item.remark }}</view
                    >
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import { mapMutations } from 'vuex';
import Base64 from '@/common/js/base64.min.js';
import loginModule from '@/common/js/loginModule.js';
export default {
  components: {},
  data() {
    return {
      showContent: false,
      formTitle: '详情',
      formListTabIndex: '0',
      nameType: '',
      tabIndex: 0,
      tabBars: [
        {
          name: '申请详情',
          viewId: 'form'
        },
        {
          name: '审批意见',
          viewId: 'opinion'
        },
        {
          name: '流程信息',
          viewId: 'record'
        }
      ],
      scrollStatus: true, //点击状态，是否能点击
      scrollViewId: '',
      applicationInfo: {},
      optionList: [],
      taskHistoryList: []
    };
  },
  async onLoad(opt) {
    if (opt && opt.token && !this.$config.ENABLE_ACCOUNT_LOGIN) {
      let userInfo = await loginModule.loginInfo(opt);
      if (JSON.stringify(userInfo) != '{}') this.changeState(userInfo);
    }
    this.nameType = opt.name;
    this.fromPage = opt.fromPage;
    this.formListTabIndex = opt.formListTabIndex;
    if (opt.isMobile) {
      await this.getWorkflowData(opt.wfInstId, opt.status);
    } else {
      this.workflowInfo = uni.getStorageSync('workflow_info');
      await this.getData();
    }
  },
  methods: {
    ...mapMutations(['changeState']),
    //获取流程信息数据
    async getWorkflowData(wfInstId, status) {
      let _self = this;
      await _self.ajax
        .getWorkflowData({
          wfInstId: wfInstId,
          status: status //待办-1，办结-2
        })
        .then(async res => {
          _self.workflowInfo = res.object;
          await _self.getData();
        });
    },
    //获取数据
    async getData() {
      let _self = this;
      await _self.ajax
        .getMeetingDatas(_self.workflowInfo.businessId)
        .then(async res => {
          let data = res.object,
            meeting = {};
          if (data.length > 0) {
            let fileName = data[0].accessoryName
                ? data[0].accessoryName.split('#,#')
                : [],
              fileId = data[0].accessoryId
                ? data[0].accessoryId.split('#,#')
                : [];
            meeting.fileList = fileId.map((item, index) => {
              let nameList = fileName[index].split('.');
              return {
                id: item,
                name: fileName[index],
                type: nameList.length > 1 ? nameList[nameList.length - 1] : ''
              };
            });
            meeting.list = data.map(item => {
              return {
                startTime: item.startTime,
                endTime: item.endTime,
                location: item.location,
                name: item.name
              };
            });
            _self.applicationInfo = { ...data[0], ...meeting };
          }
          await _self.getApprovalOpinion();
        });
    },
    //获取审批意见
    async getApprovalOpinion() {
      let _self = this;
      await _self.ajax
        .getApprovalOpinion(_self.workflowInfo.wfInstanceId)
        .then(async res => {
          if (res.object.length > 0) {
            res.object.forEach(item => {
              _self.commentList.forEach(one => {
                if (item.approvalFiled === one.fieldName) {
                  _self.optionList.push({
                    ...item,
                    ...{ approvalFiledName: one.showName }
                  });
                }
              });
            });
          }
          await this.getTaskHisList();
        });
    },
    //获取流程信息
    async getTaskHisList() {
      let _self = this;
      await _self.ajax
        .getTaskHisList(_self.workflowInfo.wfInstanceId)
        .then(res => {
          _self.taskHistoryList = res.object;
          _self.showContent = true;
        });
    },
    //tab点击事件
    ontabtap(e) {
      let _self = this;
      // 200毫秒才能执行下次点击
      if (_self.scrollStatus) {
        _self.scrollStatus = false;
        let data = e.currentTarget.dataset;
        _self.scrollViewId = data.viewId;
        _self.tabIndex = data.current;
        setTimeout(() => {
          // 200毫秒才能执行下次点击
          _self.scrollStatus = true;
        }, 200);
      }
    },
    scroll(e) {},
    //查看附件详情
    downloadFile(id) {
      let _self = this;
      _self.ajax.getFiles({ idsStr: id }).then(res => {
        let filePath = `${
          _self.$config.ENABLE_FILE_PREVIEW
            ? _self.$config.DOCUMENT_BASE_HOST
            : _self.$config.BASE_HOST
        }/ts-document/attachment/downloadFile/${id}?fullfilename=${
          res.object[0].fileName
        }&source=mobile`;
        if (_self.$config.ENABLE_FILE_PREVIEW) {
          uni.navigateTo({
            url: `/pages/webview/webview?url=${
              _self.$config.BASE_HOST
            }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
          });
        } else {
          _self.$downloadFile.downloadFile(filePath);
        }
      });
    },
    getJumpPage() {
      let pagePath = '';
      switch (this.pagePramas.formListPage) {
        case 'access':
          pagePath = '/pages/workflow/workflow-access-list'; //流程查阅
          break;
        case 'copy':
          pagePath = '/pages/workflow/workflow-copy-list'; //抄送给我的
          break;
        default:
          pagePath = '/pages/workflow/my-workflow-list'; //我的申请
      }
      return pagePath;
    },
    //流程催办
    returnBack() {
      uni.removeStorageSync('workflow_info');
      this.$nextTick(() => {
        uni.redirectTo({
          url: `${this.getJumpPage()}?index=${this.formListTabIndex}`
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  .dis_flex {
    display: flex;
    justify-content: center;
  }
  .swiper_head {
    position: relative;
    width: 100%;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    /* #ifndef APP-PLUS */
    white-space: nowrap;
    /* #endif */
    /* flex-wrap: nowrap; */
    /* border-color: #cccccc;
			border-bottom-style: solid;
			border-bottom-width: 1px; */
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
    .uni-tab-item {
      /* #ifndef APP-PLUS */
      display: inline-block;
      /* #endif */
      flex-wrap: nowrap;
      width: 33.3333333%;
      padding-left: 34rpx;
      padding-right: 34rpx;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title {
        color: #555;
        font-size: 30rpx;
        height: 80rpx;
        line-height: 76rpx;
        flex-wrap: nowrap;
        display: block;
        box-sizing: border-box;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
      }
      .uni-tab-item-title-active {
        color: $theme-color;
        border-bottom: 2px solid $theme-color;
      }
    }
  }
  .meeting_title {
    margin: 20rpx 30rpx 10rpx;
    font-size: 28rpx;
    color: #999;
  }
  .content_wrap {
    flex: 1;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .content {
      flex: 1;
      overflow: hidden;
      .node_info {
        background-color: #fff;
        padding: 22rpx 30rpx;
        box-sizing: border-box;
        border-bottom: 1px solid #eee;
        .node_info_row {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }
        .node_info_title {
          font-size: 32rpx;
          color: #333333;
          font-weight: bold;
          overflow: hidden;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .node_info_time {
          font-size: 24rpx;
          color: #999;
          overflow: hidden;
          .node_info_icon {
            color: #f59a23;
            padding-right: 10rpx;
            font-size: 28rpx;
          }
        }
        .node_info_node {
          font-size: 28rpx;
          color: #666;
        }
        .node_info_speed {
          color: #dd1f36;
        }
        .node_info_urge {
          color: #f59a23;
        }
        .node_info_speed,
        .node_info_urge {
          font-size: 28rpx;
          font-weight: bold;
        }
        .node_info_status {
          font-size: 24rpx;
          color: #999;
          font-weight: bold;
        }
        .node_info_status_blue {
          color: #005bac;
        }
        .node_info_status_org {
          color: #f59a23;
        }
      }
      .row-group {
        margin-top: 20rpx;
        .row {
          width: 100%;
          background-color: #ffffff;
          position: relative;
          &::after {
            position: absolute;
            content: '';
            bottom: 0;
            left: 30rpx;
            right: 0;
            transform: scaleY(-0.5);
            height: 1px;
            background-color: #eee;
          }
          &:last-child::after {
            height: 0;
          }
          .row_lable {
            width: 200rpx;
            font-size: 30rpx;
            color: #999;
            padding: 22rpx 30rpx;
            box-sizing: border-box;
            position: relative;
            text-align: right;
            .row_lable_text {
              padding-right: 20rpx;
              box-sizing: border-box;
            }
          }
          .row_lable ~ .row_value {
            flex: 1;
            font-size: 30rpx;
            color: #333;
            padding: 22rpx 30rpx;
            padding-left: 0;
            box-sizing: border-box;
            text-align: left;
          }
        }
      }
      .fiel_list {
        .file_item {
          text-decoration: none;
          color: #005bac;
          background-color: #fff;
          display: flex;
          align-items: center;
          font-size: 30rpx;
          .oa-icon {
            font-size: 40rpx;
            margin-right: 10rpx;
          }
        }
      }
      .nothing {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #fff;
        .img_content {
          width: 300rpx;
          height: 300rpx;
          .nothing_img {
            width: 100%;
            height: 100%;
          }
        }
        .tips_text {
          color: #666666;
        }
        .addBtn {
          padding: 10rpx 20rpx;
          margin-top: 30rpx;
          width: 200rpx;
        }
      }
      .option_list {
        background-color: #fff;
        .option_item {
          box-sizing: border-box;
          position: relative;
          width: 100%;
          padding: 22rpx 30rpx 22rpx 10rpx;
          display: flex;
          align-items: flex-start;
          &::after {
            position: absolute;
            z-index: 10;
            right: 0;
            left: 0;
            height: 1px;
            bottom: 0;
            content: '';
            -webkit-transform: scaleY(0.5);
            transform: scaleY(0.5);
            background-color: #eeeeee;
          }
          .option_item_icon {
            width: 80rpx;
            height: 80rpx;
            margin: 0 20rpx;
            border-radius: 100%;
            color: #ffffff;
            text-align: center;
            line-height: 2.8;
            background-color: #005bac;
            font-size: 28rpx;
          }
          .option_item_info {
            flex: 1;
            .option_item_approval_name {
              font-size: 32rpx;
              font-weight: bold;
              color: #333;
            }
            .option_item_top {
              display: flex;
              align-items: flex-end;
              .option_item_top_left {
                flex: 1;
                .option_item_name {
                  font-size: 28rpx;
                  color: #666;
                }
                .option_item_assent {
                  color: #3aad73;
                }
                .option_item_dissent {
                  color: #f59a23;
                }
                .option_item_response {
                  font-size: 28rpx;
                }
              }
              .option_item_time {
                font-size: 24rpx;
                color: #999;
              }
            }
            .option_item_type {
              font-size: 28rpx;
              color: #666;
            }
            .option_item_content {
              font-size: 28rpx;
              color: #333;
            }
          }
        }
      }
      .task_history_wrap {
        padding: 20rpx 30rpx 20rpx 40rpx;
        background-color: #fff;
        .task_history_list {
          list-style-type: none;
          border-left: 4rpx dashed #ddd;
          padding: 0px;
          .task_history_item_current {
            &::after {
              background: #005bac !important;
            }
            .task_history_item_name,
            .task_history_item_content {
              color: #005bac !important;
            }
          }
          .task_history_item {
            position: relative;
            &::after {
              content: '';
              position: absolute;
              top: 10rpx;
              left: 0;
              transform: translateX(-18rpx);
              width: 30rpx;
              height: 30rpx;
              border-radius: 100%;
              background: #ddd;
            }
            .task_history_item_wrap {
              margin: 20rpx 0 20rpx 30rpx;
              .task_history_item_assent {
                color: #3aad73;
                font-size: 28rpx;
              }
              .task_history_item_dissent {
                color: #f59a23;
                font-size: 28rpx;
              }
              .task_history_item_name {
                font-size: 28rpx;
                color: #666;
                padding-right: 20rpx;
              }
              .task_history_item_time {
                font-size: 24rpx;
                color: #999;
                float: right;
              }
              .task_history_item_content {
                font-size: 28rpx;
                color: #666;
              }
            }
          }
        }
      }
    }
    .theme-color {
      color: $theme-color !important;
    }
    .btn_wrap {
      background-color: #ffffff;
      box-shadow: 0 1px 6px #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      z-index: 10;
      .btn_item {
        height: 90rpx;
        flex: 1;
        box-sizing: border-box;
        text-align: center;
        font-size: 32rpx;
        position: relative;
        background-color: transparent;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: center;
        &::after {
          border: 0;
          top: 20rpx;
          bottom: 20rpx;
          right: 0;
          left: unset;
          transform: scaleX(-0.5);
          width: 1px;
          height: unset;
          background-color: #ccc;
        }
        &:last-child::after {
          width: 0;
        }
        .oa-icon {
          margin: 0 6rpx;
          font-size: 36rpx;
        }
      }
    }
  }
}
</style>
