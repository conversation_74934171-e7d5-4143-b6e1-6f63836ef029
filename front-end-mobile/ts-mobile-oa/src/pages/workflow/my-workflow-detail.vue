<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="详情"></page-head>
    <wf-tab-bar v-if="showContent" :tabBarList="tabBars" v-model="tabIndex" />
    <view class="content_wrap" v-if="showContent">
      <scroll-view
        class="content"
        scroll-y="true"
        :scroll-into-view="scrollViewId"
        :scroll-with-animation="true"
        @scroll="scroll"
      >
        <view class="form_wrap scroll-view-item" id="form">
          <wf-head-info :workflowInfo="workflowInfo"></wf-head-info>
          <form-check
            ref="form"
            :formTemplate="formTemplate"
            :formDatas="formDatas"
          ></form-check>
        </view>
        <view class="form_wrap scroll-view-item" id="opinion">
          <view class="form_title">审批意见</view>
          <wf-comment-list
            v-if="optionList.length > 0"
            :commentList="optionList"
          ></wf-comment-list>
          <view class="nothing" v-else>
            <view class="img_content">
              <image
                class="nothing_img"
                src="../../static/img/nothing.png"
                mode="aspectFit"
              ></image>
            </view>
          </view>
        </view>
        <view class="form_wrap scroll-view-item" id="record">
          <view class="form_title">流程信息</view>
          <view class="task_history_wrap">
            <data-process-history
              :processHistoryList="taskHistoryList"
            ></data-process-history>
          </view>
        </view>
        <view
          class="form-wrap scroll-view-item"
          id="copyToUser"
          v-if="taskCopytoUserList.length"
        >
          <view class="form_title">抄送信息</view>
          <view class="task_history_wrap">
            <data-copyToUser-list
              :processHistoryList="taskCopytoUserList"
            ></data-copyToUser-list>
          </view>
        </view>
      </scroll-view>
      <view class="btn_wrap" v-if="showContent">
        <view class="bottom_btn" v-if="workflowType === 'myDoing'">
          <button
            v-if="workflowDefinition && workflowDefinition.isCancel != '0'"
            class="btn_item"
            @tap="formRescind"
          >
            <text class="oa-icon oa-icon-liuchengtuihui grey-color"></text>
            撤销
          </button>
          <button class="btn_item" @tap="formUrge" v-if="showUrgingBtn">
            <text class="oa-icon oa-icon-cuiban warning-color"></text>
            催办
          </button>
        </view>
        <view class="bottom_btn" v-else-if="workflowType === 'myReturn'">
          <button class="btn_item" @tap="formDelete">
            <text class="oa-icon oa-icon-shanchu grey-color"></text>
            删除
          </button>
          <button class="btn_item" @tap="formRelaunch">
            <text class="oa-icon oa-icon-zhuanfa theme-color"></text>
            重新发起
          </button>
        </view>
        <view class="bottom_btn" v-else-if="workflowType === 'accessDoing'">
          <button class="btn_item" @tap="formEnforceEnd">
            <text class="oa-icon oa-icon-guanbi1 warning-color"></text>
            强制结束
          </button>
        </view>
        <view class="bottom_btn" v-else-if="workflowType === 'myDone'">
          <button class="btn_item" @tap="formStartAgain">
            <text class="oa-icon oa-icon-faqishenqing1 theme-color"></text>
            再次发起
          </button>
        </view>
        <view class="bottom_btn" v-else-if="workflowType === 'copy'">
          <button class="btn_item" @tap="formCopy">
            <text class="oa-icon oa-icon-tijiao1 theme-color"></text>
            抄送
          </button>
        </view>
      </view>
    </view>
    <view
      class="mainPage"
      v-if="
        workflowInfo &&
          (workflowInfo.cloudTemplateId == '1' ||
            workflowInfo.cloudTemplateId == '2') &&
          showContent
      "
    >
      <view class="file_item" @tap="downloadFile(formDatas['page_office_id'])"
        >查看正文</view
      >
    </view>
    <view class="no-permission-box" v-if="showContent != null && !showContent">
      <image
        class="no-permission-img"
        mode="aspectFit"
        src="../../static/img/noPermission.png"
      ></image>
      <view class="no-permission-text">暂无查看权限</view>
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import FormCheck from '@/components/form-components/form-check.vue';
import WfTabBar from './components/wf-tab-bar.vue';
import WfHeadInfo from './components/wf-head-info.vue';
import WfCommentList from './components/wf-comment-list.vue';

import Base64 from '@/common/js/base64.min.js';
import loginModule from '@/common/js/loginModule.js';
import { operationItemColumn } from './mixins/dictionary.js';

export default {
  components: {
    FormCheck,
    WfTabBar,
    WfHeadInfo,
    WfCommentList
  },
  computed: {
    ...mapState(['empcode'])
  },
  data() {
    return {
      showUrgingBtn: false,
      showContent: null,
      workflowType: '',
      formListPage: '',
      formListTabIndex: 0,
      pagePramasStr: '',
      workflowInfo: {},
      tabIndex: 0,
      tabBars: [
        {
          name: '申请详情',
          viewId: 'form'
        },
        {
          name: '审批意见',
          viewId: 'opinion'
        },
        {
          name: '流程信息',
          viewId: 'record'
        },
        {
          name: '抄送信息',
          viewId: 'copyToUser'
        }
      ],
      formTemplate: [],
      formId: '',
      nodeCondition: {},
      formDatas: {},
      scrollViewId: '',
      optionList: [],
      taskHistoryList: [],
      taskCopytoUserList: [],
      scrollVal: 0, //滑动的值
      scrollStatus: true, //点击状态，是否能点击
      nodeHeight: [], //存储categoryList的top
      windowHeight: 0,
      windowTop: 0,
      commentList: [],
      returnBtn: false,
      workflowDefinition: {}
    };
  },
  watch: {
    tabIndex(newVal) {
      this.scrollViewId = this.tabBars[newVal].viewId;
    }
  },
  async onLoad(opt) {
    await this.getWorkflowUrgingConfiguration();
    this.pagePramasStr = this.$common.convertObj(opt);
    if (opt && opt.token && !this.$config.ENABLE_ACCOUNT_LOGIN) {
      let userInfo = await loginModule.loginInfo(opt);
      if (JSON.stringify(userInfo) != '{}') this.changeState(userInfo);
    }
    this.formListPage = opt.formListPage;
    if (opt.isMobile) {
      if (opt.status === '3') {
        this.workflowType = 'myReturn';
      }
      if (opt.name == 'myDoing') {
        this.formListTabIndex = 0;
      } else if (opt.name == 'myReturn') {
        this.formListTabIndex = 1;
      } else if (opt.name == 'myDone') {
        this.formListTabIndex = 2;
      } else if (opt.name == 'checkDetail') {
        this.workflowType = 'checkDetail';
      }
      await this.getWorkflowData(opt.wfInstId, opt.status);
    } else {
      this.workflowType = opt.name;
      this.formListTabIndex = opt.formListTabIndex;
      this.workflowInfo = uni.getStorageSync('workflow_info');
      this.getFormDatas();
    }
    let isHideContent = await this.getIsHideContent();
    if (
      isHideContent == 1 &&
      this.workflowInfo.createUser == this.empcode &&
      this.workflowType != 'myReturn' &&
      this.workflowType != 'accessDoing'
    ) {
      this.showContent = false;
      return;
    } else {
      this.showContent = true;
    }
  },
  filters: {
    copyNameFileter(row) {
      let copy = [];
      row.forEach(i => {
        copy.push(i.username);
      });
      return copy.join(',');
    }
  },
  methods: {
    ...mapMutations(['changeState']),
    async getWorkflowUrgingConfiguration() {
      let res = await this.ajax.getDictItemByTypeCode({
        typeCode: 'CUSTOM_CODE'
      });
      if (!res.object) {
        this.showUrgingBtn = false;
      } else {
        let showUrgingConfig = res.object.find(
          e => e.itemCode == 'DISP_WORKFLOW_URGING'
        );
        this.showUrgingBtn =
          showUrgingConfig == undefined || showUrgingConfig.itemNameValue == 1
            ? true
            : false;
      }
    },
    //表单信息是否对发起人隐藏
    async getIsHideContent() {
      let workflowDefinition = 0;
      await this.ajax
        .getWorkflowDefinition(this.workflowInfo.workflowNo)
        .then(res => {
          workflowDefinition = res.object;
          this.workflowDefinition = res.object;
        });
      return workflowDefinition.isHideContent;
    },
    //获取流程信息数据
    async getWorkflowData(wfInstanceId, status) {
      await this.ajax
        .getWorkflowData({
          wfInstId: wfInstanceId,
          status: status //待办-1，办结-2
        })
        .then(res => {
          this.workflowInfo = res.object;
          this.getFormDatas();
        });
    },
    //获取表单数据
    async getFormDatas() {
      await this.ajax
        .getFormDatas({
          id: this.workflowInfo.businessId,
          wfDefinitionId: this.workflowInfo.wfDefinitionId
        })
        .then(async res => {
          this.formDatas = res['object'][0];
          await this.getFormFiledInfo();
        });
    },
    //获取表单字段读写
    async getFormFiledInfo() {
      await this.ajax
        .getFiledPermissions({
          wfDefinitionId: this.workflowInfo.wfDefinitionId,
          wfStepId: this.workflowInfo.currentStepNo
        })
        .then(async res => {
          await this.getFormTemplate(res.object);
        });
    },
    //获取表单设计模板
    async getFormTemplate(row) {
      await this.ajax
        .getFormTemplate(this.workflowInfo.wfDefinitionId)
        .then(async res => {
          let dataList = res.object.toaFieldSetList,
            dataSort = JSON.parse(res.object.formTemplate),
            dataSortList = [];
          dataSort.forEach(item => {
            for (let i = 0; i < dataList.length; i++) {
              if (item == dataList[i].fieldName) {
                dataSortList.push(dataList[i]);
                break;
              }
            }
          });

          //格式化子表单数据
          let childFormTempList = dataSortList.filter(
            item => item.fieldType == 'childForm'
          );
          if (childFormTempList.length) {
            let apiList = [],
              fileChildFormIndexList = [];
            childFormTempList.map((temp, index) => {
              apiList.push(
                // 获取 子表单详情
                this.ajax.getChildFormDetailById(temp.tableId).then(res => {
                  if (res.success) {
                    let dataIndex = dataSortList.findIndex(
                      item => item.fieldName == temp.fieldName
                    );
                    dataSortList[dataIndex].childFormDetail = res.object;
                    dataSortList[dataIndex].childFormColumns = res.object.fields
                      .filter(item => item.pcShow)
                      .map(item => {
                        const tWidth = this.$common.measureText(item.remark);
                        let itemSetting = {
                          title: item.remark,
                          key: item.fieldName,
                          fieldType: item.fieldType,
                          width: (tWidth > 50 ? tWidth + 20 : 50) + 'px'
                        };
                        item.fieldType == 'FILE' &&
                          fileChildFormIndexList.push({
                            dataIndex,
                            formKey: temp.fieldName
                          });
                        return itemSetting;
                      });
                  }
                }),
                // 获取子表单数据
                this.ajax
                  .getChildFormDataById({
                    tableId: temp.tableId,
                    fieldName: temp.fieldName,
                    businessId: this.workflowInfo.businessId
                  })
                  .then(res => {
                    if (res.success) {
                      this.formDatas[temp.fieldName] = res.object;
                    }
                  })
              );
            });
            await Promise.all(apiList);
            apiList = [];
            // 获取附件数据
            fileChildFormIndexList.map(({ dataIndex, formKey }) => {
              let dataList = this.formDatas[formKey] || [],
                fileItem =
                  dataSortList[dataIndex].childFormColumns.find(
                    item => item.fieldType == 'FILE'
                  ) || {};

              dataList.map(data => {
                apiList.push(
                  this.ajax
                    .getFileAttachmentByBusinessId({
                      businessId: data[fileItem.key]
                    })
                    .then(res => {
                      if (!res.success) {
                        return;
                      }
                      let fileList = res.object.map(file => {
                        let { id, realPath, fileExtension } = file,
                          filePath = `${this.$config.DOCUMENT_BASE_HOST}${realPath}?fullfilename=${id}.${fileExtension}&source=mobile`,
                          href = `/pages/webview/webview?url=${
                            this.$config.BASE_HOST
                          }/ts-preview/onlinePreview?url=${Base64.encode(
                            filePath
                          )}`;

                        return `<view class="child-form-file-item" data-href="${href}" >
                          <text class="oa-icon oa-icon-${this.$oaModule.formatFileType(
                            file.fileExtension
                          )}"></text>
                          <text>${file.originalName}</text>
                        </view>`;
                      });
                      data[fileItem.key + 'Html'] = fileList.join('');
                      fileItem.format = function(row, { key }) {
                        return row[key + 'Html'];
                      };
                      fileItem.event = {
                        click: function(event, rowData, prop) {
                          let target = event.target;
                          if (
                            !target.classList.contains('child-form-file-item')
                          ) {
                            target = target.parentNode;
                            if (
                              !target.classList.contains('child-form-file-item')
                            ) {
                              return;
                            }
                          }

                          let url = target.getAttribute('data-href');
                          url &&
                            uni.navigateTo({
                              url
                            });
                        }
                      };
                    })
                );
              });
            });
            await Promise.all(apiList);
          }

          // 格式化手术项目数据
          let operationItemList = dataSortList.filter(
            item => item.fieldType == 'operationItem'
          );
          if (operationItemList.length) {
            operationItemList.forEach(temp => {
              let operationItemData = JSON.parse(
                this.formDatas[temp.fieldName]
              );
              this.formDatas[temp.fieldName] = operationItemData;
              let dataIndex = dataSortList.findIndex(
                item => item.fieldName == temp.fieldName
              );
              dataSortList[dataIndex].columns = operationItemColumn
                .filter(col => operationItemData[0][col.key] !== undefined)
                .map(item => {
                  const tWidth = this.$common.measureText(item.title);
                  let itemSetting = {
                    width: (tWidth > 50 ? tWidth + 20 : 50) + 'px',
                    ...item
                  };
                  return itemSetting;
                });
            });
          }

          // hrpHyperlink不为地址 则渲染附件组件
          dataSortList.forEach(item => {
            if (item.fieldType === 'hrpHyperlink') {
              let hrpHyperlinkValue = this.formDatas[item.fieldName];
              if (
                hrpHyperlinkValue &&
                hrpHyperlinkValue.indexOf('http:') === -1
              ) {
                item.fieldType = 'file';
              }
            }
          });
          let field = this.fieldFilter(row, dataSortList, this.formDatas);
          //判断是否已有发文文号，有则不能撤销
          this.returnBtn = this.formDatas[field.documentNoKey] ? false : true;
          if (field.file.length > 0) {
            field.file.forEach(async item => {
              await this.getFiles(item.fieldName);
            });
          }
          this.formTemplate = field.read;
          this.commentList = field.comment;
          this.formId = res.object.id;
          this.nodeCondition = {
            taskId: this.workflowInfo.taskId,
            id: res.object.id
          };
          if (res.object.variables.length > 0) {
            res.object.variables.forEach(item => {
              this.nodeCondition[item.varName] = this.formDatas[item.fieldName];
            });
          }
          console.log(this.formTemplate);
          console.log(this.formDatas);
          await this.getApprovalOpinion();
        });
    },
    fieldFilter(controlList, dataList, data) {
      let readList = [],
        writeList = [],
        commentList = [],
        fileList = [],
        documentNoKey = '';
      dataList.forEach(item => {
        controlList.forEach(one => {
          if (item.fieldName === one.fieldName) {
            //isShow 控制节点是否显示字段
            //if(item.fieldName === one.fieldName && one.isShow === 1){
            if (one.isEditor === 1) {
              //可编辑
              item.isRequired = one.isRequired === 1 ? true : false;
              writeList.push(item);
              if (data[one.fieldName] != null && item.fieldType != 'comment') {
                readList.push(item);
                if (item.fieldType === 'file') {
                  fileList.push(item);
                } else if (item.fieldType === 'FWWH') {
                  //发文文号
                  documentNoKey = item.fieldName;
                }
              }
            } else if (one.isEditor === 2) {
              //只读，不可编辑
              if (item.fieldType != 'comment') {
                readList.push(item);
                if (item.fieldType === 'file') {
                  fileList.push(item);
                } else if (item.fieldType === 'FWWH') {
                  //发文文号
                  documentNoKey = item.fieldName;
                }
              }
            }
            if (item.fieldType == 'comment') {
              commentList.push(item);
            }
          }
        });
      });
      let fieldList = {
        read: readList,
        write: writeList,
        comment: commentList,
        file: fileList,
        documentNoKey: documentNoKey
      };
      return fieldList;
    },
    //获取附件
    async getFiles(fieldName) {
      await this.ajax
        .getFiles({
          idsStr: this.formDatas[fieldName]
        })
        .then(res => {
          this.formDatas[fieldName] = res.object;
        });
    },
    //获取审批意见
    async getApprovalOpinion() {
      await this.ajax
        .getApprovalOpinion(this.workflowInfo.wfInstanceId)
        .then(async res => {
          if (res.object.length > 0) {
            res.object.forEach(item => {
              this.commentList.forEach(one => {
                if (item.approvalFiled === one.fieldName) {
                  if (one.hideApprovalTime) {
                    item.finishedDate = '';
                  }
                  this.optionList.push({
                    ...item,
                    ...{ approvalFiledName: one.showName, fileList: [] }
                  });
                }
              });
            });
          }
          await this.getTaskFileList();
        });
    },
    //获取流程节点附件
    async getTaskFileList() {
      let _self = this;
      await _self.ajax
        .getTaskFileList({
          wfInstanceId: _self.workflowInfo.wfInstanceId,
          pageNo: 1,
          pageSize: 999
        })
        .then(async res => {
          res.rows.forEach(row => {
            for (let i = 0; i < _self.optionList.length; i++) {
              if (row.taskId == _self.optionList[i].taskId) {
                row.fileUrl = row.fileUrl.replace(/\\/g, '/');

                row.fileId = row.fileUrl.split('/')[0];
                row.fileExtension = row.fileUrl.split('.')[1];
                _self.optionList[i].fileList.push(row);
                break;
              }
            }
          });
          await _self.getTaskHisList();
          await _self.getCopyUserList();
        });
    },
    //获取流程信息
    async getTaskHisList() {
      await this.ajax
        .getTaskHisList({
          wfInstId: this.workflowInfo.wfInstanceId,
          pageNo: 1,
          pageSize: 10000,
          sidx: 'finished_date',
          sord: 'desc'
        })
        .then(res => {
          this.taskHistoryList = this.$oaModule.taskHisList(res.rows);
        });
    },
    //获取抄送信息
    async getCopyUserList() {
      await this.ajax
        .getCopyUserList({
          wfInstId: this.workflowInfo.wfInstanceId,
          pageNo: 1,
          pageSize: 10000,
          sidx: 'finished_date',
          sord: 'desc'
        })
        .then(res => {
          this.taskCopytoUserList = res.object;
        });
    },
    scroll(e) {},
    //查看附件详情
    downloadFile(id) {
      this.ajax
        .getFiles({
          idsStr: id
        })
        .then(res => {
          filePath = `${
            this.$config.ENABLE_FILE_PREVIEW
              ? this.$config.DOCUMENT_BASE_HOST
              : this.$config.BASE_HOST
          }/ts-document/attachment/downloadFile/${id}?fullfilename=${
            res.object[0].fileName
          }&source=mobile`;
          if (this.$config.ENABLE_FILE_PREVIEW) {
            uni.navigateTo({
              url: `/pages/webview/webview?url=${
                this.$config.BASE_HOST
              }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
            });
          } else {
            this.$downloadFile.downloadFile(filePath);
          }
        });
    },
    //流程撤销
    formRescind() {
      if (this.returnBtn) {
        uni.navigateTo({
          url: `/pages/workflow/operation/workflow-rescind`
        });
      } else {
        uni.showToast({
          icon: 'none',
          duration: 2500,
          title: '该发文流程文件已套红，不能进行撤销操作'
        });
      }
    },
    //流程催办
    formUrge() {
      uni.navigateTo({
        url: `/pages/workflow/operation/workflow-urge`
      });
    },
    //重新发起
    formRelaunch() {
      uni.navigateTo({
        url: `/pages/workflow/operation/reinit-custom-workflow`
      });
    },
    //强制结束
    formEnforceEnd() {
      uni.navigateTo({
        url: `/pages/workflow/operation/workflow-end`
      });
    },
    //再次发起
    formStartAgain() {
      let pagePramas = {
        wfDefinitionId: this.workflowInfo.wfDefinitionId,
        workflowNo: this.workflowInfo.workflowNo,
        formId: this.formId,
        businessId: this.workflowInfo.businessId,
        btnType: 'startAgain'
      };
      uni.navigateTo({
        url: `/pages/workflow/init-custom-workflow?${this.$common.convertObj(
          pagePramas
        )}`
      });
    },
    //删除
    formDelete() {
      let _self = this;
      uni.showModal({
        title: '提示',
        content: '确定要删除该流程数据吗?',
        success: function(res) {
          if (res.confirm) {
            _self.ajax
              .handleDeleteWorkflow({
                wfDefinitionId: _self.workflowInfo.wfDefinitionId,
                businessId: _self.workflowInfo.businessId
              })
              .then(e => {
                uni.showToast({
                  title: '删除成功',
                  icon: 'none',
                  complete: () => {
                    setTimeout(() => {
                      uni.removeStorageSync('workflow_info');
                      _self.returnBack();
                    }, 1000);
                  }
                });
              });
          }
        }
      });
    },
    //抄送
    formCopy() {
      uni.navigateTo({
        url: `/pages/workflow/operation/workflow-copy?${this.pagePramasStr}`
      });
    },
    //返回上一层
    returnBack() {
      let pagePath = '';
      uni.removeStorageSync('workflow_info');
      if (this.workflowType == 'checkDetail') {
        const pages = getCurrentPages(); //获取页面栈
        if (pages.length === 1) {
          //如果只有一个调用原生js
          history.back();
        } else {
          uni.navigateBack();
        }
      } else {
        switch (this.formListPage) {
          case 'myApplyList':
            pagePath = `/pages/workflow/my-workflow-list`;
            break;
          case 'accessList':
            pagePath = '/pages/workflow/workflow-access-list';
            break;
          case 'copyList':
            pagePath = '/pages/workflow/workflow-copy-list';
            break;
          default:
            pagePath = `/pages/workflow/my-workflow-list`;
        }
        uni.redirectTo({
          url: `${pagePath}${
            this.formListTabIndex != undefined
              ? `?index=${this.formListTabIndex}`
              : ''
          }`
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .swiper_head {
    position: relative;
    width: 100%;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    /* #ifndef APP-PLUS */
    white-space: nowrap;
    /* #endif */
    /* flex-wrap: nowrap; */
    /* border-color: #cccccc;
			border-bottom-style: solid;
			border-bottom-width: 1px; */
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
    .uni-tab-item {
      /* #ifndef APP-PLUS */
      display: inline-block;
      /* #endif */
      flex-wrap: nowrap;
      width: 33.3333333%;
      padding-left: 34rpx;
      padding-right: 34rpx;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title {
        color: #555;
        font-size: 30rpx;
        height: 80rpx;
        line-height: 76rpx;
        flex-wrap: nowrap;
        display: block;
        box-sizing: border-box;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
      }
      .uni-tab-item-title-active {
        color: $theme-color;
        border-bottom: 2px solid $theme-color;
      }
    }
  }
  .form_title {
    color: #999;
    padding: 10rpx 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
    background-color: #f7f8f8;
  }
  .content_wrap {
    flex: 1;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .content {
      flex: 1;
      overflow: hidden;
      width: inherit;
      background-color: #fff;
      //margin-bottom: 90rpx;
      .node_info {
        padding: 22rpx 30rpx;
        box-sizing: border-box;
        border-bottom: 1px solid #eee;
        .node_info_row {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }
        .node_info_title {
          font-size: 32rpx;
          color: #333333;
          font-weight: bold;
          overflow: hidden;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .node_info_time {
          font-size: 24rpx;
          color: #999;
          overflow: hidden;
          .node_info_icon {
            color: #f59a23;
            padding-right: 10rpx;
            font-size: 28rpx;
          }
        }
        .node_info_node {
          font-size: 28rpx;
          color: #666;
        }
        .node_info_speed {
          color: #dd1f36;
        }
        .node_info_urge {
          color: #f59a23;
          flex: 1;
        }
        .node_info_speed,
        .node_info_urge {
          font-size: 28rpx;
          font-weight: bold;
        }
        .node_info_status {
          font-size: 24rpx;
          color: #999;
          font-weight: bold;
        }
        .node_info_status_blue {
          color: #005bac;
        }
        .node_info_status_org {
          color: #f59a23;
        }
      }
      .nothing {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        .img_content {
          width: 300rpx;
          height: 300rpx;
          .nothing_img {
            width: 100%;
            height: 100%;
          }
        }
        .tips_text {
          color: #666666;
        }
        .addBtn {
          padding: 10rpx 20rpx;
          margin-top: 30rpx;
          width: 200rpx;
        }
      }
      .option_item {
        box-sizing: border-box;
        position: relative;
        width: 100%;
        padding: 22rpx 30rpx 22rpx 10rpx;
        display: flex;
        align-items: flex-start;
        &::after {
          position: absolute;
          z-index: 10;
          right: 0;
          left: 0;
          height: 1px;
          bottom: 0;
          content: '';
          -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
          background-color: #eeeeee;
        }
        .option_item_icon {
          width: 80rpx;
          height: 80rpx;
          margin: 0 20rpx;
          border-radius: 100%;
          color: #ffffff;
          text-align: center;
          line-height: 2.8;
          background-color: #005bac;
          font-size: 28rpx;
        }
        .option_item_info {
          flex: 1;
          .option_item_approval_name {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
          }
          .option_item_top {
            display: flex;
            align-items: flex-end;
            .option_item_top_left {
              flex: 1;
              .option_item_name {
                font-size: 28rpx;
                color: #666;
              }
              .option_item_assent {
                color: #3aad73;
              }
              .option_item_dissent {
                color: #f59a23;
              }
              .option_item_response {
                font-size: 28rpx;
              }
            }
            .option_item_time {
              font-size: 24rpx;
              color: #999;
            }
          }
          .option_item_type {
            font-size: 28rpx;
            color: #666;
          }
          .option_item_content {
            font-size: 28rpx;
            color: #333;
          }
        }
      }
      .task_history_wrap {
        padding: 20rpx 30rpx 20rpx 40rpx;
        .task_history_list {
          list-style-type: none;
          border-left: 4rpx dashed #ddd;
          padding: 0px;
          .task_history_item_current {
            &::after {
              background: #005bac !important;
            }
            .task_history_item_name,
            .task_history_item_content {
              color: #005bac !important;
            }
          }
          .task_history_item {
            position: relative;
            &::after {
              content: '';
              position: absolute;
              top: 10rpx;
              left: 0;
              transform: translateX(-18rpx);
              width: 30rpx;
              height: 30rpx;
              border-radius: 100%;
              background: #ddd;
            }
            .task_history_item_wrap {
              margin: 20rpx 0 20rpx 30rpx;
              .task_history_item_step_name {
                font-size: 28rpx;
                color: #666;
                padding-right: 20rpx;
              }
              .task_history_item_assent {
                color: #3aad73;
                font-size: 28rpx;
              }
              .task_history_item_dissent {
                color: #f59a23;
                font-size: 28rpx;
              }
              .task_history_item_name {
                font-size: 28rpx;
                color: #666;
                padding-right: 20rpx;
              }
              .task_history_item_time {
                font-size: 24rpx;
                color: #999;
                float: right;
              }
              .task_history_item_content {
                font-size: 28rpx;
                color: #666;
                .content-more {
                  font-size: 28rpx;
                  margin-right: 10rpx;
                }
              }
            }
          }
        }
      }
      /deep/ .child-form-file-item {
        overflow: hidden;
        display: -webkit-box; //特别显示模式
        -webkit-line-clamp: 2; //行数
        line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
      }
    }
  }

  .theme-color {
    color: $theme-color !important;
  }
  .grey-color {
    color: $uni-text-color-subtitle !important;
  }
  .warning-color {
    color: $uni-color-warning !important;
  }
  .btn_wrap {
    z-index: 10;
    .bottom_btn {
      background-color: #ffffff;
      box-shadow: 0 1px 6px #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      z-index: 10;
      .btn_item {
        height: 90rpx;
        flex: 1;
        box-sizing: border-box;
        text-align: center;
        font-size: 32rpx;
        position: relative;
        background-color: transparent;
        color: #333;
        display: flex;
        align-items: center;
        justify-content: center;
        &::after {
          border: 0;
          top: 20rpx;
          bottom: 20rpx;
          right: 0;
          left: unset;
          transform: scaleX(-0.5);
          width: 1px;
          height: unset;
          background-color: #ccc;
        }
        &:last-child::after {
          width: 0;
        }
        .oa-icon {
          margin: 0 6rpx;
          font-size: 36rpx;
        }
      }
    }
  }
  .mainPage {
    font-size: 26rpx;
    color: #ffffff;
    text-align: right;
    position: fixed;
    right: 0;
    top: 35%;
    background-color: #75a5f2;
    padding: 10rpx 20rpx;
    border-radius: 40rpx 0 0 40rpx;
    z-index: 2;
    .file_item {
      text-decoration: none;
      color: #ffffff;
    }
  }
  .fiel_list {
    .file_item {
      text-decoration: none;
      color: #005bac;
      font-size: 30rpx;
      background-color: #fff;
      display: flex;
      align-items: center;
      .oa-icon {
        font-size: 40rpx;
        margin-right: 10rpx;
      }
    }
  }
  .no-permission-box {
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, calc(-50% - 45px));
    .no-permission-img {
      width: 420rpx;
      height: 230rpx;
    }
    .no-permission-text {
      color: #b4b8bf;
      font-size: 28rpx;
    }
  }
}
</style>
