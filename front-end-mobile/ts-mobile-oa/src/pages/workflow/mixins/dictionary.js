export const urgencyList = [
  {
    text: '一般',
    value: 1
  },
  {
    text: '加急',
    value: 2
  },
  {
    text: '急件',
    value: 3
  },
  {
    text: '特急',
    value: 4
  }
];
export const multiInstanceType = {
  1: {
    text: '串行审批',
    explainText: '（该节点任意一个审批人进行处理后，流程扭转到下一节点）',
    inputType: 'radio'
  },
  2: {
    text: '并行审批',
    explainText: '（该节点审批人全部进行处理后，流程扭转到下一节点）',
    inputType: 'radio'
  },
  3: {
    text: '多节点串行审批',
    explainText: '（该节点任意一个审批人进行处理后，流程扭转到下一节点）',
    inputType: 'checkbox'
  },
  4: {
    text: '多节点并行审批',
    explainText: '（该节点审批人全部进行处理后，流程扭转到下一节点）',
    inputType: 'checkbox'
  },
  5: {
    text: '有序并行审批',
    explainText:
      '（按照办理人所选顺序进行审批，当该节点审批人全部进行处理后，流程扭转到下一节点）',
    inputType: 'radio'
  },
  6: {
    text: '并行审批-不阻断流程',
    inputType: 'radio'
  },
  7: {
    text: '并行审批-汇合',
    inputType: 'radio'
  }
};

export const operationItemColumn = [
  {
    title: '序号',
    key: 'index'
  },
  {
    title: '授权结果',
    key: 'auditFlag',
    format: val => {
      return val == 1 ? '正常授权' : '停止授权';
    }
  },
  {
    title: '原因',
    key: 'auditScr'
  },
  {
    title: '手术类型',
    key: 'quaAuthTypeName'
  },
  {
    title: '手术编码',
    key: 'itemCode'
  },
  {
    title: '申请授权名称',
    key: 'itemName'
  },
  {
    title: '院内手术级别',
    key: 'authLvHospName'
  },
  {
    title: '国家手术级别',
    key: 'authLvNatName'
  },
  {
    title: '是否新技术项目',
    key: 'isNewTech'
  },
  {
    title: '是否限制性项目',
    key: 'isRstdTech'
  }
];
