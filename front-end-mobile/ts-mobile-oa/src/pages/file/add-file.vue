<template>
  <view class="ts-content" v-if="showContent">
    <page-head
      title="上传文件"
      rightIcon=""
      rightText=""
      @clickLeft="returnBack"
    ></page-head>
    <base-form
      class="base-form"
      ref="baseForm"
      :form-list="formList"
      :form-data.sync="form"
      :rules="rules"
      :showSubmitButton="false"
    ></base-form>
    <view class="action-content">
      <view class="action-item" @click="returnBack">关闭</view>
      <view class="action-item submit-btn" @click="submit()">保存</view>
    </view>
  </view>
</template>

<script>
import BaseForm from '@/components/base-form/base-form.vue';
export default {
  components: { BaseForm },
  data() {
    return {
      showContent: false,
      formList: [
        {
          title: '所属目录',
          prop: 'channelName',
          propVal: 'channelId',
          type: 'select',
          mode: 'dept',
          selectMode: 'scoll',
          chooseType: 'radio',
          placeholder: '请选择所属目录',
          name: '目录选择',
          getListType: 'scollSearch',
          searchParams: [{ name: 'scope', value: 'personal' }],
          searchApi: 'documentChannelList',
          required: true,
          changeCallback: this.handleRepairManDeptChange
        },
        {
          title: '附件',
          prop: 'uploadedFile',
          propVal: 'uploadedFilesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          required: true,
          moduleName: 'personal',
          placeholder: '上传附件'
        }
      ],
      form: {
        channelId: '',
        channelName: '',
        uploadedFile: '',
        personal: 'personal',
        uploadedFilesList: []
      },
      rules: {
        channelId: [
          {
            required: true,
            message: '请选择所属目录',
            trigger: ''
          }
        ]
      },
      isListOverivew: '',
      tabIndex: ''
    };
  },
  async onLoad(opt) {
    this.fromPage = opt.fromPage;
    if (opt.channelId) {
      this.form.channelId = opt.channelId;
      this.form.channelName = opt.channelName;
    }
    if (opt.tabIndex) this.tabIndex = opt.tabIndex;
    if (opt.isListOverivew) this.isListOverivew = opt.isListOverivew;
    this.showContent = true;
  },
  methods: {
    handleRepairManDeptChange(data = [], props) {
      let prop = '',
        propVal = '';
      if (data.length) {
        prop = data[0].name;
        propVal = data[0].id;
      }
      this.$set(this.form, props.prop, prop);
      this.$set(this.form, props.propVal, propVal);
      this.$refs.baseForm.personLabel = {};
    },
    async submit() {
      const result = await this.$refs.baseForm.validate();
      if (!result) return;
      let data = this.form.uploadedFilesList.map(e => {
        return {
          id: e.uid,
          folderId: this.form.channelId
        };
      });
      const res = await this.ajax.attachmentBacthUpdate(data);
      if (!res.success) {
        uni.showToast({
          title: res.message || '操作失败!',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      uni.showToast({
        title: '操作成功',
        duration: 1000,
        icon: 'none'
      });
      setTimeout(() => {
        this.$set(this, 'form', {});
        this.returnBack();
      }, 500);
    },
    //返回上一层
    returnBack() {
      this.form = this.$options.data().form;
      let url = `/pages/file/new-personal-file?fromPage=${this.fromPage}`;
      url += `&tabIndex=${this.tabIndex}&isListOverivew=${this.isListOverivew}`;
      uni.redirectTo({ url });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  /deep/ .base-form {
    height: calc(100% - 84px);
    overflow: auto;
    .u-form-item--left__content__label {
      font-size: 28rpx;
    }
    .u-switch {
      &.u-switch--on {
        background: $theme-color !important;
      }
    }
  }
  .action-content {
    position: absolute;
    bottom: 0;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    box-shadow: 0 -1px 6px #ccc;
    width: 100%;
    height: 40px;
    .action-item {
      flex: 1;
      text-align: center;
      color: $uni-text-color-grey;
      position: relative;
      &:not(:last-child)::after {
        content: ' ';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 15px;
        border-right: 1px solid #eee;
      }
      &.submit-btn {
        color: $theme-color;
      }
    }
  }
}
</style>
