<template>
  <view class="ts-content" v-if="showContent">
    <page-head
      :title="title"
      rightIcon=""
      rightText=""
      @clickLeft="returnBack"
    ></page-head>
    <base-form
      class="base-form"
      ref="baseForm"
      :form-list="formList"
      :form-data.sync="form"
      :rules="rules"
      :showSubmitButton="false"
    ></base-form>
    <view class="action-content">
      <view class="action-item" @click="returnBack">关闭</view>
      <view
        class="action-item submit-btn"
        v-if="type != 'detail'"
        @click="submit()"
        >保存</view
      >
    </view>
  </view>
</template>

<script>
import BaseForm from '@/components/base-form/base-form.vue';
import moment from 'moment';
export default {
  components: { BaseForm },
  data() {
    return {
      title: '新建文档',
      showContent: false,
      tabIndex: '',
      shareTabIndex: '',
      isListOverivew: '',
      formList: [
        {
          title: '所属目录',
          prop: 'channelName',
          propVal: 'channelId',
          type: 'select',
          mode: 'dept',
          selectMode: 'scoll',
          chooseType: 'radio',
          placeholder: '请选择所属目录',
          name: '科室选择',
          getListType: 'scollSearch',
          searchParams: [{ name: 'scope', value: 'pub' }],
          searchApi: 'documentChannelList',
          required: true,
          changeCallback: this.handleRepairManDeptChange
        },
        {
          title: '文档编号',
          prop: 'docNo',
          type: 'text',
          placeholder: '请填写文档编号',
          maxlength: 70,
          required: true
        },
        {
          title: '文档标题',
          prop: 'docTitle',
          type: 'text',
          placeholder: '请填写文档标题',
          maxlength: 70,
          required: true
        },
        {
          title: '有效期',
          prop: 'completeDate',
          propVal: 'completeDateVal',
          type: 'select',
          mode: 'range-picker',
          format: 'YYYY-MM-DD',
          placeholder: '请选择有效期',
          required: false
        },
        {
          title: '文档标签',
          prop: 'docType',
          type: 'text',
          placeholder: '请填写文档标签',
          maxlength: 70,
          required: false
        },
        {
          title: '内容摘要',
          prop: 'isoModifyReason',
          type: 'textarea',
          placeholder: '请填写内容摘要',
          maxlength: 500,
          required: false
        },
        {
          title: '附件',
          prop: 'uploadedFile',
          propVal: 'uploadedFilesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          moduleName: 'document',
          placeholder: '上传附件'
        }
      ],
      type: '',
      form: {
        channelId: '',
        channelName: '',
        docNo: moment().format('YYYYMMDDHHmmss'),
        docTitle: '',
        completeDate: '',
        completeDateVal: [],
        docType: '',
        isoModifyReason: '',
        pub: 'pub',
        uploadedFile: '',
        uploadedFilesList: []
      },
      rules: {
        channelId: [
          {
            required: true,
            message: '请选择所属目录',
            trigger: ''
          }
        ],
        docNo: [
          {
            required: true,
            message: '请填写文档编号',
            trigger: ''
          }
        ],
        docTitle: [
          {
            required: true,
            message: '请填写文档标题',
            trigger: ''
          }
        ]
      }
    };
  },
  async onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.title = '新建文档';
    this.action = 'documentSave';
    this.formList[0].disabled = false;
    if (opt.id) {
      this.action = 'documentUpdate';
      this.id = opt.id;
      this.title = '修改文档';
      this.formList[0].disabled = true;
      await this.getData(this.id);
      await this.getFileList(this.id);
      this.type = opt.type;
      this.form.channelName = opt.channelName;
      if (this.type == 'detail') {
        this.title = '查看文档';
        this.formList.forEach(e => {
          e.disabled = true;
        });
      }
    }
    if (opt.tabIndex) this.tabIndex = opt.tabIndex;
    if (opt.shareTabIndex) this.shareTabIndex = opt.shareTabIndex;
    if (opt.isListOverivew) this.isListOverivew = opt.isListOverivew;
    this.form.pub = 'pub';
    if (opt.channelId) {
      this.form.channelId = opt.channelId;
      this.form.channelName = opt.channelName;
    }
    this.showContent = true;
  },
  methods: {
    async getData(id) {
      let res = await this.ajax.selectDocumentById({ docId: id });
      this.form = res.object || {};
      if (this.form.validBegintime == null) {
        this.form.completeDateVal = [];
      } else {
        this.form.completeDate =
          this.form.validBegintime + '-' + this.form.validEndtime;
        this.form.completeDateVal = [
          this.form.validBegintime,
          this.form.validEndtime
        ];
      }
    },
    async getFileList(id) {
      let res = await this.ajax.selectDocumentAccessoryByBocId({ docId: id });
      this.form.uploadedFilesList = res.object.map(e => {
        return {
          fileExtension: e.type,
          fileId: e.id,
          fileRealName: e.name,
          fileName: e.saveName,
          name: e.name,
          fileSize: e.fileSize
        };
      });
    },
    handleRepairManDeptChange(data = [], props) {
      let prop = '',
        propVal = '';
      if (data.length) {
        prop = data[0].name;
        propVal = data[0].id;
      }
      this.$set(this.form, props.prop, prop);
      this.$set(this.form, props.propVal, propVal);
      this.$refs.baseForm.personLabel = {};
    },
    //获取附件
    async getFiles(fileIds) {
      let list = [],
        _self = this;
      await _self.ajax.getFileAttachmentByBusinessIdOrId(fileIds).then(res => {
        list = res.object;
      });
      return list;
    },
    async submit() {
      const result = await this.$refs.baseForm.validate();
      if (!result) return;
      let [validBegintime = '', validEndtime = ''] = this.form.completeDateVal;
      let uploadedFilesList = this.form.uploadedFilesList
        .map(e => e.fileId || e.uid)
        .join(',');
      let data = {
        channelId: this.form.channelId,
        channelName: this.form.channelName,
        docNo: this.form.docNo,
        docTitle: this.form.docTitle,
        docType: this.form.docType,
        isoModifyReason: this.form.isoModifyReason,
        uploadedFile: uploadedFilesList
      };
      if (validBegintime) {
        data.validBegintime = validBegintime;
        data.validEndtime = validEndtime;
      }
      if (this.form.id) {
        data.id = this.form.id;
      }
      const res = await this.ajax[this.action](data);
      if (!res.success) {
        uni.showToast({
          title: res.message || '操作失败!',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      uni.showToast({
        title: '操作成功',
        duration: 1000,
        icon: 'none'
      });
      setTimeout(() => {
        this.$set(this, 'form', {});
        this.returnBack();
      }, 500);
    },
    //返回上一层
    returnBack() {
      this.form = this.$options.data().form;
      let url = `/pages/file/new-department-file?fromPage=${this.fromPage}`;
      url += `&tabIndex=${this.tabIndex}&isListOverivew=${this.isListOverivew}`;
      if (this.shareTabIndex) url += `&shareTabIndex=${this.shareTabIndex}`;
      uni.redirectTo({ url });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  /deep/ .base-form {
    height: calc(100% - 84px);
    overflow: auto;
    .u-form-item--left__content__label {
      font-size: 28rpx;
    }
    .u-switch {
      &.u-switch--on {
        background: $theme-color !important;
      }
    }
  }
  .action-content {
    position: absolute;
    bottom: 0;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    box-shadow: 0 -1px 6px #ccc;
    width: 100%;
    height: 40px;
    .action-item {
      flex: 1;
      text-align: center;
      color: $uni-text-color-grey;
      position: relative;
      &:not(:last-child)::after {
        content: ' ';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 15px;
        border-right: 1px solid #eee;
      }
      &.submit-btn {
        color: $theme-color;
      }
    }
  }
}
</style>
