<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="危急值信息"></page-head>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :searchInput="true"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view class="contact-list">
          <view
            class="contact-item"
            v-for="item in dataList"
            :key="item.id"
            @tap="chooseItem(item.id, item.isRead)"
          >
            <view class="contact-item-row">
              <text class="contact-item-unread" v-if="item.isRead == 0"></text>
              <text
                class="contact-item-title"
                :class="item.status ? 'contact-item-handled' : ''"
              >
                {{ item.title | replaceFilter }}
              </text>
            </view>
            <view class="contact-item-row contact-item-content">{{
              item.dangerNote | replaceFilter
            }}</view>
            <view class="contact-item-row">
              <text class="contact-item-user">{{
                item.deptName + ' ' + (item.sendUser || '')
              }}</text>
              <text class="contact-item-time">{{
                item.createTime | formatDate
              }}</text>
            </view>
          </view>
        </view>
      </mescroll>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      dataList: [],
      keywords: ''
    };
  },
  filters: {
    replaceFilter(val) {
      if (val == null) return '';
      return val.replace(/\\n/g, '');
    },
    formatDate(timeStamp) {
      let dates = new Date(timeStamp),
        Y = dates.getFullYear(),
        M = String(dates.getMonth() + 1).replace(/(^\d{1}$)/, '0$1'),
        D = String(dates.getDate()).replace(/(^\d{1}$)/, '0$1'),
        h = dates.getHours(),
        m = String(dates.getMinutes()).replace(/(^\d{1}$)/, '0$1'),
        s = dates.getSeconds(),
        times = Y + '-' + M + '-' + D + ' ' + h + ':' + m;
      return times;
    }
  },
  methods: {
    //搜索
    search(res) {
      this.keywords = res.value;
      this.datasInit();
      this.$nextTick(() => {
        this.$refs['mescroll'].downCallback();
      });
    },
    //获取数据
    getListData(page, successCallback, errorCallback) {
      this.ajax
        .getCriticalValueList({
          pageSize: page.size,
          pageNo: page.num,
          sidx: 'create_time',
          sord: 'desc',
          title: this.keywords
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(e => {
          errorCallback();
        });
    },
    setListData(rows) {
      this.dataList = this.dataList.concat(rows);
    },
    datasInit() {
      this.dataList = [];
    },
    chooseItem(id, status) {
      uni.navigateTo({
        url: `/pages/criticalValues/critical-values-detail?id=${id}&status=${status}&fromPage=criticalValuesList`
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .mescroll-content {
    flex: 1;
    position: relative;
    .contact-item {
      padding: 22rpx 30rpx;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        height: 1px;
        background-color: #eee;
        left: 30rpx;
        right: 0;
      }
      &:last-child::after {
        height: 0;
      }
      .contact-item-row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }
      .contact-item-content {
        font-size: 28rpx;
        color: #666;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .contact-item-title {
        flex: 1;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        padding-right: 20rpx;
        box-sizing: border-box;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
      .contact-item-title.contact-item-handled {
        color: #666;
      }
      .contact-item-unread {
        width: 20rpx;
        height: 20rpx;
        border-radius: 100%;
        background-color: #f59a23;
        margin-right: 10rpx;
      }
      .contact-item-user {
        flex: 1;
      }
      .contact-item-user,
      .contact-item-time {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}
</style>
