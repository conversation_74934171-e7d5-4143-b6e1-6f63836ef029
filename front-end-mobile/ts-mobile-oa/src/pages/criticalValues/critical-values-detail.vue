<template>
  <view class="critical-values-box">
    <page-head @clickLeft="returnBack" title="危急值详情"></page-head>
    <view class="content">
      <view class="content-head" v-if="JSON.stringify(datas) != '{}'">
        <view class="content-head_title">{{
          datas.title | replaceFilter
        }}</view>
        <view class="content-head_info">
          <view class="content-head_info-right">{{
            datas.createTime | formatDate
          }}</view>
        </view>
      </view>
      <view class="content-list">
        <view v-for="(item, index) in dataList" :key="index">
          <view v-if="item.val" class="content-item">
            <view class="content-item-lable">{{ item.key }}</view>
            <view class="content-item-value">{{ item.val }}</view>
          </view>
        </view>
      </view>
      <view class="project-list" v-if="criticaiValueList.length > 0">
        <view class="project-item header">
          <view class="item-title">
            <view class="project-item-lable header">项目</view>
            <view class="project-item-value header">结果</view>
          </view>
        </view>
        <view
          class="project-item"
          v-for="(item, index) in criticaiValueList"
          :key="index"
        >
          <view class="item-title">
            <view class="project-item-lable">{{ item.itemName }}</view>
            <view class="project-item-value">{{ item.checkValue }}</view>
          </view>
          <view class="item-title">
            <view class="bottom">参考范围：{{ item.refRange }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="bottom-btn" v-if="showHandleBtn">
      <button class="btn-item uni-bg-blue" @tap="confirm">
        确认处理
      </button>
    </view>
    <uni-popup ref="remarkPopup" type="allBottom">
      <page-head
        title="确认处理"
        right-text="提交"
        @clickLeft="popupHide"
        @clickRight="popupConfirm"
      ></page-head>
      <view class="uni-textarea">
        <textarea
          class="textarea"
          v-model="remark"
          placeholder="请输入处理情况"
          :maxlength="-1"
        />
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      datas: {},
      dataList: [],
      remark: '',
      showHandleBtn: false,
      isSubmit: false,
      criticaiValueList: []
    };
  },
  filters: {
    replaceFilter(val) {
      return val.replace(/\\n/g, '');
    },
    formatDate(timeStamp) {
      let dates = new Date(timeStamp),
        Y = dates.getFullYear(),
        M = String(dates.getMonth() + 1).replace(/(^\d{1}$)/, '0$1'),
        D = String(dates.getDate()).replace(/(^\d{1}$)/, '0$1'),
        h = dates.getHours(),
        m = String(dates.getMinutes()).replace(/(^\d{1}$)/, '0$1'),
        s = dates.getSeconds(),
        times = Y + '-' + M + '-' + D + ' ' + h + ':' + m;
      return times;
    }
  },
  onLoad(opt) {
    if (opt.status == 0) this.updateCriticalStatus(opt.id);
    this.getCriticalData(opt.id);
  },
  methods: {
    //获取数据
    getCriticalData(id) {
      this.ajax
        .getCriticalValueDetails({
          id: id
        })
        .then(res => {
          this.datas = res.object;
          this.showHandleBtn = this.datas.status == 0;
          // let dangerNote = this.datas.dangerNote
          //   .replace(/<br\s*\/?>/g, '，')
          //   .replace(/\r\n/g, '，');
          // let criticalValList = dangerNote.split('，');
          this.criticaiValueList =
            JSON.parse(this.datas.criticaiValueJson) || [];
          this.dataList = [
            {
              key: '患者姓名',
              val: this.datas.patientName
            },
            {
              key: '患者科室',
              val: this.datas.deptName
            },
            {
              key: '床号',
              val: this.datas.bedNo
            },
            {
              key: '病历号',
              val: this.datas.regNo
            },
            {
              key: '主管医生',
              val: this.datas.doctorName
            },
            {
              key: '开单医生',
              val: this.datas.receiveUser
            },
            {
              key: '危急值',
              val: this.datas.panicvalue
            },
            {
              key: '报告科室',
              val: this.datas.sendDept
            },
            {
              key: '检验样本',
              val: this.datas.sampleName
            },
            {
              key: '报告时间',
              val: this.datas.signDate
            },
            {
              key: '报告人',
              val: this.datas.sendUser
            }
          ];
        })
        .catch(e => {
          errorCallback();
        });
    },
    //获取数据
    updateCriticalStatus(id) {
      this.ajax.updateCriticalValueStstus({
        id: id
      });
    },
    confirm() {
      this.$refs['remarkPopup'].open();
    },
    popupHide() {
      this.$refs['remarkPopup'].close();
      this.remark = '';
    },
    popupConfirm() {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;
      this.ajax
        .handleCriticalValue({
          remark: this.remark,
          id: this.datas.id
        })
        .then(res => {
          this.showHandleBtn = false;
          this.returnBack();
        })
        .catch(e => {
          this.isSubmit = false;
        });
    },
    returnBack() {
      uni.redirectTo({
        url: '/pages/criticalValues/critical-values-list'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.critical-values-box {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.content {
  flex: 1;
  overflow: auto;
}
.content-head {
  padding: 20rpx 30rpx;
  position: relative;
  background-color: #ffffff;
  &::after {
    position: absolute;
    content: '';
    left: 0;
    right: 0;
    bottom: 0;
    height: 1px;
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #eeeeee;
  }
  .content-head_title {
    font-size: 32rpx;
    margin-bottom: 10rpx;
    font-weight: bold;
    color: #333333;
  }
  .content-head_info {
    height: 40rpx;
    font-size: 24rpx;
    color: #999999;
    .content-head_info-left {
      float: left;
      font-size: 24rpx;
    }
    .content-head_info-right {
      float: right;
      font-size: 24rpx;
    }
  }
}
.content-item {
  position: relative;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  line-height: 1;
  &::after {
    position: absolute;
    content: '';
    bottom: 0;
    left: 30rpx;
    right: 0;
    transform: scaleY(-0.5);
    height: 1px;
    background-color: #eee;
  }
  .content-item-lable {
    width: 200rpx;
    padding: 5rpx 30rpx;
    box-sizing: border-box;
    position: relative;
    color: #666;
    font-size: 30rpx;
    text-align: right;
  }
  .content-item-value {
    flex: 1;
    font-size: 30rpx;
    color: #333;
    padding: 5rpx 30rpx;
    padding-left: 0;
    box-sizing: border-box;
    text-align: left;
  }
}
.project-list {
  margin: 20rpx;
  background: #fff;
  .header {
    background-color: #007bc1;
  }
  .project-item {
    padding: 10rpx 10rpx;
    .item-title {
      display: flex;
      .header {
        color: #fff !important;
      }
      .bottom {
        color: #aaa !important;
      }
      .border-bottom {
        border-bottom: 1px dashed #aaa;
      }
      .project-item-lable {
        width: 400rpx;
        box-sizing: border-box;
        position: relative;
        color: #000;
        font-size: 30rpx;
        text-align: left;
      }
      .project-item-value {
        flex: 1;
        font-size: 30rpx;
        color: #000;
        padding-left: 0;
        box-sizing: border-box;
        text-align: left;
      }
    }
  }
}
.bottom-btn {
  background-color: #ffffff;
  box-shadow: 0 1px 6px #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 22rpx 30rpx;
  box-sizing: border-box;
  z-index: 10;
  .btn-item {
    flex: 1;
    box-sizing: border-box;
    text-align: center;
    font-size: 32rpx;
    position: relative;
    border-radius: 10rpx;
  }
}
.uni-textarea .textarea {
  height: 300rpx;
  font-size: 32rpx;
  color: #333;
}
</style>
