<template>
  <view class="ts-content">
    <page-head
      title="流程代理设置"
      right-text="提交"
      @clickLeft="returnBack"
      @clickRight="agentSubmit"
    ></page-head>
    <view class="uni-input-group" v-if="showContent">
      <view class="uni-input-row">
        <view class="uni-label">代理有效期</view>
        <view class="input_container" @tap="showPicker('range')">
          <input
            class="uni-input"
            disabled="disabled"
            :class="{ slectedInp: rangeDate }"
            placeholder="请选择代理有效期"
            :value="rangeDate"
          />
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
      <view class="uni-input-row">
        <view class="uni-label">代理人</view>
        <view class="input_container" @tap="showPersonSelet">
          <view class="uni-input" :class="{ slectedInp: agentNameEllipsis }">{{
            agentNameEllipsis ? agentNameEllipsis : '请选择代理人'
          }}</view>
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
      <view class="uni-input-row">
        <view class="input_container">启用代理</view>
        <view class="item_extra">
          <switch
            class="item_extra_switch"
            :checked="switchChecked"
            @change="onSwitchChange"
          />
        </view>
      </view>
    </view>
    <date-picker
      startDate="2020-01-01"
      :value="agentTimeArr"
      endDate="2100-12-31"
      mode="range"
      :current="true"
      @confirm="onConfirm"
      @cancel="onCancel"
      ref="range"
    ></date-picker>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState } from 'vuex';
import datePicker from '@/components/picker/date-picker.vue';
export default {
  components: {
    datePicker
  },
  data() {
    return {
      showContent: false,
      fromPage: '',
      info: null,
      agentTimeArr: [],
      startDate: '',
      endDate: '',
      rangeDate: '',
      switchChecked: false,
      personlist: [],
      agentId: '',
      agentNameEllipsis: '',
      agentName: ''
    };
  },
  computed: {
    ...mapState(['empcode'])
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.getPersonalInfoSettings();
  },
  methods: {
    getPersonalInfoSettings() {
      this.ajax
        .getPersonalInformationSettings({
          userCode: this.empcode
        })
        .then(res => {
          let data = res.object;
          this.info = data;
          this.showContent = true;
          if (data.agentStartTime && data.agentEndTime) {
            this.rangeDate = `${data.agentStartTime} 至 ${data.agentEndTime}`;
            this.startDate = data.agentStartTime;
            this.endDate = data.agentEndTime;
            this.agentTimeArr = [data.agentStartTime, data.agentEndTime];
            this.switchChecked = data.isEnableProcessAgent == 1 ? true : false;
            this.agentName = data.agentName;
            this.agentId = data.agentId;
            let agentNameEllipsisArr = [];
            data.empList.forEach((item, index) => {
              if (index < 3) {
                agentNameEllipsisArr.push(item.empName);
              }
              this.personlist.push({
                name: item.empName,
                id: item.empCode,
                empFirstName: item.empName.substring(item.empName.length - 2),
                empHeadImg: item.empHeadImg,
                empDeptName: item.empDeptName,
                empDutyName: item.empDutyName,
                empSex: item.empSex,
                choose: true
              });
            });
            this.agentNameEllipsis = agentNameEllipsisArr.join(',');
            if (data.empList.length > 3) {
              this.agentNameEllipsis += `等${data.empList.length}人`;
            }
          }
        });
    },
    //显示时间选择
    showPicker(type) {
      this.$refs[type].show();
    },
    //时间确定
    onConfirm(res) {
      this.rangeDate = res.result;
      this.agentTimeArr = [];
      this.startDate = `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`;
      this.endDate = `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`;
    },
    //时间取消
    onCancel() {},
    //启动开关
    onSwitchChange(event) {
      this.switchChecked = event.detail.value;
    },
    //跳转至人员选择
    showPersonSelet() {
      //监听事件
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        this.personlist = data;
        let agentNameArr = [],
          agentIdArr = [],
          agentNameEllipsisArr = [];
        data.forEach((item, index) => {
          agentNameArr.push(item.name);
          agentIdArr.push(item.id);
          if (index < 3) {
            agentNameEllipsisArr.push(item.name);
          }
        });
        this.agentNameEllipsis = agentNameEllipsisArr.join('、');
        if (data.length > 3) {
          this.agentNameEllipsis += `等${data.length}人`;
        }
        this.agentName = agentNameArr.join(',');
        this.agentId = agentIdArr.join(',');
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      uni.setStorageSync('person_list', JSON.stringify(this.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    //提交
    agentSubmit() {
      if (!this.rangeDate) {
        uni.showToast({
          icon: 'none',
          title: '请选择代理有效期'
        });
        return;
      } else if (!this.agentNameEllipsis) {
        uni.showToast({
          icon: 'none',
          title: '请选择代理人'
        });
        return;
      }
      this.submitInfo();
    },
    //修改基本信息
    submitInfo() {
      this.ajax
        .editEmployee({
          empCode: this.empcode,
          id: this.empcode,
          empDeptId: this.info.empDeptId,
          agentStartTime: this.startDate,
          agentEndTime: this.endDate,
          agentName: this.agentName,
          agentId: this.agentId,
          isEnableProcessAgent: this.switchChecked ? 1 : 0
        })
        .then(res => {
          uni.showToast({
            title: '提交成功',
            icon: 'none'
          });
          uni.redirectTo({
            url: `/pages/personalCenter/settingApproval/approval?fromPage=${this.fromPage}`
          });
        });
    },
    returnBack() {
      uni.redirectTo({
        url: `/pages/personalCenter/settingApproval/approval?fromPage=${this.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  .uni-input-row {
    .uni-label {
      color: #333333;
    }
    .input_container {
      position: relative;
      flex: 1;
      display: flex;
      align-items: center;
      .uni-input {
        text-align: right;
        color: #bbb;
        font-size: 32rpx;
      }
      .slectedInp {
        color: #666666;
      }
    }
  }
}
</style>
