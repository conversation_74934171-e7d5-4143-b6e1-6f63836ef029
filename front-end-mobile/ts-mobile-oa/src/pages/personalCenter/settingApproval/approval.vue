<template>
  <view class="ts-content">
    <page-head title="流程设置" @clickLeft="returnBack"></page-head>
    <view class="uni-list" v-if="showContent">
      <view class="uni-list-cell" v-for="(item, index) in navList" :key="index">
        <view
          class="uni-list-cell-navigate uni-navigate-right"
          @click="goDetailPage(item.url)"
        >
          <text class="sign_text">{{ item.text }}</text>
          <text
            class="sign"
            v-if="item.enable"
            :class="item.enableVal ? 'enable' : 'notenable'"
          >
            {{ item.enableVal ? '已启用' : '未启用' }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState } from 'vuex';
export default {
  data() {
    return {
      showContent: false,
      fromPage: '',
      navList: [
        {
          text: '签章设置',
          enable: true,
          enableVal: false,
          url: '/pages/personalCenter/setting/signature'
        },
        {
          text: '流程代理设置',
          enable: true,
          enableVal: false,
          url: '/pages/personalCenter/settingApproval/process-agent'
        },
        {
          text: '常用语设置',
          enable: false,
          enableVal: false,
          url: '/pages/personalCenter/settingApproval/common-words'
        }
      ]
    };
  },
  computed: {
    ...mapState(['empcode'])
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.getApprovalSetting();
  },
  methods: {
    getApprovalSetting() {
      this.ajax
        .getPersonalInformationSettings({
          userCode: this.empcode
        })
        .then(res => {
          this.showContent = true;
          let data = res.object;
          this.navList[0].enableVal = data.isUseSignature == 1 ? true : false;
          this.navList[1].enableVal =
            data.isEnableProcessAgent == 1 ? true : false;
        });
    },
    //跳转到相应页面
    goDetailPage(path) {
      uni.navigateTo({
        url: `${path}?fromPage=${this.fromPage}`
      });
    },
    //返回
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/${this.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .enable {
    color: #10b47f;
  }
  .notenable {
    color: #999999;
  }
  .uni-list {
    margin-bottom: 10px;
    .uni-list-cell-navigate {
      justify-content: start;
      .sign_text {
        flex: 1;
        color: #333333;
        font-size: 32rpx;
      }
      .sign {
        font-size: 24rpx;
      }
    }
    .item_icon {
      margin-right: 18rpx;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      line-height: 1;
      .oa-icon {
        font-size: 40rpx;
      }
    }
    &::after {
      height: 0;
    }
  }
}
</style>
