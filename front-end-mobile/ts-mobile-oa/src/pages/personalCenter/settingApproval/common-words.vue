<template>
  <view class="ts-content">
    <page-head
      title="常用语设置"
      right-text="新增"
      @clickLeft="returnBack"
      @clickRight="addSubmit"
    ></page-head>
    <uni-swipe-action class="swipe-action" v-if="showContent">
      <uni-swipe-action-item
        v-for="(item, index) in commentList"
        :auto-close="false"
        :show="item.isOpened"
        :options="item.options"
        :key="item.id"
        :itemIndex="index"
        @change="swipeChange($event, index)"
        @click="swipeClick($event, index)"
        @choose="swipeChoose"
      >
        <view class="comment-item">
          <view class="content-text">{{ item.content }}</view>
          <uni-tag
            v-if="item.isDefault == '1'"
            style="margin-left: 10px;"
            :inverted="true"
            size="small"
            text="系统"
            type="primary"
          />
          <uni-tag
            v-if="item.isDefaultValue == '1'"
            style="margin-left: 10px;"
            :inverted="true"
            size="small"
            text="默认填充"
            type="warning"
          />
        </view>
      </uni-swipe-action-item>
    </uni-swipe-action>
    <input-prompt
      v-if="cancelShow"
      type="text"
      :title="titleText"
      :value="inputVal"
      label="内容"
      maxlength="100"
      :name="fieldName"
      placeholder="请输入常用语"
      @confirm="confirm"
      @cancel="cancel"
    >
      <template #formStart v-if="isAdmin">
        <radio-group class="radio-group" @change="commentTypeChange">
          <view class="group-label">类型</view>
          <view>
            <label
              class="group-radio"
              v-for="item in commentTypeList"
              :key="item.value"
            >
              <radio :value="item.value" :checked="item.value == commentType" />
              {{ item.name }}
            </label>
          </view>
        </radio-group>
      </template>
    </input-prompt>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState } from 'vuex';
import uniSwipeAction from '@/components/uni-swipe-action/uni-swipe-action.vue';
import uniSwipeActionItem from '@/components/uni-swipe-action-item/uni-swipe-action-item.vue';
import uniTag from '@/components/uni-tag/uni-tag.vue';
import inputPrompt from '@/components/input-prompt/input-prompt.vue';

export default {
  components: {
    uniSwipeActionItem,
    uniSwipeAction,
    uniTag,
    inputPrompt
  },
  data() {
    return {
      showContent: false,
      fromPage: '',
      commentList: [],
      cancelShow: false,
      inputVal: '',
      fieldName: '',
      saveShow: false,
      titleText: '',
      isOpened: false,
      commentTypeList: [
        {
          value: '1',
          name: '系统'
        },
        {
          value: '0',
          name: '个人'
        }
      ],
      commentType: 1
    };
  },
  computed: {
    ...mapState(['empcode']),
    isAdmin() {
      return this.$store.state.userInfo.isAdmin == 'Y';
    }
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.getOfficalDictionList();
  },
  methods: {
    getOfficalDictionList() {
      this.ajax
        .getOfficalDictionList({
          createUser: this.empcode,
          offName: '',
          pageNo: 1,
          pageSize: 1000,
          sord: 'ASC',
          sidx: 'IS_TOP'
        })
        .then(res => {
          let data = res.rows;
          this.showContent = true;
          this.commentList = data.map(item => {
            let swipe = {
              id: item.id,
              options: [
                {
                  text: '置顶'
                },
                {
                  text: item.isDefaultValue == 1 ? '取消填充' : '默认填充',
                  style: {
                    backgroundColor: 'rgb(254,156,1)'
                  }
                }
              ],
              isOpened: false,
              content: item.offName,
              isDefault: item.isDefault,
              isDefaultValue: item.isDefaultValue
            };
            if (item.isDefault == 0 || this.isAdmin) {
              let swipeOther = [
                {
                  text: '修改',
                  style: {
                    backgroundColor: 'rgb(0 122 255)'
                  }
                },
                {
                  text: '删除',
                  style: {
                    backgroundColor: 'rgb(255,58,49)'
                  }
                }
              ];
              swipe.options = swipe.options.concat(swipeOther);
            }
            return swipe;
          });
        });
    },
    swipeChange(e, vm) {
      this.commentList.forEach((item, index) => {
        if (vm === index) {
          this.$set(this.commentList[index], 'isOpened', e);
          return;
        } else {
          if (e) this.$set(this.commentList[index], 'isOpened', false);
        }
      });
    },
    swipeClick(e, index) {
      switch (e.index) {
        case 0: //置顶
          this.roofOffName(index);
          break;
        case 1: //填充
          this.changeFillStatus(index);
          break;
        case 2: //修改
          this.commentType = this.commentList[index].isDefault;
          this.cancelShow = true;
          this.inputVal = this.commentList[index].content;
          (this.fieldName = index.toString()), (this.titleText = '常用语修改');
          break;
        case 3: //删除
          uni.showModal({
            title: '提示',
            content: '您确定删除该常用语？',
            confirmText: '取消',
            cancelText: '确定',
            confirmColor: '#005BAC',
            success: res => {
              if (res.cancel) {
                this.deleteOffName(index);
              }
            }
          });
          break;
      }
    },
    swipeChoose(e) {},
    //弹出层确定事件
    confirm(nameVal, pholderVal, formVal) {
      if (!formVal) {
        uni.showToast({
          icon: 'none',
          title: pholderVal
        });
        return;
      }
      this.cancelShow = false;
      if (this.saveShow) {
        this.saveOffName(formVal);
      } else {
        this.editOffName(nameVal, formVal);
      }
      this.saveShow = false;
    },
    //弹出层取消事件
    cancel() {
      this.cancelShow = false;
      (this.saveShow = false), (this.fieldName = '');
      this.inputVal = '';
    },
    //置顶
    roofOffName(index) {
      this.$set(this.commentList[Number(index)], 'isOpened', false);
      this.ajax
        .confirmOfficalDiction('update', {
          isTop: 0,
          id: this.commentList[index].id
        })
        .then(res => {
          // this.$nextTick(() => {
          //   let thobj = this.commentList.splice(index, 1);
          //   this.commentList = thobj.concat(this.commentList);
          // });
          this.getOfficalDictionList();
        });
    },
    //改变填充
    changeFillStatus(index) {
      let isDefaultValue =
        this.commentList[index].isDefaultValue == 1 ? '0' : '1';
      this.ajax
        .confirmOfficalDiction('setDefaultValue', {
          id: this.commentList[index].id,
          isDefaultValue: isDefaultValue
        })
        .then(res => {
          this.$set(this.commentList[Number(index)], 'isOpened', false);
          if (isDefaultValue == 1) {
            this.commentList.forEach(item => {
              item.isDefaultValue = 0;
              item.options[1].text = '默认填充';
            });
          }
          this.$set(
            this.commentList[Number(index)],
            'isDefaultValue',
            isDefaultValue
          );
          this.$set(
            this.commentList[Number(index)].options[1],
            'text',
            isDefaultValue == 1 ? '取消填充' : '默认填充'
          );
          uni.showToast({
            title: '修改成功',
            icon: 'none'
          });
        });
    },
    //修改常用语
    editOffName(index, val) {
      this.ajax
        .confirmOfficalDiction('update', {
          offName: val,
          isDefault: this.commentType,
          id: this.commentList[index].id
        })
        .then(res => {
          this.$set(this.commentList[Number(index)], 'isOpened', false);
          this.$set(
            this.commentList[Number(index)],
            'isDefault',
            this.commentType
          );
          this.$set(this.commentList[Number(index)], 'content', val);
          uni.showToast({
            title: '修改成功',
            icon: 'none'
          });
        });
    },
    //删除常用语
    deleteOffName(index) {
      this.ajax
        .confirmOfficalDiction('deletedById', {
          id: this.commentList[index].id
        })
        .then(res => {
          this.commentList.splice(index, 1);
          uni.showToast({
            title: '删除成功',
            icon: 'none'
          });
        });
    },
    //新增常用语
    saveOffName(val) {
      this.ajax
        .confirmOfficalDiction('save', {
          offName: val,
          isDefault: this.commentType
        })
        .then(res => {
          let data = res.object;
          let swipe = {
            id: data,
            options: [
              {
                text: '置顶'
              },
              {
                text: '默认填充',
                style: {
                  backgroundColor: 'rgb(254,156,1)'
                }
              },
              {
                text: '修改',
                style: {
                  backgroundColor: 'rgb(0 122 255)'
                }
              },
              {
                text: '删除',
                style: {
                  backgroundColor: 'rgb(255,58,49)'
                }
              }
            ],
            isOpened: false,
            content: val,
            isDefault: this.commentType,
            isDefaultValue: 0
          };
          this.commentList.push(swipe);
          uni.showToast({
            title: '新增成功',
            icon: 'none'
          });
        });
    },
    //新增按钮点击
    addSubmit() {
      this.commentType = this.isAdmin ? '1' : '0';
      this.saveShow = true;
      this.titleText = '新增常用语';
      this.cancelShow = true;
      this.inputVal = '';
      this.fieldName = '';
    },
    //类型切换
    commentTypeChange(e) {
      this.commentType = e.detail.value;
    },
    //返回按钮
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/my'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.swipe-action {
  &::before {
    position: absolute;
    z-index: 3;
    right: 0;
    top: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #eeeeee;
  }
}
.comment-item {
  width: 100%;
  padding: 20rpx 24rpx 20rpx 30rpx;
}
.content-text {
  display: inline;
  background-color: #fff;
  font-size: 32rpx;
}
.radio-group {
  padding: 0 30rpx 10rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .group-label,
  .group-radio {
    font-size: 28rpx;
  }
  .group-radio:last-child {
    padding: 0;
  }
}
.uni-icons {
  color: #bbb;
}
</style>
