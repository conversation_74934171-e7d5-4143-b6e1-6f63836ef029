<template>
  <view class="ts-content">
    <page-head title="编辑个人信息" @clickLeft="returnBack"></page-head>
    <view class="uni-list" v-if="showContent">
      <view class="uni-list-cell">
        <view
          class="uni-list-cell-navigate uni-navigate-right"
          @click="editImg"
        >
          <text class="signtitle">头像</text>
          <view class="listImg">
            <image
              v-cloak
              :src="
                info.empHeadImg
                  ? $config.BASE_HOST + info.empHeadImg
                  : '../../static/img/headImg.png'
              "
              mode=""
            ></image>
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate">
          <text class="signtitle">姓名</text>
          <text class="signvalue">{{ info.empName }}</text>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate">
          <text class="signtitle">科室及职位</text>
          <text class="signvalue"
            >{{ info.empDeptName
            }}{{ info.empDutyName == null ? '' : ' ' + info.empDutyName }}</text
          >
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate">
          <text class="signtitle">性别</text>
          <text class="signvalue">
            {{ info.empSex == 0 ? '男' : info.empSex == 1 ? '女' : '' }}
          </text>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate">
          <text class="signtitle">身份证</text>
          <text class="signvalue">{{ info.empIdcard }}</text>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate">
          <text class="signtitle">工号</text>
          <text class="signvalue">{{ info.empCode }}</text>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate">
          <text class="signtitle">生日</text>
          <text class="signvalue">{{ info.empBirth }}</text>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate">
          <text class="signtitle">入职时间</text>
          <text class="signvalue">{{ info.empHiredate | dateFilter }}</text>
        </view>
      </view>
    </view>
    <view class="uni-list">
      <view
        class="uni-list-cell"
        @click="editInfo('邮箱', 'empEmail', 'text', info.empEmail)"
      >
        <view class="uni-list-cell-navigate uni-navigate-right">
          <text class="required_red oa-icon oa-icon-asterisks"></text>
          <text class="signtitle">邮箱</text>
          <text class="signvalue" name="empEmail">{{ info.empEmail }}</text>
        </view>
      </view>
      <view
        class="uni-list-cell"
        @click="
          editInfo('手机号码(1)', 'empPhone', 'number', info.empPhone, '[0-9]*')
        "
      >
        <view class="uni-list-cell-navigate uni-navigate-right">
          <text class="required_red oa-icon oa-icon-asterisks"></text>
          <text class="signtitle">手机号码(1)</text>
          <text class="signvalue" name="empPhone">{{ info.empPhone }}</text>
        </view>
      </view>
      <view
        class="uni-list-cell"
        @click="
          editInfo(
            '手机号码(2)',
            'empPhoneSecond',
            'number',
            info.empPhoneSecond,
            '[0-9]*'
          )
        "
      >
        <view class="uni-list-cell-navigate uni-navigate-right">
          <text class="signtitle">手机号码(2)</text>
          <text class="signvalue" name="empPhoneSecond">{{
            info.empPhoneSecond
          }}</text>
        </view>
      </view>
      <view
        class="uni-list-cell"
        @click="
          editInfo(
            '移动短号',
            'empBusinessPhone',
            'number',
            info.empBusinessPhone,
            '[0-9]*'
          )
        "
      >
        <view class="uni-list-cell-navigate uni-navigate-right">
          <text class="signtitle">移动短号</text>
          <text class="signvalue" name="empBusinessPhone">{{
            info.empBusinessPhone
          }}</text>
        </view>
      </view>
      <view
        class="uni-list-cell"
        @click="
          editInfo(
            '联通短号',
            'empUnicomBusinessPhone',
            'number',
            info.empUnicomBusinessPhone,
            '[0-9]*'
          )
        "
      >
        <view class="uni-list-cell-navigate uni-navigate-right">
          <text class="signtitle">联通短号</text>
          <text class="signvalue" name="empUnicomBusinessPhone">{{
            info.empUnicomBusinessPhone
          }}</text>
        </view>
      </view>
      <view
        class="uni-list-cell"
        @click="
          editInfo(
            '电信短号',
            'empTelecomBusinessPhone',
            'number',
            info.empTelecomBusinessPhone,
            '[0-9]*'
          )
        "
      >
        <view class="uni-list-cell-navigate uni-navigate-right">
          <text class="signtitle">电信短号</text>
          <text class="signvalue" name="empTelecomBusinessPhone">{{
            info.empTelecomBusinessPhone
          }}</text>
        </view>
      </view>
      <view
        class="uni-list-cell"
        @click="
          editInfo('办公室号码', 'empShortPhone', 'text', info.empShortPhone)
        "
      >
        <view class="uni-list-cell-navigate uni-navigate-right">
          <text class="signtitle">办公室号码</text>
          <text class="signvalue" name="empShortPhone">{{
            info.empShortPhone
          }}</text>
        </view>
      </view>
    </view>
    <input-prompt
      v-if="cancelShow"
      :value="inputVal"
      :type="inputType"
      :pattern="inputPattern"
      :title="titleText"
      :name="fieldName"
      :placeholder="placeholderText"
      @confirm="confirm"
      @cancel="cancel"
    ></input-prompt>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState, mapMutations } from 'vuex';
import inputPrompt from '@/components/input-prompt/input-prompt.vue';
import { chooseImage } from '@/common/js/uploadImg.js';
export default {
  components: {
    inputPrompt
  },
  data() {
    return {
      showContent: false,
      info: {},
      cancelShow: false,
      placeholderText: '',
      titleText: '',
      fieldName: '',
      inputType: '',
      inputVal: '',
      inputPattern: ''
    };
  },
  computed: {
    ...mapState(['empcode'])
  },
  onLoad() {
    this.ajax
      .getPersonalInformationSettings({
        userCode: this.empcode
      })
      .then(res => {
        let data = res.object;
        this.info = data;
        this.showContent = true;
      });
  },
  filters: {
    dateFilter(value) {
      if (null != value) {
        let getDate = new Date(value.replace(/-/g, '/')),
          getDateStr = `${getDate.getFullYear()}年${getDate.getMonth() + 1}月`;
        return getDateStr;
      }
      return '';
    }
  },
  methods: {
    ...mapMutations(['changeState']),
    editInfo(title, field, type, val, pattern = '') {
      this.titleText = `${title}修改`;
      this.placeholderText = `请输入${title}`;
      this.fieldName = field;
      this.inputType = type;
      this.inputVal = val ? val : '';
      this.inputPattern = pattern;
      this.$nextTick(() => {
        this.cancelShow = true;
      });
    },
    cancel() {
      this.cancelShow = false;
      this.placeholderText = '';
      (this.titleText = ''), (this.fieldName = '');
      this.inputType = '';
    },
    confirm(nameVal, pholderVal, formVal) {
      if (!formVal && (nameVal == 'empPhone' || nameVal == 'empEmail')) {
        uni.showToast({
          icon: 'none',
          title: pholderVal
        });
        return;
      }
      let validRule, toastTitle;
      switch (nameVal) {
        case 'empPhone':
          validRule = /^(13[0-9]|14[5-9]|15[012356789]|166|17[0-8]|18[0-9]|19[8-9])[0-9]{8}$/; // 手机号码校验规则
          toastTitle = '手机号输入有误';
          break;
        case 'empPhoneSecond':
          validRule = /^(13[0-9]|14[5-9]|15[012356789]|166|17[0-8]|18[0-9]|19[8-9])[0-9]{8}$/; // 手机号码校验规则
          toastTitle = '手机号输入有误';
          break;
        case 'empEmail':
          validRule = /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/; // 邮箱校验规则
          toastTitle = '邮箱输入有误';
          break;
        case 'empShortPhone':
          validRule = /^[0][1-9]{2,3}-[0-9]{5,10}$/;
          toastTitle = '办公室号码输入有误';
      }
      if (formVal && validRule && !validRule.test(formVal)) {
        uni.showToast({
          title: toastTitle,
          icon: 'none'
        });
        return;
      }
      this.cancelShow = false;
      let param_s = {
        showLoading: true,
        hideLoading: true,
        loadingTitle: '提交中...',
        successToast: '修改成功'
      };
      this.submitInfo(nameVal, formVal, param_s);
    },
    //修改基本信息
    submitInfo(fieldname, val, param_sub) {
      let datas = {};
      (datas.empCode = this.empcode),
        (datas.id = this.empcode),
        (datas.empDeptId = this.info.empDeptId);
      datas[fieldname] = val;
      this.ajax.updateMyUser(datas).then(res => {
        if (fieldname == 'avatar') {
          this.$set(this.info, 'empHeadImg', val);
          this.changeState({ name: 'headimg', value: val });
          let userInfo = JSON.parse(uni.getStorageSync('_oa_user_key') || '{}');
          userInfo.headimg = val;
          uni.setStorageSync('_oa_user_key', JSON.stringify(userInfo));
        } else {
          this.$set(this.info, fieldname, val);
        }
        uni.showToast({
          title: param_sub.successToast,
          icon: 'none'
        });
      });
    },
    //上传图片
    editImg() {
      chooseImage({
        limitNum: 1, //数量
        uploadFileUrl: `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/upload?module=hrm`, //服务器地址
        fileKeyName: 'file', //参数
        showLoading: true,
        hideLoading: false,
        loadingTitle: '上传中...',
        success: res => {
          let resVal = JSON.parse(res),
            param_s = {
              showLoading: false,
              hideLoading: true,
              loadingTitle: '上传中...',
              successToast: '上传成功'
            };
          this.submitInfo('avatar', resVal.object[0].filePath, param_s);
        }
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/my'
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.uni-list {
  margin-bottom: 10px;
  .uni-list-cell {
    .uni-list-cell-navigate {
      padding-right: 60rpx;
      .required_red {
        color: #f00;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 4rpx;
        font-size: 24rpx;
      }
      .signtitle {
        color: #333333;
      }
      .signvalue {
        color: #7f7f7f;
      }
      .listImg {
        width: 50px;
        height: 50px;
        box-shadow: 0 0 5px #ccc;
        border-radius: 50px;
        overflow: hidden;
        image {
          width: 50px;
          height: 50px;
        }
      }
    }
    .describe_tap {
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      .describe {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 10;
        -webkit-box-orient: vertical;
      }
    }
  }
}
</style>
