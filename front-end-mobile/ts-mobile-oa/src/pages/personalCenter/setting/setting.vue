<template>
  <view class="ts-content" v-if="showContent">
    <page-head title="个人设置" @clickLeft="returnBack"></page-head>
    <view
      v-if="$store.state.globalSetting.parttimeSwitch"
      class="part-time-dept-box"
      :style="{
        'padding-bottom':
          employeeDetail.organizationParttimeList &&
          employeeDetail.organizationParttimeList.length
            ? 0
            : '16rpx'
      }"
    >
      <view class="part-time-dept_title">兼职科室</view>
      <view class="part-time-dept_extra" @click="showDeptSelection">
        <view class="part-time-dept_text">
          <text
            v-for="item in employeeDetail.organizationParttimeList"
            :key="item.orgId"
            class="part-time-dept-item"
          >
            {{ item.orgName }}
          </text>
        </view>
        <uni-icons
          :size="30"
          class="uni-icon-wrapper"
          color="#bbb"
          type="arrowright"
        />
      </view>
    </view>
    <view
      class="setting-list"
      v-for="(item, index) in settingList"
      :key="index"
    >
      <uni-section :title="item.title"></uni-section>
      <uni-list>
        <uni-list-item
          v-for="(i, cindex) in item.child"
          :key="cindex"
          :title="i.title"
          :showArrow="i.showArrow"
          :rightText="i.rightText"
          :tipText="i.tipText"
          :show-switch="i.type == 'switch'"
          :switch-checked="i.switchChecked"
          @switchChange="switchChange(index, cindex, $event)"
          @click="clickItem(index, cindex)"
        >
        </uni-list-item>
      </uni-list>
    </view>
    <button type="default" class="logout" @click="manualLogout">
      退出登录
    </button>
    <date-picker
      ref="date"
      startDate="2020-01-01"
      :value="agentTimeArr"
      endDate="2100-12-31"
      :mode="datePickerType"
      :current="true"
      @confirm="onConfirm"
      @cancel="onCancel"
    >
    </date-picker>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState } from 'vuex';
import datePicker from '@/components/picker/date-picker.vue';
import common from '../../../common/js/common';

export default {
  components: {
    datePicker
  },
  data() {
    return {
      showContent: false,
      navigateFlag: false,
      agentTimeArr: [],
      employeeDetail: {},
      personlist: [],
      datePickerType: 'range',
      paretIndex: 0,
      childIndex: 0
    };
  },
  computed: {
    ...mapState(['empid']),
    settingList() {
      let list = [
        {
          title: '隐私设置',
          child: [
            {
              title: '开启短信提醒',
              showArrow: false,
              type: 'switch',
              switchChecked: false,
              fieldName: 'isSmsReminder'
            },
            {
              title: '开启微信消息提醒',
              showArrow: false,
              type: 'switch',
              switchChecked: false,
              fieldName: 'isWxReminder'
            },
            {
              title: '开启号码显示保护',
              showArrow: false,
              type: 'switch',
              tipText: '隐藏部分数字',
              switchChecked: false,
              fieldName: 'isDisplayPhoneNo'
            }
          ]
        },
        {
          title: '流程代理',
          child: [
            {
              title: '代理有效期',
              showArrow: true,
              type: 'date',
              dateType: 'range',
              rightText: '',
              fieldName: 'agentStartTime',
              fieldId: 'agentEndTime'
            },
            {
              title: '代理人',
              showArrow: true,
              type: 'person',
              rightText: '',
              fieldName: 'agentNames',
              fieldId: 'agentIds'
            },
            {
              title: '启用代理',
              showArrow: false,
              type: 'switch',
              switchChecked: false,
              fieldName: 'isEnableProcessAgent'
            }
          ]
        },
        {
          title: '签章设置',
          child: [
            {
              title: '电子签章设置',
              showArrow: true,
              type: 'path',
              tipText: '',
              jumpPath: '/pages/personalCenter/setting/signature'
            }
          ]
        },
        {
          title: '账号设置',
          child: [
            {
              title: '密码管理',
              showArrow: true,
              type: 'path',
              jumpPath: '/pages/personalCenter/setting/password'
            }
          ]
        }
      ];
      this.$store.state.globalSetting.orgCode == 'csjkyy' && list.splice(1, 1);
      return list;
    }
  },
  onLoad() {
    this.getPersonalInfoSettings();
  },
  methods: {
    getPersonalInfoSettings() {
      this.ajax
        .getMyEmployeeDetail({
          userCode: this.empid
        })
        .then(res => {
          let data = res.object;
          data.organizationParttimeList =
            data.organizationParttimeList?.filter(item => item.isBelong == 0) ||
            [];
          this.employeeDetail = data;

          this.showContent = true;

          if (data.agentStartTime && data.agentEndTime) {
            this.agentTimeArr = [data.agentStartTime, data.agentEndTime];
            this.$set(
              this.settingList[1]['child'][0],
              'rightText',
              `${data.agentStartTime} 至 ${data.agentEndTime}`
            );
          }
          this.$set(
            this.settingList[0]['child'][0],
            'switchChecked',
            data.isSmsReminder == 1
          );
          this.$set(
            this.settingList[0]['child'][1],
            'switchChecked',
            data.isWxReminder == 1
          );
          this.$set(
            this.settingList[0]['child'][2],
            'switchChecked',
            data.isDisplayPhoneNo == 1
          );
          this.$set(
            this.settingList[1]['child'][2],
            'switchChecked',
            data.isEnableProcessAgent == 1
          );
          let agentNameEllipsisArr = [],
            agentNameEllipsis = '';
          if (data.agents) {
            data.agents.forEach((item, index) => {
              if (index < 3) {
                agentNameEllipsisArr.push(item.employeeName);
              }
              this.personlist.push({
                name: item.employeeName,
                id: item.employeeNo,
                empFirstName: item.employeeName.substring(
                  item.employeeName.length - 2
                ),
                empHeadImg: item.avatar,
                empDeptName: item.orgName,
                empDutyName: item.positionName,
                empSex: item.gender,
                choose: true
              });
            });
            agentNameEllipsis = agentNameEllipsisArr.join(',');
            if (data.agents.length > 3) {
              agentNameEllipsis += `等${data.agents.length}人`;
            }
          }
          this.$set(
            this.settingList[1]['child'][1],
            'rightText',
            agentNameEllipsis
          );
          this.$set(
            this.settingList[2]['child'][0],
            'tipText',
            data.isUseSignature == 1 ? '启用' : '未启用'
          );
        });
    },
    //
    switchChange(paretIndex, childIndex, value) {
      let elem = this.settingList[paretIndex]['child'][childIndex],
        fieldName = elem.fieldName,
        val = value == true ? '1' : '0';
      this.$set(elem, 'switchChecked', value);
      this.$set(this.employeeDetail, fieldName, val);
      this.submitInfo();
    },
    clickItem(paretIndex, childIndex) {
      this.paretIndex = paretIndex;
      this.childIndex = childIndex;
      let elem = this.settingList[paretIndex]['child'][childIndex];
      if (elem.type == 'path') {
        uni.navigateTo({
          url: elem.jumpPath
        });
      } else if (elem.type == 'date') {
        this.datePickerType = elem.dateType;
        this.showPicker(elem.type);
      } else if (elem.type == 'person') {
        this.showPersonSelet();
      }
    },
    //显示时间选择
    showPicker(ref) {
      this.$refs[ref].show();
    },
    //时间确定
    onConfirm(res) {
      let elem = this.settingList[this.paretIndex]['child'][this.childIndex],
        fieldName = elem.fieldName,
        fieldId = elem.fieldId,
        startDate = `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`,
        endDate = `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`;
      this.$set(elem, 'rightText', res.result);
      this.agentTimeArr = [startDate, endDate];
      this.$set(this.employeeDetail, fieldName, startDate);
      this.$set(this.employeeDetail, fieldId, endDate);
      this.submitInfo();
    },
    //时间取消
    onCancel() {},
    goDetailPage(path) {
      if (this.navigateFlag) {
        return;
      }
      this.navigateFlag = true;
      uni.navigateTo({
        url: path
      });
      setTimeout(() => {
        this.navigateFlag = false;
      }, 200);
    },
    //跳转至人员选择
    showPersonSelet() {
      let elem = this.settingList[this.paretIndex]['child'][this.childIndex],
        fieldName = elem.fieldName,
        fieldId = elem.fieldId;
      //监听事件
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        this.personlist = data;
        let agentNameArr = [],
          agentIdArr = [],
          agentNameEllipsisArr = [],
          agentNameEllipsis;
        data.forEach((item, index) => {
          agentNameArr.push(item.name);
          agentIdArr.push(item.id);
          if (index < 3) {
            agentNameEllipsisArr.push(item.name);
          }
        });
        agentNameEllipsis = agentNameEllipsisArr.join('、');
        if (data.length > 3) {
          agentNameEllipsis += `等${data.length}人`;
        }
        this.$set(this.employeeDetail, fieldName, agentNameArr.join(','));
        this.$set(this.employeeDetail, fieldId, agentIdArr.join(','));
        this.$set(elem, 'rightText', agentNameEllipsis);
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
        this.submitInfo();
      });
      uni.setStorageSync('person_list', JSON.stringify(this.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    showDeptSelection() {
      let _self = this,
        data = _self.employeeDetail.organizationParttimeList || [],
        deptList = data.map(item => {
          return {
            name: item.orgName,
            id: item.orgId
          };
        }),
        disabledDeptList = [
          {
            name: _self.$store.state.userInfo.orgName,
            id: _self.$store.state.userInfo.orgId
          }
        ];
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('deptlist', function(res) {
        let list = res.map(item => {
          return {
            employeeId: _self.employeeDetail.employeeId,
            employeeName: _self.employeeDetail.employeeName,
            employeeNo: _self.employeeDetail.employeeNo,
            isBelong: 0,
            orgId: item.id,
            orgName: item.name
          };
        });
        _self.$set(_self.employeeDetail, 'organizationParttimeList', list);
        uni.removeStorageSync('dept_list');
        //清除监听，不清除会消耗资源
        uni.$off('deptlist');
        _self.submitInfo();
      });
      uni.setStorageSync('dept_list', JSON.stringify(deptList));
      uni.setStorageSync(
        'disabled_dept_list',
        JSON.stringify(disabledDeptList)
      );

      uni.navigateTo({
        url: '/pages/selectDept/select-dept?checkType=checkBox'
      });
    },
    //修改基本信息
    submitInfo() {
      let data = common.deepClone(this.employeeDetail);
      data.organizationParttimeList.push({
        employeeId: data.employeeId,
        employeeName: data.employeeName,
        employeeNo: data.employeeNo,
        isBelong: 1,
        orgId: data.orgId,
        orgName: data.orgName
      });
      this.ajax.updateUserConfig(data).then(res => {
        uni.showToast({
          icon: 'none',
          title: '修改成功'
        });
      });
    },
    manualLogout() {
      this.ajax
        .signOut({
          usercode: this.empid
        })
        .then(res => {
          if (!window.__POWERED_BY_QIANKUN__) {
            uni.reLaunch({
              url: '/pages/login/login'
            });
          } else {
            this.$childComponentOutLogin(res.object);
          }
        });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/my'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .uni-list {
    margin-bottom: 10px;

    .uni-list-cell-navigate {
      flex: 1;
    }

    .item_title {
      color: #333333;
    }

    .item_icon {
      margin-right: 12 rpx;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      line-height: 1;

      .oa-icon {
        font-size: 36 rpx;
      }
    }

    &::after {
      height: 0;
    }

    .item_title {
      display: flex;
      flex: 1;
      flex-direction: column;
      overflow: hidden;
      font-size: 32 rpx;
    }
  }

  .part-time-dept-box {
    background-color: #ffffff;
    font-size: 32rpx;
    padding: 16rpx 30rpx;
    display: flex;
    align-items: center;
    .part-time-dept_title {
      color: #333;
      padding-right: 16rpx;
    }
    .part-time-dept_extra {
      flex: 1;
      display: flex;
      align-items: center;
      color: #666;
      .part-time-dept_text {
        flex: 1;
        text-align: right;
        .part-time-dept-item {
          font-size: 28rpx;
          padding: 4rpx 16rpx;
          margin-left: 16rpx;
          background-color: #f2f8ff;
          border-radius: 28rpx;
          display: inline-block;
          margin-bottom: 16rpx;
          &:first-child {
            margin-left: 0;
          }
        }
      }
    }
  }

  .logout {
    background-color: #ffffff;
    font-size: 28rpx;
    border-radius: 0;
    margin-top: 20rpx;
    padding: 0;
    height: 90rpx;
    line-height: 90rpx;

    &::after {
      border: 0;
      border-radius: 0;
    }
  }
}
</style>
