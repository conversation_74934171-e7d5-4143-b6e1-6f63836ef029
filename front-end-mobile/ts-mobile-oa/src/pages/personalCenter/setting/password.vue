<template>
  <view class="ts-content">
    <page-head
      title="修改密码"
      :isleft="hasReturnBtn"
      @clickLeft="returnBack"
      rightText="提交"
      @clickRight="modifySubmit"
    />
    <view class="uni-form-item uni-column ts-form">
      <view class="ts-form-item" v-for="(item, index) in formList" :key="index">
        <view class="uni-list-cell-left">
          <view class="uni-label">{{ item.label }}</view>
        </view>
        <view class="uni-input-wrapper">
          <input
            class="uni-input"
            :placeholder="item.placeholder"
            v-model="formData[item.prop]"
            :password="!item.showPassword"
            oncopy="return false"
            onpaste="return false"
          />
          <uni-icons
            :size="48"
            v-if="formData[item.prop]"
            class="uni-icon-wrapper"
            color="#bbb"
            type="clear"
            @tap="formData[item.prop] = ''"
          />
          <uni-icons
            :size="48"
            class="uni-icon-wrapper"
            color="#bbb"
            :type="item.showPassword ? 'eye-filled' : 'eye'"
            @tap="item.showPassword = !item.showPassword"
          />
        </view>
      </view>
    </view>
    <view class="tips">* 提示：{{ tips }}</view>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState, mapMutations } from 'vuex';
import { pwdCheckStrong } from '@/common/js/password.js';
import { Encrypt } from '@/common/js/encrypt.js';
export default {
  data() {
    return {
      fromPage: '',
      formList: [
        {
          prop: 'oldVal',
          label: '旧密码',
          placeholder: '请输入旧密码',
          showPassword: false
        },
        {
          prop: 'newVal',
          label: '新密码',
          placeholder: '请输入新密码',
          showPassword: false
        },
        {
          prop: 'confirmVal',
          label: '确认新密码',
          placeholder: '请确认新密码',
          showPassword: false
        }
      ],
      formData: {},
      typeStr: {
        1: '大写字母',
        2: '小写字母',
        3: '数字',
        4: '特殊字符'
      },
      hasReturnBtn: true,
      pawLength: 6,
      pawRuleList: [],
      tips: ''
    };
  },
  computed: {
    ...mapState(['usercode', 'globalSetting'])
  },
  async onLoad(opt) {
    this.fromPage = opt.fromPage;
    if (uni.getStorageSync('_oa_token')) {
      this.changeState({
        token: uni.getStorageSync('_oa_token'),
        usercode: uni.getStorageSync('_oa_usercode')
      });
    }
    let isEassyPass = uni.getStorageSync('eassy_password');
    this.hasReturnBtn = isEassyPass != 'true';
    await this.ajax.getAllGlobalSetting().then(res => {
      this.$store.commit('changeState', {
        globalSetting: res.object
      });
      this.pawLength = this.globalSetting.passwordLength || 6;
      this.pawRuleList = res.object.passwordRule
        ? res.object.passwordRule.split(',')
        : [];
      let pawRuleStrList = this.pawRuleList.map(item => {
        return this.typeStr[item];
      });
      this.tips = `密码不能少于${res.object.passwordLength || 6}位数${
        pawRuleStrList.length ? `，且必须包含${pawRuleStrList.join('、')}` : ''
      }`;
    });
  },
  methods: {
    ...mapMutations(['changeState', 'logout']),
    modifySubmit() {
      let pwdStrong = pwdCheckStrong(this.formData.confirmVal, this.pawLength);
      try {
        this.formList.forEach(item => {
          if (!this.formData[item.prop]) throw item.placeholder;
        });
        if (this.formData.newVal != this.formData.confirmVal)
          throw '两次填写的密码不一致';
        else if (this.formData.oldVal == this.formData.newVal) {
          throw '新密码不能和旧密码一致';
        } else if (
          this.formData.confirmVal.length < this.pawLength ||
          pwdStrong.level < this.pawRuleList.length ||
          pwdStrong.level == -1
        ) {
          throw this.tips;
        }
        for (let i = 0; i < this.pawRuleList.length; i++) {
          if (!pwdStrong.checkType[this.pawRuleList[i]]) {
            throw this.tips;
            break;
          }
        }
      } catch (err) {
        uni.showToast({
          title: err,
          icon: 'none'
        });
        return;
      }
      this.ajax
        .editPassWord({
          usercode: this.usercode,
          oldpassword: Encrypt(this.formData.oldVal.trim()).toString(),
          newpassword: Encrypt(this.formData.confirmVal.trim()).toString()
        })
        .then(res => {
          uni.showToast({
            icon: 'none',
            title: '密码修改成功'
          });
          setTimeout(() => {
            if (!window.__POWERED_BY_QIANKUN__) {
              if (uni.getStorageSync('_oa_token')) {
                uni.removeStorageSync('_oa_token');
                uni.removeStorageSync('_oa_id');
              }
              uni.removeStorageSync('eassy_password');
              this.logout();
            } else {
              this.$childComponentOutLogin();
            }
          }, 1500);
        });
    },
    returnBack() {
      if (this.fromPage == 'login') {
        this.$parentTypeFun({
          type: 'jumpPage',
          path: '/login'
        });
      } else {
        this.$parentTypeFun({
          type: 'jumpPage',
          path: '/my'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-form {
  padding: 0;
  position: relative;
  .ts-form-item {
    background-color: #ffffff;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    flex-direction: row;
    position: relative;

    .uni-input-wrapper {
      display: -webkit-box;
      display: -webkit-flex;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding-right: 30rpx;

      .uni-input {
        font-size: 30 rpx;
        padding: 10px 0;
        flex: 1;
        background-color: tsnsparent;
        font-size: 32 rpx;
      }
    }

    .uni-label {
      font-size: 30 rpx;
      padding: 10px 0;
      text-indent: 0;
      color: #333333;
    }

    &::after {
      content: '';
      position: absolute;
      z-index: 3;
      right: 0;
      bottom: 0;
      left: 15px;
      height: 1px;
      content: '';
      -webkit-tsnsform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }

    &:last-child::after {
      height: 0;
    }
  }

  &::before {
    position: absolute;
    z-index: 10;
    right: 0;
    top: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-tsnsform: scaleY(0.5);
    tsnsform: scaleY(0.5);
    background-color: #eeeeee;
  }

  &::after {
    position: absolute;
    z-index: 10;
    right: 0;
    bottom: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-tsnsform: scaleY(0.5);
    tsnsform: scaleY(0.5);
    background-color: #eeeeee;
  }
}
.tips {
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  color: $uni-color-error;
  font-size: $uni-font-size-xsm;
}
</style>
