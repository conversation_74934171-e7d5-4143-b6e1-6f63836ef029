<template>
  <view class="ts-content">
    <page-head title="版本信息" @clickLeft="returnBack"></page-head>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        @getDatas="getFileList"
        @setDatas="setFileList"
        @datasInit="datasInit"
      >
        <uni-list>
          <uni-list-item
            v-for="(item, index) in fileList"
            :key="index"
            :title="'版本' + item.version"
            :note="item.uploadTime ? item.uploadTime : item.createDate"
            @click="showDetail(item.id)"
          ></uni-list-item>
        </uni-list>
      </mescroll>
    </view>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState } from 'vuex';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import uniList from '@/components/uni-list/uni-list.vue';
import uniListItem from '@/components/uni-list-item/uni-list-item.vue';
export default {
  components: {
    uniList,
    uniListItem,
    mescroll
  },
  data() {
    return {
      fileList: []
    };
  },
  computed: {
    ...mapState(['empcode'])
  },
  methods: {
    async getFileList(page, successCallback, errorCallback, keywords) {
      await this.ajax
        .getVersionList({
          userCode: this.empcode,
          pageNo: page.num,
          pageSize: page.size
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setFileList(row) {
      this.fileList = this.fileList.concat(row);
    },
    datasInit() {
      this.fileList = [];
    },
    showDetail(id) {
      uni.navigateTo({
        url: `/pages/personalCenter/settingPersonal/version-detail?versionId=${id}`
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/my'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  .mescroll-content {
    position: absolute;
    width: 100%;
    top: 44px;
    bottom: 0;
  }
}
</style>
