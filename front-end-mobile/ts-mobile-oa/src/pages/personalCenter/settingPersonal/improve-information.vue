<template>
  <view class="ts-content">
    <page-head
      title="完善资料"
      @clickLeft="returnBack"
      rightText="保存"
      @clickRight="submitInfo"
    ></page-head>
    <view class="uni-list" v-if="showContent">
      <view class="uni-list-cell">
        <view
          class="uni-list-cell-navigate uni-navigate-right"
          @click="editImg"
        >
          <text class="signtitle">头像</text>
          <view class="listImg">
            <image
              v-cloak
              :src="
                info.empHeadImg
                  ? $config.BASE_HOST + info.empHeadImg
                  : '../../../static/img/headImg.png'
              "
              mode=""
            ></image>
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate">
          <view class="signtitle">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            <text>姓名</text>
          </view>
          <view class="signvalue">
            <text>{{ info.empName }}</text>
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate uni-navigate-right">
          <view class="signtitle">
            <text>昵称</text>
          </view>
          <view class="signvalue">
            <input
              type="text"
              v-model="info.empNickName"
              placeholder="请输入昵称"
            />
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate uni-navigate-right">
          <view class="signtitle">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            <text>性别</text>
          </view>
          <view
            class="signvalue"
            data-tap="empSex"
            data-ref="popup"
            data-popup-type="bottom"
            data-popup-choice="radio"
            @tap="showPopup"
          >
            <text>{{ info.empSex == 0 ? '男' : '女' }}</text>
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate uni-navigate-right">
          <view class="signtitle">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            <text>身份证</text>
          </view>
          <view class="signvalue">
            <input
              type="text"
              v-model="info.empIdcard"
              placeholder="请输入身份证号"
            />
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate">
          <view class="signtitle">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            <text>工号</text>
          </view>
          <view class="signvalue">
            <text>{{ info.empCode }}</text>
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate">
          <view class="signtitle">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            <text>科室</text>
          </view>
          <view class="signvalue">
            <text>{{ info.empDeptName }}</text>
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate uni-navigate-right">
          <view class="signtitle">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            <text>职务</text>
          </view>
          <view
            class="signvalue"
            data-tap="empDuty"
            data-ref="popup"
            data-popup-type="bottom"
            data-popup-choice="radio"
            @tap="showPopup"
          >
            <text>{{ info.empDutyName }}</text>
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate uni-navigate-right">
          <view class="signtitle">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            <text>入职时间</text>
          </view>
          <view
            class="signvalue"
            data-tap="empHiredate"
            data-ref="date"
            data-mode="date"
            data-fields="month"
            :data-value="info.empHiredate"
            @tap="showPicker"
          >
            <text v-if="info.empHiredate">{{
              info.empHiredate | dateFilter
            }}</text>
            <input v-else :disabled="true" placeholder="请选择入职时间" />
          </view>
        </view>
      </view>
    </view>
    <view class="uni-list">
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate uni-navigate-right">
          <view class="signtitle">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            <text>手机号码(1)</text>
          </view>
          <view class="signvalue">
            <input
              class="uni-input-input"
              :type="inputType"
              pattern="[0-9]*"
              v-model="info.empPhone"
              data-tap="empPhone"
              @input="valueChange"
              placeholder="请输入手机号码"
            />
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate uni-navigate-right">
          <view class="signtitle">
            <text>手机号码(2)</text>
          </view>
          <view class="signvalue">
            <input
              class="uni-input-input"
              :type="inputType"
              pattern="[0-9]*"
              v-model="info.empPhoneSecond"
              data-tap="empPhoneSecond"
              @input="valueChange"
              placeholder="请输入手机号码"
            />
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate uni-navigate-right">
          <view class="signtitle">
            <text>移动短号</text>
          </view>
          <view class="signvalue">
            <input
              class="uni-input-input"
              :type="inputType"
              pattern="[0-9]*"
              v-model="info.empBusinessPhone"
              data-tap="empBusinessPhone"
              @input="valueChange"
              placeholder="请输入移动短号"
            />
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate uni-navigate-right">
          <view class="signtitle">
            <text>联通短号</text>
          </view>
          <view class="signvalue">
            <input
              class="uni-input-input"
              :type="inputType"
              pattern="[0-9]*"
              v-model="info.empUnicomBusinessPhone"
              data-tap="empUnicomBusinessPhone"
              @input="valueChange"
              placeholder="请输入联通短号"
            />
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate uni-navigate-right">
          <view class="signtitle">
            <text>电信短号</text>
          </view>
          <view class="signvalue">
            <input
              class="uni-input-input"
              :type="inputType"
              pattern="[0-9]*"
              v-model="info.empTelecomBusinessPhone"
              data-tap="empTelecomBusinessPhone"
              @input="valueChange"
              placeholder="请输入电信短号"
            />
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate uni-navigate-right">
          <view class="signtitle">
            <text>办公室号码</text>
          </view>
          <view class="signvalue">
            <input
              v-model="info.empShortPhone"
              placeholder="请输入办公室号码"
            />
          </view>
        </view>
      </view>
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate uni-navigate-right">
          <view class="signtitle">
            <text>邮箱</text>
          </view>
          <view class="signvalue">
            <input
              type="text"
              v-model="info.empEmail"
              placeholder="请输入邮箱"
            />
          </view>
        </view>
      </view>
    </view>
    <view class="uni-list">
      <view class="uni-list-cell">
        <view class="uni-list-cell-navigate describe_tap">
          <view class="signtitle">
            <text>个人简介</text>
          </view>
          <view class="signvalue describe" style="width: 100%;">
            <textarea
              style="width: 100%;"
              v-model="info.empDescribe"
              placeholder="请输入个人简介"
            />
          </view>
        </view>
      </view>
    </view>
    <date-picker
      ref="date"
      :endDate="pickerEnd"
      :mode="picker.mode"
      :disabledAfter="picker.disabledAfter"
      @confirm="onConfirm"
      :fields="picker.fields"
      :value="info[picker.tap]"
    ></date-picker>
    <uni-popup
      :type="picker.popupType"
      :popup-choice="picker.popupChoice"
      ref="popup"
    >
      <view
        v-if="picker.popupChoice == 'radio'"
        class="contact_list scroll_list"
      >
        <view
          class="contact_item"
          v-for="item in picker.list"
          :key="item.value"
          :data-column-value="item.value"
          :data-column-text="item.text"
          data-ref="popup"
          @tap="singleColumn"
        >
          <text class="contact_item_text">{{ item.text }}</text>
          <view class="contact_item-icon">
            <uni-icons
              v-if="info[picker.tap] == item.value"
              type="checkmarkempty"
              color="#005BAC"
              size="44"
            />
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState, mapMutations } from 'vuex';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import datePicker from '@/components/picker/date-picker.vue';
import graceChecker from '@/common/js/graceChecker.js';
import { chooseImage } from '@/common/js/uploadImg.js';
export default {
  components: {
    datePicker,
    uniPopup
  },
  data() {
    return {
      showContent: false,
      info: {},
      empcode: '',
      token: '',
      numberType: '',
      valueList: {
        empSex: [
          {
            value: 0,
            text: '男'
          },
          {
            value: 1,
            text: '女'
          }
        ]
      },
      picker: {
        tap: '', //点击的元素
        mode: 'date', //日期选择的类型
        fields: 'day', //日期选择的颗粒度
        value: '', //已选值
        list: '', //选项
        popupType: '', //弹出层弹出方式
        popupChoice: '' //弹出层选择方式
      },
      pickerEnd: '',
      passwordValue: ''
    };
  },
  computed: {
    ...mapState(['hasbind'])
  },
  created() {
    this.inputType = uni.getSystemInfoSync().platform == 'ios' ? '' : 'number';
  },
  onLoad(opt) {
    this.empcode = opt.empcode;
    this.token = opt.token;
    this.changeState({
      token: opt.token
    });
    this.passwordValue = decodeURIComponent(opt.pwd);
    this.pickerEnd = this.$common.getDate('hour').timeStr;
    this.getPersonalInfo();
  },
  filters: {
    dateFilter(time) {
      let getDate = new Date(time.replace(/-/g, '/'));
      let getDateStr = `${getDate.getFullYear()}年${getDate.getMonth() + 1}月`;
      return getDateStr;
    }
  },
  methods: {
    ...mapMutations(['changeState']),
    getPersonalInfo() {
      this.ajax
        .getPersonalInformationSettings({
          userCode: this.empcode
        })
        .then(res => {
          let data = res.object;
          this.info = data;
          this.getDutyList();
        });
    },
    getDutyList() {
      this.ajax
        .getDutyList({
          pageSize: 10000
        })
        .then(res => {
          let list = [];
          res.rows.forEach(item => {
            list.push({
              value: item.dutyCode,
              text: item.dutyName
            });
          });
          this.$set(this.valueList, 'empDuty', list);
          this.showContent = true;
        });
    },
    //显示时间弹出层
    showPicker(e) {
      let data = e.currentTarget.dataset;
      this.$set(this.picker, 'mode', data.mode);
      this.$set(this.picker, 'fields', data.fields);
      this.$set(this.picker, 'tap', data.tap);
      this.$set(this.picker, 'value', data.value);
      this.$set(this.picker, 'disabledAfter', true);
      this.$nextTick(() => {
        this.$refs[data.ref].show();
      });
    },
    //时间选择确认
    onConfirm(res) {
      this.$set(this.info, this.picker.tap, `${res.result}-01 `);
    },
    //显示弹出层
    showPopup(e) {
      let data = e.currentTarget.dataset;
      this.$set(this.picker, 'tap', data.tap);
      this.$set(this.picker, 'popupType', data.popupType);
      this.$set(this.picker, 'popupChoice', data.popupChoice);
      this.$set(this.picker, 'list', this.valueList[data.tap]);
      this.$nextTick(() => {
        this.$refs[data.ref].open();
      });
    },
    //单选
    singleColumn(e) {
      let data = e.currentTarget.dataset;
      if (this.picker.tap == 'empDuty') {
        this.$set(this.info, 'empDutyId', data.columnValue);
        this.$set(this.info, 'empDutyName', data.columnText);
      } else {
        this.$set(this.info, this.picker.tap, data.columnValue);
      }
      this.$nextTick(() => {
        this.$refs[data.ref].close();
      });
    },
    //上传图片
    editImg() {
      chooseImage({
        limitNum: 1, //数量
        uploadFileUrl: `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/upload?module=hrm`, //服务器地址
        fileKeyName: 'file', //参数
        showLoading: true,
        hideLoading: true,
        loadingTitle: '上传中...',
        success: res => {
          let resVal = JSON.parse(res);
          this.$set(this.info, 'empHeadImg', resVal.object[0].filePath);
        }
      });
    },
    valueChange(e) {
      let data = e.currentTarget.dataset;
      this.$set(this.info, data.tap, e.target.value.replace(/[^\d]/g, ''));
    },
    //修改基本信息
    submitInfo() {
      //定义表单规则
      let rule = [
        {
          filedKey: 'empSex',
          required: true,
          checkType: 'notnull',
          errorMsg: '请选择性别'
        },
        {
          filedKey: 'empIdcard',
          required: true,
          checkType: 'idcard',
          errorMsg: '请输入身份证号码',
          otherErrorMsg: '身份证号码输入不合法'
        },
        {
          filedKey: 'empDutyName',
          required: true,
          checkType: 'notnull',
          errorMsg: '请选择职务'
        },
        {
          filedKey: 'empHiredate',
          required: true,
          checkType: 'notnull',
          errorMsg: '请选择入职时间'
        },
        {
          filedKey: 'empPhone',
          required: true,
          checkType: 'phone',
          errorMsg: '请输入手机号码（1）',
          otherErrorMsg: '手机号码（1）输入有误'
        },
        {
          filedKey: 'empPhoneSecond',
          checkType: 'phone',
          errorMsg: '请输入手机号码（2）',
          otherErrorMsg: '手机号码（2）输入有误'
        },
        {
          filedKey: 'empEmail',
          checkType: 'email',
          errorMsg: '请输入邮箱',
          otherErrorMsg: '邮箱输入有误'
        }
      ];
      //进行表单检查
      let checkRes = graceChecker.check(this.info, rule);
      if (!checkRes) {
        uni.showToast({ title: graceChecker.error, icon: 'none' });
        return false;
      }
      this.info.empAge = this.$common.getAgeFromIdCard(this.info.empIdcard);
      this.info.empBirth = this.$common.getBirthdayFromIdCard(
        this.info.empIdcard
      );
      this.ajax.editEmployee(this.info).then(res => {
        uni.showToast({
          title: '保存成功',
          icon: 'none'
        });
        let userInfo = {
          username: this.info.username,
          empsex: this.info.empSex,
          password: this.passwordValue,
          usercode: this.userAccounts,
          empcode: this.empcode,
          deptname: this.info.empDeptName,
          deptid: this.info.empDeptId,
          dutyname: this.info.empDutyName,
          headimg: this.info.empHeadImg,
          hasbind: true
        };
        uni.setStorageSync('_oa_user_key', JSON.stringify(userInfo));
        //更新状态
        this.changeState(data);
        if (!this.hasbind) {
          window.location.href = `${this.$config.BASE_HOST}/ts-information/oa/bindUser?userAccounts=${this.accountValue}`;
        }
        this.$nextTick(() => {
          uni.reLaunch({
            url: '/pages/index/index'
          });
        });
      });
    },
    returnBack() {
      uni.reLaunch({
        url: '/pages/login/login'
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.uni-input-input::-webkit-input-placeholder {
  color: #bbb;
}
.scroll_list {
  max-height: 800rpx;
  overflow: auto;
}
.contact_list {
  background-color: #ffffff;
  .contact_item {
    padding: 22rpx 30rpx;
    font-size: 28rpx;
    color: #333333;
    position: relative;
    display: flex;
    align-items: center;
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 30rpx;
      bottom: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &:last-child::after {
      height: 0;
    }
    .contact_item_text {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
    }
    .contact_item-icon {
      line-height: 1;
    }
  }
}
.uni-list {
  margin-bottom: 10px;
  .uni-list-cell {
    .uni-list-cell-navigate {
      padding-right: 60rpx;
      .signtitle {
        color: #333333;
        text-align: left;
        position: relative;
        .required_red {
          color: #f00;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: -30rpx;
          font-size: 24rpx;
        }
      }
      .signvalue {
        color: #7f7f7f;
        text-align: right;
        flex: 1;
        min-height: 50rpx;
      }
      .listImg {
        width: 50px;
        height: 50px;
        box-shadow: 0 0 5px #ccc;
        border-radius: 50px;
        overflow: hidden;
        image {
          width: 50px;
          height: 50px;
        }
      }
    }
    .describe_tap {
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      .describe {
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 10;
        -webkit-box-orient: vertical;
      }
    }
  }
}
</style>
