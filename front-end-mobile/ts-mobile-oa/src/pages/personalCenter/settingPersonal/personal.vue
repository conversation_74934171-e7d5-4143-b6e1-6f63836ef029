<template>
  <view class="ts-content">
    <page-head title="个人设置" @clickLeft="returnBack"></page-head>
    <view class="uni-list" v-for="(item, index) in personalList" :key="index">
      <view class="uni-list-cell" v-for="subItem in item" :key="subItem.url">
        <view
          class="uni-list-cell-navigate uni-navigate-right"
          @click="goDetailPage(subItem.url)"
        >
          <view class="item_icon" v-if="subItem.icon">
            <text
              :style="{ color: subItem.color }"
              class="uni-icons oa-icon"
              :class="subItem.icon"
            ></text>
          </view>
          <text class="item_title">{{ subItem.text }}</text>
        </view>
      </view>
    </view>
    <button type="default" class="logout" @click="manualLogout">
      退出登录
    </button>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState, mapMutations } from 'vuex';

export default {
  data() {
    return {
      navigateFlag: false,
      personalList: [
        [
          {
            text: '隐私设置',
            color: '#666666',
            //icon: 'oa-icon-yinsishezhi',
            url: '/pages/personalCenter/settingPersonal/privacy'
          },
          {
            text: '修改密码',
            color: '#666666',
            //icon: 'oa-icon-xiugaimima',
            url: '/pages/personalCenter/setting/password'
          },
          {
            text: '版本信息',
            color: '#666666',
            //icon: 'oa-icon-banbenxinxi',
            url: '/pages/personalCenter/settingPersonal/version'
          }
        ]
      ]
    };
  },
  computed: {
    ...mapState(['empcode'])
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
  },
  methods: {
    ...mapMutations(['logout']),
    goDetailPage(path) {
      if (this.navigateFlag) {
        return;
      }
      this.navigateFlag = true;
      uni.navigateTo({
        url: `${path}?fromPage=${this.fromPage}`
      });
      setTimeout(() => {
        this.navigateFlag = false;
      }, 200);
    },
    manualLogout() {
      this.ajax
        .signOut({
          userCode: this.empcode
        })
        .then(res => {
          this.logout();
          uni.reLaunch({
            url: '/pages/login/login'
          });
        });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/my'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .uni-list {
    margin-bottom: 10px;
    .uni-list-cell-navigate {
      flex: 1;
    }
    .item_title {
      color: #333333;
    }
    .item_icon {
      margin-right: 12rpx;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      line-height: 1;
      .oa-icon {
        font-size: 36rpx;
      }
    }
    &::after {
      height: 0;
    }
    .item_title {
      display: flex;
      flex: 1;
      flex-direction: column;
      overflow: hidden;
      font-size: 32rpx;
    }
  }
  .logout {
    background-color: #ffffff;
    font-size: 28rpx;
    padding: 20rpx;
    border-radius: 0;
    &::after {
      border: 0;
      border-radius: 0;
    }
  }
}
</style>
