<template>
  <view class="ts-content">
    <page-head title="更新详情" @clickLeft="returnBack"></page-head>
    <view class="version_head">
      <text class="version_head_title">{{ info.version }}</text>
      <text class="version_head_date">{{
        info.uploadTime ? info.uploadTime : info.createDate
      }}</text>
    </view>
    <view class="version_content">
      <view class="version_content_title">【最近更新】</view>
      <rich-text :nodes="content"></rich-text>
    </view>
    <view v-if="info.remark" class="version_remark">
      {{ info.remark }}
    </view>
    <view
      v-if="fileList.length > 0"
      class="version_file"
      :style="{ transform: transform }"
    >
      <uni-transition
        :mode-class="['fade']"
        :styles="maskClass"
        :show="showTrans"
        :duration="300"
        @click="close"
      />
      <view
        class="oa-icon oa-icon-fujian version_file_statistic"
        @click="togglePopup()"
      >
        {{ fileList.length }}个附件</view
      >
      <!-- <uni-list v-if="showTrans">
				 <uni-list-item  v-for="(item,index) in fileList" :key="index" :title="item.title" :number="item.number" :thumb="item.thumb" @click="goDetailPage(item.path)" :iconStyleStr="item.iconStyleStr"></uni-list-item>
			</uni-list> -->
      <view v-if="showTrans" class="version_file_list">
        <view
          v-for="(item, index) in fileList"
          :key="index"
          class="version_file_item"
          @click="downloadFile(item.id, item.saveName)"
        >
          <text
            class="oa-icon version_file_item_icon"
            :class="'oa-icon-' + $oaModule.formatFileType(item.type)"
          ></text>
          <view class="version_file_item_text">
            <text class="version_file_item_title">{{ item.name }}</text>
            <text class="version_file_item_size">{{
              item.fileSize | fileSizeFilter
            }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import uniTransition from '@/components/uni-transition/uni-transition.vue';
import Base64 from '@/common/js/base64.min.js';
export default {
  components: {
    uniTransition
    // uniList,
    // uniListItem
  },
  data() {
    return {
      info: {}, //版本信息
      content: '', //版本更新内容
      fileList: [], //附件列表
      maskClass: {
        //背景遮罩层样式
        position: 'fixed',
        bottom: 0,
        top: 0,
        left: 0,
        right: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.4)'
      },
      showTrans: false,
      transform: 'translateY(calc(100% - 88rpx))'
    };
  },
  onLoad(opt) {
    this.getVersionDetail(opt.versionId);
  },
  methods: {
    getVersionDetail(id) {
      let _self = this;
      _self.ajax
        .getWxVersionDetailsByVersionId({
          versionId: id
        })
        .then(res => {
          let data = res.object;
          _self.info = data.version;
          _self.content = data.version.content;
          _self.fileList = data.fileList;
        });
    },
    clear(e) {
      // TODO nvue 取消冒泡
      e.stopPropagation();
    },
    togglePopup() {
      let _self = this;
      if (_self.showTrans) {
        _self.close();
      } else {
        _self.open();
      }
    },
    close() {
      let _self = this;
      _self.transform = 'translateY(calc(100% - 88rpx))';
      _self.$nextTick(() => {
        clearTimeout(_self.timer);
        _self.timer = setTimeout(() => {
          _self.showTrans = false;
        }, 300);
      });
    },
    open() {
      let _self = this;
      _self.showTrans = true;
      _self.$nextTick(() => {
        clearTimeout(_self.timer);
        _self.timer = setTimeout(() => {
          _self.transform = '';
        }, 50);
      });
    },
    downloadFile(id, fileName) {
      let _self = this,
        filePath = `${
          _self.$config.ENABLE_FILE_PREVIEW
            ? _self.$config.DOCUMENT_BASE_HOST
            : _self.$config.BASE_HOST
        }/ts-basics-bottom/fileAttachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (_self.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            _self.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      } else {
        _self.$downloadFile.downloadFile(filePath);
      }
    },
    returnBack() {
      uni.redirectTo({
        url: '/pages/personalCenter/settingPersonal/version'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  background-color: #ffffff;
  .version_head {
    padding: 20rpx 30rpx;
    font-size: 28rpx;
    .version_head_title {
      font-weight: bold;
      color: #333333;
    }
    .version_head_date {
      color: #999999;
      float: right;
    }
  }
  .version_content {
    padding: 0 30rpx 20rpx;
    color: #333333;
  }
  .version_remark {
    padding: 0 30rpx 20rpx;
  }
  .version_file {
    background-color: #ffffff;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    transition-duration: 0.3s;
    transition-property: all;
    transition-timing-function: linear;
    .version_file_statistic {
      color: #10b47f;
      text-align: center;
      background-color: #ffffff;
      font-size: 28rpx;
      position: relative;
      z-index: 10;
      height: 88rpx;
      line-height: 88rpx;
    }
    .version_file_list {
      padding: 10rpx 20rpx 20rpx;
      background-color: #ffffff;
      z-index: 10;
      position: relative;
      .version_file_item {
        text-decoration: none;
        display: block;
        font-size: 28rpx;
        color: #333333;
        padding: 6rpx 20rpx;
        border: 1px solid #dddddd;
        border-radius: 5px;
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        .version_file_item_icon {
          font-size: 40rpx;
          margin-right: 20rpx;
          color: $theme-color;
        }
        .version_file_item_text {
          flex: 1;
          .version_file_item_title {
            color: #333333;
            font-size: 28rpx;
            margin-right: 20rpx;
          }
          .version_file_item_size {
            margin-left: 5px;
            font-size: 24rpx;
            color: #999999;
          }
        }
      }
    }
  }
}
.slide-bottom-in {
  transform: translateY(100%);
}

.slide-bottom-active {
  transform: translateY(0);
}
</style>
