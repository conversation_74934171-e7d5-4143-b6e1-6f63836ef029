<template>
  <view class="ts-content">
    <page-head
      title="自定义群组设置"
      @clickLeft="returnBack"
      :rightText="list.lenght != 0 ? '新增' : ''"
      @clickRight="addGroup"
    ></page-head>
    <view v-if="showContent" class="group_content">
      <view class="group_list" v-if="list.lenght != 0">
        <uni-collapse>
          <uni-collapse-item
            v-for="item in list"
            :title="item.groupName"
            :key="item.groupId"
          >
            <view class="edit" slot="subtext" @click.stop.self="editGroup(item)"
              >编辑</view
            >
            <view
              class="contact-item"
              v-for="row in item['employeeList']"
              :key="row.empCode"
            >
              <image
                class="iconImg"
                v-if="row.empHeadImg ? true : false"
                :src="$config.BASE_HOST + row.empHeadImg"
                mode="aspectFill"
              ></image>
              <view
                v-else
                class="iconImg"
                :class="row.empSex == 0 ? 'sexMan' : 'sexWoman'"
              >
                {{ row.empName.substring(row.empName.length - 2) }}
              </view>
              <view class="userInfo">
                <text>{{ row.empName }}</text>
                <text class="description"
                  >{{ row.empDeptName }}&nbsp;&nbsp;{{ row.empDutyName }}</text
                >
              </view>
            </view>
          </uni-collapse-item>
        </uni-collapse>
      </view>
      <view class="nothing" v-else>
        <view class="img_content">
          <image
            class="nothing_img"
            src="../../../static/img/nothing.png"
            mode="aspectFit"
          ></image>
        </view>
        <view class="tips_text">
          <text>暂无自定义群组，现在开始添加吧</text>
        </view>
        <button class="addBtn" size="mini" type="primary" @click="newAddGroup">
          添加自定义群组
        </button>
      </view>
    </view>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState } from 'vuex';
export default {
  data() {
    return {
      showContent: false,
      fromPage: '',
      list: []
    };
  },
  computed: {
    ...mapState(['empid'])
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.getOrgGroupList();
  },
  methods: {
    getOrgGroupList() {
      this.ajax
        .getOrgGroupList({
          pageSize: 20000,
          pageNo: 1,
          empName: this.empid
        })
        .then(res => {
          this.list = res;
          this.showContent = true;
        });
    },
    newAddGroup() {
      uni.navigateTo({
        url: `/pages/personalCenter/settingPersonal/custom-groups-add?fromPage=${this.fromPage}&type=add`
      });
    },
    //新增
    addGroup() {
      if (this.list.lenght === 0) return false;
      uni.navigateTo({
        url: `/pages/personalCenter/settingPersonal/custom-groups-add?fromPage=${this.fromPage}&type=add`
      });
    },
    editGroup(data) {
      uni.setStorageSync('group', data);
      uni.navigateTo({
        url: `/pages/personalCenter/settingPersonal/custom-groups-add?fromPage=${this.fromPage}&type=edit`
      });
    },
    //返回
    returnBack() {
      uni.redirectTo({
        url: `/pages/personalCenter/settingPersonal/personal?fromPage=${this.fromPage}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.group_list {
  height: 100%;
  width: 100%;
  overflow: auto;
  .edit {
    font-size: 24rpx;
    border: 1px solid #005bac;
    color: #005bac;
    padding: 4rpx 16rpx;
    margin-right: 16rpx;
    border-radius: 32rpx;
  }
  .contact-item {
    padding: 20rpx;
    display: flex;
    align-items: center;
    background-color: #ffffff;
    .userInfo {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      color: #333333;
      font-weight: bold;
      .description {
        color: #666666;
        font-weight: normal;
      }
    }
    .iconImg {
      width: 40px;
      height: 40px;
      margin: 0 20rpx;
      border-radius: 100%;
      color: #ffffff;
      text-align: center;
      line-height: 2.8;
    }
    .sexMan {
      background-color: $sexman-color;
    }
    .sexWoman {
      background-color: $sexwoman-color;
    }
  }
}
.nothing {
  position: absolute;
  top: 40%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .img_content {
    width: 300rpx;
    height: 300rpx;
    .nothing_img {
      width: 100%;
      height: 100%;
    }
  }
  .tips_text {
    color: #666666;
  }
  .addBtn {
    padding: 10rpx 20rpx;
    margin-top: 30rpx;
  }
}
</style>
