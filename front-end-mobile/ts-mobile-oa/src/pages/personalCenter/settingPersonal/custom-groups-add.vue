<template>
  <view class="ts-content">
    <page-head
      :title="title"
      @clickLeft="returnBack"
      rightText="保存"
      @clickRight="submit"
    ></page-head>
    <view class="group" v-if="showContent">
      <view class="row dis-flex">
        <view class="row-lable">
          <text class="required-red oa-icon oa-icon-asterisks"></text>
          <text class="row-lable-text">组名称</text>
        </view>
        <view class="row-value">
          <input
            class="row-value-input-text"
            type="text"
            placeholder="请输入群组名称"
            v-model="groupData.groupName"
          />
        </view>
      </view>
      <view class="row dis-flex">
        <view class="row-lable">
          <text class="required-red oa-icon oa-icon-asterisks"></text>
          <text class="row-lable-text">组分类</text>
        </view>
        <view
          class="row-value row-value-input"
          data-ref="popup"
          data-popup-type="bottom"
          data-popup-choice="radio"
          data-tap="groupClass"
          @tap="showPopup"
        >
          <input
            class="row-value-input-text"
            :disabled="true"
            type="text"
            placeholder="请选择群分类"
            v-model="groupData.groupClassName"
          />
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
      <view class="row">
        <view class="row-lable" style="width: 100%;">
          <text class="required-red oa-icon oa-icon-asterisks"></text>
          <text class="row-lable-text">组用户</text>
          <text
            class="add-icon oa-icon oa-icon-tianjiachaosong"
            data-tap="groupUserList"
            @tap="choosePerson"
          ></text>
        </view>
        <view class="row-value row-value-personal">
          <text
            class="personNameStr"
            data-tap="groupUserList"
            @tap="choosePerson"
            >{{ groupUserList | personFilter }}</text
          >
          <uni-icons
            v-if="groupUserList.length > 0"
            :size="40"
            class="uni-icon-wrapper"
            color="#bbb"
            type="closeempty"
            @tap="emptyPerson('groupUserList')"
          />
        </view>
      </view>
      <view class="row">
        <view class="row-lable" style="width: 100%;">
          <text class="required-red oa-icon oa-icon-asterisks"></text>
          <text class="row-lable-text">使用范围</text>
          <text
            class="add-icon oa-icon oa-icon-tianjiachaosong"
            data-tap="rangeList"
            @tap="choosePerson"
          ></text>
        </view>
        <view class="row-value row-value-personal">
          <text
            class="personNameStr"
            data-tap="rangeList"
            @tap="choosePerson"
            >{{ rangeList | personFilter }}</text
          >
          <uni-icons
            v-if="rangeList.length > 0"
            :size="40"
            class="uni-icon-wrapper"
            color="#bbb"
            type="closeempty"
            @tap="emptyPerson('rangeList')"
          />
        </view>
      </view>
      <button
        v-if="type == 'edit'"
        type="default"
        class="delete-group"
        @click="submitDelete"
      >
        删除
      </button>
    </view>
    <uni-popup
      :type="picker.popupType"
      :popup-choice="picker.popupChoice"
      top-height="45%"
      ref="popup"
      @maskclick="maskConfirm"
    >
      <view class="contact-list scroll_list">
        <view
          class="contact-item"
          v-for="item in groupClassList"
          :key="item.id"
          :data-column-value="item.id"
          :data-column-text="item.className"
          :data-tap="picker.tap"
          data-ref="popup"
          @tap="singleColumn"
        >
          <text class="contact-item-text">{{ item.className }}</text>
          <view class="contact-item-icon">
            <uni-icons
              v-if="groupData.groupClassId === item.id"
              type="checkmarkempty"
              color="#005BAC"
              size="44"
            />
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState } from 'vuex';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import graceChecker from '@/common/js/graceChecker.js';
export default {
  components: {
    uniPopup
  },
  data() {
    return {
      fromPage: '',
      type: '',
      title: '新增群组',
      showContent: false,
      picker: {},
      groupData: {
        groupClassId: '',
        groupClassName: '',
        groupName: '',
        groupId: '',
        groupOrder: '',
        groupType: 1,
        groupUserNames: '',
        groupUserString: '',
        rangeEmp: '',
        rangeName: '',
        rangeOrg: ''
      },
      groupClassList: [],
      groupUserList: [],
      rangeList: []
    };
  },
  computed: {
    ...mapState([
      'username',
      'empcode',
      'empsex',
      'headimg',
      'deptname',
      'dutyname'
    ])
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.type = opt.type;
    this.getGroupClass();
    if (this.type == 'edit') {
      let data = uni.getStorageSync('group');
      this.title = '群组编辑';
      this.$set(this.groupData, 'groupClassId', data.groupClassId);
      this.$set(this.groupData, 'groupClassName', data.groupClassName);
      this.$set(this.groupData, 'groupName', data.groupName);
      this.$set(this.groupData, 'groupId', data.groupId);
      this.$set(this.groupData, 'groupOrder', data.groupOrder);
      this.$set(this.groupData, 'groupUserNames', data.groupUserNames);
      this.$set(this.groupData, 'groupUserString', data.groupUserString);
      this.$set(this.groupData, 'rangeEmp', this.formatRangeStr(data.rangeEmp));
      this.$set(
        this.groupData,
        'rangeName',
        this.formatRangeStr(data.rangeName)
      );
      this.$set(this.groupData, 'rangeOrg', this.formatRangeStr(data.rangeOrg));
      this.groupUserList = this.formatArrData(data.employeeList);
      this.rangeList = this.formatArrData(data.rangeEmployeeList);
    }
  },
  created() {
    if (this.type == 'add') {
      this.setDefaultPerson();
    }
  },
  methods: {
    formatRangeStr(data) {
      let dataStr = '';
      if (data) {
        let reg = new RegExp('\\|', 'g'); //g,表示全部替换。
        dataStr = data.replace(reg, '');
      }
      return dataStr;
    },
    formatArrData(data) {
      let dataArr = [];
      data.forEach(item => {
        dataArr.push({
          choose: true,
          empDeptName: item.empDeptName,
          empDutyName: item.empDutyName,
          empFirstName: item.empName.substring(item.empName.length - 2),
          empHeadImg: item.empHeadImg,
          empSex: item.empSex,
          id: item.empCode,
          name: item.empName
        });
      });
      return dataArr;
    },
    //获取群组类别
    getGroupClass() {
      this.ajax
        .getOrgGroupClassList({
          pageSize: 200,
          pageNo: 1,
          classType: 1
        })
        .then(res => {
          this.groupClassList = res.rows;
          this.showContent = true;
        });
    },
    //设置默认范围
    setDefaultPerson() {
      this.rangeList = [
        {
          choose: true,
          empDeptName: this.deptname,
          empDutyName: this.dutyname,
          empFirstName: this.username.substring(this.username.length - 2),
          empHeadImg: this.headimg,
          empSex: this.empsex,
          id: this.empcode,
          name: this.username
        }
      ];
      this.$set(this.groupData, 'rangeName', this.username);
      this.$set(this.groupData, 'rangeEmp', this.empcode);
    },
    //显示弹出层
    showPopup(e) {
      let data = e.currentTarget.dataset;
      this.$set(this.picker, 'popupType', data.popupType);
      this.$set(this.picker, 'popupChoice', data.popupChoice);
      this.$set(this.picker, 'tap', data.tap);
      this.$nextTick(() => {
        this.$refs[data.ref].open();
      });
    },
    //单选
    singleColumn(e) {
      let data = e.currentTarget.dataset;
      this.$set(this.groupData, 'groupClassId', data.columnValue);
      this.$set(this.groupData, 'groupClassName', data.columnText);
      this.$nextTick(() => {
        this.$refs[data.ref].close();
      });
    },
    //弹出遮罩层点击事件
    maskConfirm() {},
    //选择人员
    choosePerson(e) {
      let data = e.currentTarget.dataset,
        personArr = this[data.tap] || [];
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', function(res) {
        this.$set(this, data.tap, res);
        let arrName = [],
          arrId = [];
        res.forEach(item => {
          arrName.push(item.name);
          arrId.push(item.id);
        });
        if (data.tap === 'groupUserList') {
          this.$set(this.groupData, 'groupUserNames', arrName.join(','));
          this.$set(this.groupData, 'groupUserString', arrId.join(','));
        } else {
          this.$set(this.groupData, 'rangeName', arrName.join(','));
          this.$set(this.groupData, 'rangeEmp', arrId.join(','));
        }
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      uni.setStorageSync('person_list', JSON.stringify(personArr));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    //清空人员
    emptyPerson(tap) {
      this.$set(this, tap, []);
      if (tap === 'groupUserList') {
        this.groupData['groupUserNames'] = '';
        this.groupData['groupUserString'] = '';
      } else {
        this.groupData['rangeName'] = '';
        this.groupData['rangeEmp'] = '';
      }
    },
    submit() {
      let rule = [
          {
            filedKey: 'groupName',
            required: true,
            checkType: 'notnull',
            errorMsg: '请输入名称'
          },
          {
            filedKey: 'groupClassName',
            required: true,
            checkType: 'notnull',
            errorMsg: '请选择群分类'
          },
          {
            filedKey: 'groupUserNames',
            required: true,
            checkType: 'notnull',
            errorMsg: '请输入选择组用户'
          },
          {
            filedKey: 'rangeName',
            required: true,
            checkType: 'notnull',
            errorMsg: '请输入选择使用范围'
          }
        ],
        checkRes = graceChecker.check(this.groupData, rule);
      if (checkRes) {
        this.submitGroup();
      } else {
        uni.showToast({ title: graceChecker.error, icon: 'none' });
      }
    },
    //新增群组
    submitGroup() {
      this.ajax
        .confirmOrgGroup(
          this.type == 'edit' ? 'update' : 'save',
          this.groupData
        )
        .then(res => {
          if (this.type == 'edit') {
            uni.removeStorageSync('group');
          }
          this.$nextTick(() => {
            uni.redirectTo({
              url: '/pages/addressBook/groups'
            });
          });
        });
    },
    //删除群组
    submitDelete() {
      uni.showModal({
        title: '提示',
        content: '您确定删除该群组?',
        confirmText: '取消',
        cancelText: '确定',
        confirmColor: '#005BAC',
        success: res => {
          if (res.cancel) {
            this.deleteGroup();
          }
        }
      });
    },
    deleteGroup() {
      this.ajax
        .confirmOrgGroup('deletedById', {
          groupId: this.groupData['groupId']
        })
        .then(res => {
          if (this.type == 'edit') {
            uni.removeStorageSync('group');
            uni.showToast({
              title: '操作成功',
              icon: 'none'
            });
          }
          this.$nextTick(function() {
            uni.redirectTo({
              url: '/pages/addressBook/groups'
            });
          });
        });
    },
    //返回
    returnBack() {
      if (this.type == 'edit') {
        uni.removeStorageSync('group');
      }
      this.$nextTick(function() {
        uni.redirectTo({
          url: '/pages/addressBook/groups'
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.dis-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.row {
  position: relative;
  background-color: #ffffff;
  &::after {
    position: absolute;
    content: '';
    bottom: 0;
    left: 30rpx;
    right: 0;
    transform: scaleY(-0.5);
    height: 1px;
    background-color: #eee;
  }
  .row-title {
    color: #333;
    margin: 30rpx 30rpx 20rpx;
  }
  .row-lable {
    width: 240rpx;
    padding: 22rpx 30rpx;
    box-sizing: border-box;
    position: relative;
    color: #333;
    .required-red {
      color: #f00;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 4rpx;
      font-size: 24rpx;
    }
    .add-icon {
      font-size: 40rpx;
      padding: 0 30rpx;
      line-height: 1;
      color: #005bac;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }
    .file-add-icon {
      padding: 0 30rpx;
      font-size: 56rpx;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      color: #bbb;
    }
    & ~ .row-value {
      flex: 1;
      font-size: 32rpx;
      color: #666;
      padding: 22rpx 30rpx;
      padding-left: 0;
      box-sizing: border-box;
      text-align: right;
    }
    & ~ .row-value-input {
      display: flex;
      justify-content: center;
      align-items: center;
      .row-value-input-text {
        text-align: right;
        flex: 1;
        font-size: 32rpx;
      }
    }
    & ~ .row-value-textarea {
      width: 100%;
      padding-left: 30rpx;
      padding-top: 0;
      text-align: left;
      .row-value-textarea-text {
        width: 100%;
        min-height: 160rpx;
        font-size: 32rpx;
      }
      .textarea-placeholder {
        color: #bbb;
      }
    }
    & ~ .row-value-personal {
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      text-align: left;
      padding: 0 30rpx 22rpx;
      .personNameStr {
        flex: 1;
      }
    }
  }
  .file-list {
    display: -webkit-box;
    overflow-x: scroll;
    padding: 0 !important;
    .file-item {
      width: 160rpx;
      height: 160rpx;
      margin: 20rpx;
      border-radius: 10rpx;
      position: relative;
      .file-img {
        border-radius: 10rpx;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .file-delet {
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(50%, -50%);
        background-color: #fff;
        color: #bbb;
        border-radius: 100%;
      }
    }
  }
}
.delete-group {
  margin-top: 60rpx;
  background-color: #ffffff;
  font-size: 32rpx;
  color: #333333;
  &::after {
    border: 0;
    border-radius: 0;
  }
}
.scroll_list {
  max-height: 800rpx;
  overflow: auto;
}
.contact-list {
  background-color: #ffffff;
  width: 100%;
  height: 100%;
  overflow: auto;
  .contact-item {
    padding: 22rpx 30rpx;
    font-size: 28rpx;
    color: #333333;
    position: relative;
    display: flex;
    align-items: center;
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 30rpx;
      bottom: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &:last-child::after {
      height: 0;
    }
    .contact-item-text {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
    }
    .contact-item-icon {
      line-height: 1;
    }
  }
}
</style>
