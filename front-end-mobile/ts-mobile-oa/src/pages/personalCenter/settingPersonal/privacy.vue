<template>
  <view class="ts-content">
    <page-head title="隐私设置" @clickLeft="returnBack"></page-head>
    <view v-if="showContent">
      <view class="uni-list" v-for="(item, index) in personalList" :key="index">
        <view
          class="uni-list-cell"
          v-for="(subItem, subindex) in item"
          :key="subItem.fieldname"
        >
          <view class="uni-list-cell-navigate">
            <view class="item_icon" v-if="subItem.hasIcon">
              <text
                :style="{ color: subItem.color }"
                class="uni-icons oa-icon"
                :class="subItem.icon"
              ></text>
            </view>
            <view class="item_title">
              <text>{{ subItem.text }}</text>
            </view>
            <view class="item_extra">
              <view
                class="item_extra_icon"
                v-if="subItem.rightIcon"
                :style="{ 'background-color': subItem.rightIconBgcolor }"
              >
                <text
                  class="uni-icons oa-icon"
                  :class="subItem.rightIcon"
                  :style="{ color: subItem.rightIconColor }"
                ></text>
              </view>
              <text class="item_extra_text" v-if="subItem.rightText">{{
                subItem.rightText
              }}</text>
              <switch
                class="item_extra_switch"
                v-if="subItem.showSwitch"
                :checked="subItem.switchChecked"
                @change="onSwitchChange(index, subindex, $event)"
              />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState } from 'vuex';
export default {
  data() {
    return {
      showContent: false,
      personalList: [],
      empDeptId: ''
    };
  },
  computed: {
    ...mapState(['empcode'])
  },
  onLoad() {
    this.ajax
      .getPersonalInformationSettings({
        userCode: this.empcode
      })
      .then(res => {
        this.showContent = true;
        let data = res.object;
        this.empDeptId = data.empDeptId;
        this.personalList = [
          [
            // {
            // 	text: '开启生日显示保护 ',
            // 	rightIcon: 'oa-icon-yinsishezhi',
            // 	rightIconColor: '#ffffff',
            // 	rightIconBgcolor: '#10b47f',
            // 	rightText: '隐藏生日',
            // 	showSwitch: true,
            // 	fieldname: 'isBirthdayProtect',
            // 	switchChecked:data.isBirthdayProtect == 0 ? false : true,
            // },
            {
              text: '开启短信提醒',
              showSwitch: true,
              fieldname: 'isSmsReminder',
              switchChecked: data.isSmsReminder == 0 ? false : true
            },
            {
              text: '开启微信消息提醒',
              showSwitch: true,
              fieldname: 'isWxReminder',
              switchChecked: data.isWxReminder == 0 ? false : true
            },
            {
              text: '开启手机号码保护',
              rightIcon: 'oa-icon-yinsishezhi',
              rightIconColor: '#ffffff',
              rightIconBgcolor: '#10b47f',
              rightText: '隐藏部分数字',
              showSwitch: true,
              fieldname: 'isDisplayPhoneNo',
              switchChecked: data.isDisplayPhoneNo == 0 ? false : true
            },
            {
              text: '使用电子签章',
              showSwitch: true,
              fieldname: 'isUseSignature',
              switchChecked: data.isUseSignature == 0 ? false : true
            }
            // ,{
            // 	text: '开启语音提醒',
            // 	showSwitch: true,
            // 	fieldname: 'isVoiceReminder',
            // 	switchChecked:data.isVoiceReminder == 0 ? false : true,
            // }
          ]
        ];
      });
  },
  methods: {
    onSwitchChange(index1, index2, $event) {
      let elem = this.personalList[index1][index2];
      let fieldname = elem.fieldname;
      let datas = {};
      (datas.empCode = this.empcode),
        (datas.id = this.empcode),
        (datas.empDeptId = this.empDeptId);
      datas[fieldname] = $event.detail.value ? 1 : 0;
      this.ajax.editEmployee(datas).then(res => {
        elem.switchChecked = $event.detail.value;
      });
    },
    returnBack() {
      uni.redirectTo({
        url: '/pages/personalCenter/settingPersonal/personal'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .uni-list {
    margin-bottom: 10px;
    .uni-list-cell-navigate {
      padding: 14rpx 30rpx;
      flex: 1;
      .item_icon {
        margin-right: 18rpx;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        line-height: 1;
        .oa-icon {
          font-size: 40rpx;
        }
      }
      .item_title {
        display: flex;
        flex: 1;
        flex-direction: column;
        overflow: hidden;
        font-size: 30rpx;
        color: #333333;
      }
      .item_extra {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        .item_extra_icon {
          border-radius: 100%;
          overflow: hidden;
          margin-right: 2px;
          .oa-icon {
            font-size: 24rpx;
          }
          height: 40rpx;
          width: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .item_extra_text {
          color: #666666;
          font-size: 24rpx;
        }
        .item_extra_switch {
          margin-left: 10rpx;
        }
      }
    }
    &::after {
      height: 0;
    }
  }
}
</style>
