import { chooseImage } from '@/common/js/uploadImg.js';
export default {
  data() {
    return {
      picker: {} // popup 弹窗配置
    };
  },
  methods: {
    /**@desc 处理唤醒弹窗操作 */
    handleWakeUpChooseBox(value = '', setting, optionsList, form, formData) {
      let { fieldType, fieldName, optionValue = '' } = setting || {},
        picker = {};
      switch (fieldType) {
        case 'radio':
        case 'select':
        case 'jobType_1':
        case 'jobType_2':
        case 'jobType_3':
          picker = {
            ref: 'popup',
            optionsList,
            popupType: 'bottom',
            popupChoice: 'radio',
            value,
            fieldName,
            setting,
            form,
            formData
          };
          break;
        case 'checkbox':
          if (value instanceof String) {
            value = value.split(',').filter(item => item);
          }
          picker = {
            ref: 'popup',
            optionsList,
            popupType: 'bottom',
            popupChoice: 'checkbox',
            value,
            fieldName,
            setting,
            form,
            formData
          };
          break;
        case 'date':
          let dateFormat = setting.dataFormat || 'yyyy-MM-dd';
          picker = {
            ref: 'datePicker',
            fields: {
              'yyyy-MM': 'month',
              'yyyy-MM-dd': 'day',
              'yyyy-MM-dd HH:mm': 'minute',
              'yyyy-MM-dd HH:mm:ss': 'second'
            }[dateFormat],
            mode: 'date',
            value,
            fieldName,
            setting,
            form,
            formData
          };
          break;
        case 'address':
          var data = this.cityDatas;
          let regionDefault = [];

          if (value) {
            regionDefault = value
              .split('-')
              .filter(t => t)
              .map(key => {
                let index = data.findIndex(item => item.value == key);
                if (index >= 0) {
                  data = data[index].children || [];
                }
                return index;
              });
          }
          picker = {
            regionDefault,
            value,
            fieldName,
            setting,
            form,
            formData
          };
          this.$nextTick(() => {
            this.regionSelectShow = true;
          });
          break;
        case 'deptChose':
          picker = {
            checkType: 'radio',
            value,
            fieldName,
            setting,
            form,
            formData
          };
          this.handleChooseDept(picker);
          break;
      }
      this.picker = picker;
      picker.ref &&
        (this.$refs[picker.ref].open
          ? this.$refs[picker.ref].open()
          : this.$refs[picker.ref].show());
    },
    handleOptionSelect(item) {
      let value = item.value,
        fieldName = this.picker.fieldName,
        setting = this.picker.setting,
        form = this.picker.form,
        formData = this.picker.formData;
      this.$set(formData, fieldName, value);

      switch (this.picker.popupChoice) {
        case 'radio':
          if (setting && setting.fieldType == 'select') {
            fieldName == 'plgw' &&
              form.hasOwnProperty('gwdj') &&
              form.gwdj.getOptionsData();
            fieldName == 'salary_level_type' &&
              form.hasOwnProperty('salary_level_id') &&
              form.salary_level_id.getOptionsData();

            /**@desc 政治面貌 为 中共党员时 入党时间必填 */
            if (
              fieldName == 'political_status' &&
              form.hasOwnProperty('party_date')
            ) {
              if (value == 1) {
                form.party_date.isMust = 1;
              } else {
                this.$set(formData, 'party_date', '');
                form.party_date.isMust = form.party_date.setting.isMust;
              }
            }
          } else if (setting && setting.fieldType.includes('jobType')) {
            if (value) {
              let nextTypeName = '';
              if (setting.fieldType == 'jobType_1') {
                nextTypeName = 'jobType_2';
                let thirdKeyName = Object.keys(form).find(key => {
                  return form[key].setting.fieldType == 'jobType_3';
                });
                form[thirdKeyName].optionsList = [];
                formData[thirdKeyName] = null;
              } else if (setting.fieldType == 'jobType_2') {
                nextTypeName = 'jobType_3';
              }

              if (nextTypeName) {
                let optionsList = item.children || [],
                  nextKeyName = Object.keys(form).find(key => {
                    return form[key].setting.fieldType == nextTypeName;
                  });
                if (nextKeyName) {
                  this.$set(formData, nextKeyName, null);
                  this.$set(form[nextKeyName], 'optionsList', optionsList);
                }
              }
            } else {
              // 按理来说应该清除 jobType_2 还有 jobType_3 的数据的，但是貌似没必要，毕竟没办法取消选择
            }
          }
          this.$refs[this.picker.ref].close();
          this.$forceUpdate();
          break;
        case 'checkbox':
          var valueIndex = this.picker.value.findIndex(val => val == value);
          if (valueIndex >= 0) {
            this.picker.value.splice(valueIndex, 1);
          } else {
            this.picker.value.push(value);
          }
          break;
      }
    },
    /**@desc 弹窗关闭事件 */
    maskConfirm(type) {
      if (type == 'checkbox') {
        this.$set(this.picker.formData, fieldName, this.picker.value.join(','));
      }
    },
    /**@desc 时间选择器确认 */
    handleDatePickerConfirm({ value }) {
      let formData = this.picker.formData,
        fieldName = this.picker.fieldName;
      this.$set(formData, fieldName, value);
      // 特殊逻辑计算
      switch (fieldName) {
        case 'entry_date':
          // 根据入院时间 计算 本单位连续工龄
          var today = this.$dayjs().format('YYYY-MM-DD'),
            diffYear = this.$dayjs(today).diff(value, 'year');
          this.$set(this.picker.formData, 'year_work', diffYear);
          break;
        case 'work_start_date':
          // 根据参加工作时间 计算 连续工龄
          var today = this.$dayjs().format('YYYY-MM-DD'),
            diffYear = this.$dayjs(today).diff(value, 'year');
          this.$set(this.picker.formData, 'bdwlxgl', diffYear);
          break;
        case 'positive_time':
          this.$dayjs(value).isAfter(this.$dayjs()) &&
            this.$set(this.picker.formData, 'employee_status', '99');
          break;
        default:
          break;
      }
    },
    /**@desc 地区选择确认 */
    regionSelectConfirm(valueList = []) {
      this.$set(
        this.picker.formData,
        this.picker.fieldName,
        valueList.map(item => item.value).join('-')
      );
      this.regionSelectShow = false;
    },
    /**
     * @desc 科室选择
     * @param {object} picker 科室选择参数
     * @param {field}  picker.setting
     */
    handleChooseDept(picker) {
      let valueList = this.lineDeptData
        .filter(item => item.organizationId == picker.value)
        .map(item => ({
          name: item.name,
          id: item.organizationId
        }));

      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('deptlist', function(res = []) {
        let dataList = res.map(item => item.id).join(',');
        this.$set(picker.formData, picker.fieldName, dataList);
        uni.removeStorageSync('dept_list');
        //清除监听，不清除会消耗资源
        uni.$off('deptlist');
      });
      uni.setStorageSync('dept_list', JSON.stringify(valueList));
      uni.navigateTo({
        url:
          '/pages/selectDept/select-dept?checkType=' + picker.checkType ||
          'radio'
      });
    },
    /**
     * @desc 上传图片
     * @param {object} setting -当前表单项配置
     * @param {string} setting.fieldName -表单项取值名称
     *
     * @param {object} formData -表单绑定对象
     * @param {object} form -表单项对象集合（可以拿到所有相关的表单项）
     */
    handleChooseImg(setting, formData) {
      chooseImage({
        limitNum: 1, //数量
        uploadFileUrl: `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/upload?module=hrm`, //服务器地址
        fileKeyName: 'file', //参数
        showLoading: true,
        hideLoading: false,
        loadingTitle: '上传中...',
        success: res => {
          let resVal = JSON.parse(res);
          uni.showToast({
            title: '上传成功',
            icon: 'none'
          });
          this.$set(formData, setting.fieldName, resVal.object[0].filePath);
        }
      });
    }
  }
};
