<template>
  <view
    class="form-item"
    :class="{
      disabled: disabled,
      'vertical-item': ['textarea', 'file'].includes(setting.fieldType),
      'remake-item': setting.remark
    }"
  >
    <view
      :class="{
        'form-item-title': true,
        'pass-text': passFields.includes(setting.fieldName),
        'out-text': outFields.includes(setting.fieldName),
        'staging-text': Object.keys(changeField).includes(setting.fieldName)
      }"
    >
      <text v-if="isMust == 1">*</text>
      {{ setting.showName }}
    </view>
    <view class="form-item-edit-content">
      <template
        v-if="['input', 'number', 'serialNumber'].includes(setting.fieldType)"
      >
        <input
          :value="value"
          :disabled="disabled"
          :placeholder="
            setting.placeholderContent
              ? setting.placeholderContent
              : '请输入' + setting.showName
          "
          maxlength="-1"
          @input="handleInputDomInput"
          @blur="handleInputBlur()"
        />
      </template>
      <template
        v-else-if="
          [
            'radio',
            'checkbox',
            'date',
            'select',
            'jobType_1',
            'jobType_2',
            'jobType_3',
            'address',
            'deptChose'
          ].includes(setting.fieldType)
        "
      >
        <view class="flex-col-center" @click="handleWakeUpChooseBox">
          <input
            :value="insideVal"
            :disabled="true"
            :placeholder="
              setting.placeholderContent
                ? setting.placeholderContent
                : '请选择' + setting.showName
            "
            maxlength="-1"
            style="point-event: none;"
          />
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </template>
      <template v-else-if="setting.fieldType == 'img'">
        <view class="img-content" @click="handleChooseImg">
          <image
            v-cloak
            :src="
              value
                ? $config.BASE_HOST + value
                : require('@/static/img/headImg.png')
            "
            mode=""
          ></image>
        </view>
      </template>
      <template v-else-if="setting.fieldType == 'textarea'">
        <textarea
          :value="value"
          maxlength="-1"
          :disabled="disabled"
          :placeholder="
            setting.placeholderContent
              ? setting.placeholderContent
              : '请输入' + setting.showName
          "
          @input="handleInputDomInput"
        />
      </template>
      <template v-else-if="setting.fieldType == 'file'">
        <view
          class="file_add_icon oa-icon oa-icon-tupiantianjia"
          @click="handleUploadFile"
        ></view>
        <view class="file_list">
          <view class="file_item" v-for="t in fileList" :key="t.fileId">
            <view
              class="file_item_info"
              @click="previewFile(t.fileId, t.fileRealName, t, fileList)"
            >
              <view
                class="oa-icon"
                :class="'oa-icon-' + $oaModule.formatFileType(t.fileType)"
              ></view>
              <view class="file_item_name">
                <text class="file_name">{{ t.fileName }}</text>
                <text class="file_size">{{ t.fileSize | fileSizeFilter }}</text>
              </view>
            </view>
            <text
              class="oa-icon oa-icon-xiazai delete_file"
              @click.stop="downloadFile(t.fileId, t.fileRealName)"
            ></text>
            <text
              v-if="!disabled"
              class="oa-icon oa-icon-guanbi delete_file"
              @click.stop="deletFile(t.fileId)"
            ></text>
          </view>
        </view>
      </template>
      <template v-else>
        <input
          :value="value"
          :disabled="disabled"
          :placeholder="
            setting.placeholderContent
              ? setting.placeholderContent
              : '请输入' + setting.showName
          "
          maxlength="-1"
          @input="handleInputDomInput"
          @blur="handleInputBlur()"
        />
      </template>
    </view>
  </view>
</template>

<script>
import Base64 from '@/common/js/base64.min.js';
import { chooseImage } from '@/common/js/uploadImg.js';
import { cityDatas } from '@/common/js/cityData.js';

export default {
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {},
    setting: {
      type: Object,
      default: () => ({})
    },
    changeField: {
      type: Object,
      default: () => ({})
    },
    form: Object,
    formData: Object,
    jobData: {
      type: Array,
      default: () => []
    },
    passFields: {
      type: Array,
      default: () => []
    },
    outFields: {
      type: Array,
      default: () => []
    },
    lineDeptData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isMust: 0,
      optionsList: [],
      fileList: []
    };
  },
  methods: {
    validate() {
      if (this.disabled) {
        return true;
      }
      let validate = true;
      // 是否为空
      if (this.isMust == 1) {
        validate =
          validate &&
          this.value != null &&
          this.value != undefined &&
          this.value != '';
        !validate &&
          uni.showToast({
            icon: 'none',
            title: `${this.setting.showName || ''}必填数据不能为空`
          });
      }

      // 校验配置项中的特别校验规则
      if (this.setting.decimalDigit) {
        validate = validate && this.handleValidateSetting();
      }

      return validate;
    },
    /**@desc 获取选项数据 */
    getOptionsData() {
      if (this.setting.fieldType == 'select') {
        if (
          ['concurrent_position', 'position_id'].includes(
            this.setting.fieldName
          )
        ) {
          this.ajax
            .getUserInfoEditPartTimePositionData({
              pageNo: 1,
              pageSize: 50
            })
            .then(res => {
              if (res.success == false) {
                return;
              }
              this.optionsList = res.rows.map(item => ({
                label: item.positionName,
                value: item.positionId
              }));
            });
        }
        if (this.setting.fieldName == 'gwdj') {
          this.ajax
            .getUserInfoEditGetOptionsData({
              postCategory: this.formData.plgw || ''
            })
            .then(res => {
              if (res.object) {
                this.optionsList = res.object.map(item => ({
                  label: item.postName,
                  value: item.postId
                }));
              }
            });
        }
        if (this.setting.fieldName == 'salary_level_type') {
          this.ajax.getUserInfoEditSalaryLevelTypeData().then(res => {
            if (res.object) {
              this.optionsList = res.object.map(item => ({
                label: item.dictName,
                value: item.dictValue
              }));
            }
          });
        }
        if (this.setting.fieldName == 'salary_level_id') {
          this.ajax
            .getUserInfoEditSalaryLevelData({
              salaryLevelCategory: this.formData.salary_level_type || ''
            })
            .then(res => {
              if (res.object) {
                this.optionsList = res.object.map(item => ({
                  label: item.salaryLevelName,
                  value: item.salaryLevelId
                }));
              }
            });
        }

        if (this.setting.dataSource == 1) {
          this.optionsList = (this.setting.optionValue || '')
            .split('|')
            .filter(item => item)
            .map(item => {
              let list = item.split(':');
              return {
                value: list[0],
                label: list[1]
              };
            });
        } else if (this.setting.dataSource == 4) {
          this.ajax.getDataByDataLibrary(this.setting.dictSource).then(res => {
            if (res.object) {
              this.optionsList = res.object.map(item => ({
                label: item.itemName,
                value: item.itemNameValue
              }));
            }
          });
        }
      } else if (this.setting.fieldType == 'jobType_1') {
        this.optionsList = this.jobData.map(item => item);
      } else if (this.setting.fieldType == 'jobType_2') {
        let parentKey = Object.keys(this.form).find(key => {
          return (this.form[key].setting || {}).fieldType == 'jobType_1';
        });
        this.optionsList =
          (
            this.getJobTypeOptions(
              this.jobData,
              this.formData[parentKey] || ''
            ) || {}
          ).children || [];
      } else if (this.setting.fieldType == 'jobType_3') {
        let parentKey = Object.keys(this.form).find(key => {
          return (this.form[key].setting || {}).fieldType == 'jobType_2';
        });
        this.optionsList =
          (
            this.getJobTypeOptions(
              this.jobData,
              this.formData[parentKey] || ''
            ) || {}
          ).children || [];
      }
    },
    /**@desc 获取工作数据 */
    getJobTypeOptions(list, value) {
      for (var i = 0; i < list.length; i++) {
        if (list[i].value == value) {
          return list[i];
        }
        if (list[i].children && list[i].children.length) {
          var d = this.getJobTypeOptions(list[i].children, value);
          if (d) {
            return d;
          }
        }
      }
    },
    /**@desc 输入文字 */
    handleInputDomInput(event) {
      let { value = '' } = event.target || {};
      this.$emit('input', value);
      if (this.setting.fieldType == 'number') {
        let matchList = value.match(/^(([1-9]{1}\d*)|(0{1}))(\.\d+|\.{1})?/);
        value = matchList ? matchList[0] : null;
      }
      this.$nextTick(() => {
        this.$emit('input', value);
      });
    },
    handleInputBlur() {
      this.setting.decimalDigit && this.handleValidateSetting();
      // 自动通过身份证号获取年龄等信息
      if (this.setting.fieldName == 'identity_number') {
        if (!this.$common.checkIDCard(this.value)) {
          uni.showToast({
            icon: 'none',
            title: '身份证格式错误'
          });
          return;
        }
        let birthDay = this.$common.getBirthdayFromIdCard(this.value),
          age = this.$common.getAgeFromIdCard(this.value);
        this.$set(this.formData, 'birthday', birthDay);
        this.$set(this.formData, 'emp_age', age);
      }
    },
    /**@desc 校验特定规则 */
    handleValidateSetting() {
      let res = true;
      switch (this.setting.decimalDigit) {
        case 'idCard':
          res = this.$common.checkIDCard(this.value);
          if (!res) {
            uni.showToast({
              icon: 'none',
              title: '身份证格式错误'
            });
          }
          break;
        case 'isPhone':
          res = /^1[3-9]\d{9}$/.test(this.value);
          if (!res) {
            uni.showToast({
              icon: 'none',
              title: '请输入正确的手机号'
            });
          }
          break;
        case 'email':
          res = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(
            this.value
          );
          if (!res) {
            uni.showToast({
              icon: 'none',
              title: '请输入正确的邮箱'
            });
          }
          break;
      }
      return res;
    },
    /**@desc 唤起弹窗操作 */
    handleWakeUpChooseBox() {
      if (this.disabled) {
        return;
      }
      this.$emit(
        'wake-up-popup',
        this.value,
        this.setting,
        this.optionsList,
        this.form,
        this.formData
      );
    },
    /**@desc 触发图片选择事件 */
    handleChooseImg() {
      // !this.disabled &&
      this.$emit('choose-img', this.setting, this.formData, this.form);
    },
    /**@desc 上传文件 */
    handleUploadFile() {
      if (this.disabled) {
        return;
      }
      let businessId = this.value;
      if (!this.value) {
        businessId = this.$common.guid();
      }
      chooseImage({
        limitNum: 9, //数量
        uploadFileUrl: `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/upload?moduleName=hrms`, //服务器地址
        fileKeyName: 'file', //参数
        showLoading: true,
        hideLoading: true,
        loadingTitle: '上传中...',
        formData: { businessId },
        success: res => {
          let refVal = JSON.parse(res),
            fileList = (refVal.object || []).map(i => {
              let nameList = i.fileName.split('.');
              return {
                fileRealName: i.fileRealName,
                fileName: i.fileName,
                fileSize: i.fileSize,
                fileUrl: i.filePath,
                fileId: i.fileId,
                fileType:
                  nameList.length > 1 ? nameList[nameList.length - 1] : ''
              };
            });
          if (!this.value) {
            this.$emit('input', businessId);
          } else {
            this.fileList = this.fileList.concat(fileList);
          }
        }
      });
    },
    //预览文件
    previewFile(id, fileName, file, fileList = []) {
      // let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-basics-bottom/fileAttachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      let filePath =
        location.origin +
          file.fileUrl +
          '?fullfilename=' +
          file.fileId +
          '.' +
          file.fileType || file.fileName.split('.')[1];
      if (!fileList.length) {
        fileList = [file];
      }
      uni.setStorageSync('fileList', fileList);
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    },
    downloadFile(id, fileName) {
      let filePath = `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      this.$downloadFile.downloadFile(filePath);
    },
    //删除图片
    deletFile(fileId) {
      this.ajax
        .deletFileById({
          fileid: fileId
        })
        .then(res => {
          if (res.success) {
            let index = this.fileList.findIndex(item => item.fileId == fileId);
            if (index >= 0) {
              this.fileList.splice(index, 1);
              !this.fileList.length && this.$emit('input', null);
            }
          }
        });
    },
    /**@desc 获取附件文件 */
    getFileData() {
      this.ajax
        .getFileAttachmentByBusinessId({
          businessId: this.value
        })
        .then(res => {
          if (res.object) {
            this.fileList = res.object.map(item => {
              let { fileName, fileSize, id, fileExtension, realPath } = item;
              return {
                fileRealName: fileName,
                fileName: id + '.' + fileExtension,
                fileSize,
                fileId: id,
                fileType: fileExtension,
                fileUrl: realPath
              };
            });
          }
        });
    }
  },
  computed: {
    disabled() {
      return (
        this.setting.isOnly == 1 || this.setting.fieldName == 'employee_no'
      );
    },
    insideVal() {
      let value = this.value || '';
      if (
        [
          'radio',
          'checkbox',
          'select',
          'jobType_1',
          'jobType_2',
          'jobType_3'
        ].includes(this.setting.fieldType)
      ) {
        let valueList = value.split(',').filter(item => item);
        value = this.optionsList
          .filter(item => valueList.includes(item.value))
          .map(item => item.label)
          .join(',');
      }

      if (this.setting.fieldType == 'address' && value) {
        let valueList = value.split('-'),
          data = cityDatas;
        value = valueList
          .map(key => {
            let cityData = data.find(item => item.value == key) || {};
            if (cityData.children) {
              data = cityData.children;
            }
            return cityData.label || '';
          })
          .filter(item => item)
          .join('-');
      }
      /**@desc 科室数据匹配 */
      if (this.setting.fieldType == 'deptChose' && value) {
        value =
          this.lineDeptData.find(dept => dept.organizationId == value) || {};
        value = value.name || '';
      }
      return value;
    }
  },
  watch: {
    setting: {
      handler(val = {}) {
        this.form && this.$set(this.form, val.fieldName, this);
        this.isMust = val.isMust || 0;
        if (['radio', 'checkbox'].includes(val.fieldType)) {
          this.optionsList = (val.optionValue || '')
            .split('|')
            .filter(item => item)
            .map(item => {
              let list = item.split(':');
              return {
                value: list[0],
                label: list[1]
              };
            });
        }
        val.fieldType && this.getOptionsData();
        val.value && this.$emit('input', val.value);
      },
      deep: true,
      immediate: true
    },
    value: {
      handler(val, oldVal) {
        if (val && this.setting) {
          this.setting.fieldType == 'file' && this.getFileData();
        }
        // 编制类型 为 合同制、劳务派遣 时，合同开始时间和合同结束时间必填
        if (this.setting.fieldName == 'establishment_type' && this.form) {
          this.$nextTick(() => {
            if (['2', '6'].includes(String(val))) {
              this.$set(this.form['start_employ_date'], 'isMust', 1);
              this.$set(this.form['end_employ_date'], 'isMust', 1);
            } else {
              this.$set(
                this.form['start_employ_date'],
                'isMust',
                (this.form['start_employ_date'].setting || {}).isMust || 0
              );
              this.$set(
                this.form['end_employ_date'],
                'isMust',
                (this.form['end_employ_date'].setting || {}).isMust || 0
              );
            }
          });
        }
        // 吉首特殊需求
        if (this.$store.state.globalSetting.orgCode == 'jssrmyy') {
          // 编辑类型为 learn_way 学习方式 时， 如果选中值是 自考（4） 学制（school_system），入学时间（start_time） 非必填
          if (this.setting.fieldName == 'learn_way' && this.form) {
            this.$nextTick(() => {
              this.$set(
                this.form['school_system'],
                'isMust',
                val == 4 ? 0 : (this.form['school_system'].setting || {}).isMust
              );
              this.$set(
                this.form['start_time'],
                'isMust',
                val == 4 ? 0 : (this.form['start_time'].setting || {}).isMust
              );
            });
          }
          // 编辑类型为 hr_htsc 合同时长 时， 若选中值为 '无固定期限' 时，去除合同结束时间的必填；
          // 选中的值为 数值 的时候， 自动计算合同结束时长
          if (this.setting.fieldName == 'hr_htsc' && this.form) {
            this.$nextTick(() => {
              // 修改是否必填
              this.$set(
                this.form['end_employ_date'],
                'isMust',
                val == '无固定期限'
                  ? 0
                  : (this.form['end_employ_date'].setting || {}).isMust
              );
              val == '无固定期限' &&
                this.$set(this.formData, 'end_employ_date', '');
              // 自动计算结束时间
              if (
                val &&
                val != '无固定期限' &&
                this.formData &&
                this.formData['start_employ_date']
              ) {
                let duringYear = Number(val);
                if (isNaN(duringYear)) {
                  console.warn('合同时长基本配置错误');
                  return;
                }
                let newVal = this.$dayjs(this.formData['start_employ_date'])
                  .add(duringYear, 'year')
                  .subtract(1, 'day')
                  .format('YYYY-MM-DD');
                this.$set(this.formData, 'end_employ_date', newVal);
              }
            });
          }
          // 选中合同开始时间，计算合同结束时间
          if (
            this.setting.fieldName == 'start_employ_date' &&
            this.formData['hr_htsc'] &&
            this.formData['hr_htsc'] != '无固定期限' &&
            val
          ) {
            let duringYear = Number(this.formData['hr_htsc']);
            if (isNaN(duringYear)) {
              console.warn('合同时长基本配置错误');
              return;
            }
            let newVal = this.$dayjs(val)
              .add(duringYear, 'year')
              .subtract(1, 'day')
              .format('YYYY-MM-DD');
            this.$set(this.formData, 'end_employ_date', newVal);
          }
        }
      },
      immediate: true
    }
  }
};
</script>

<style lang="scss" scoped>
.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  margin: 0 16px;
  font-size: 14px;
  &.validate {
    border: 1px solid red !important;
  }

  &:not(:last-child) {
    border-bottom: 1px solid #eee;
  }

  &.remake-item {
    border-bottom: 0px solid #eee;
    padding-bottom: 0px;
  }

  &.vertical-item {
    display: block;
    position: relative;
    * {
      text-align: unset !important;
    }
    uni-textarea {
      margin-top: 8px;
      width: 100%;
    }
  }
  .form-item-title {
    color: #333;
    font-size: inherit;

    &.out-text {
      color: #fa0213 !important;
    }

    &.pass-text {
      color: #2782f7 !important;
    }
    text {
      color: red;
      margin-right: 4px;
    }

    &.staging-text {
      color: #c2c207 !important;
    }
  }
  .form-item-edit-content * {
    font-size: 14px;
    text-align: right;
  }
  /deep/ .uni-icon-wrapper .uni-icon {
    font-size: 14px !important;
  }
  &.disabled {
    background-color: #eee;
    .form-item-title {
      color: #999;
    }
  }
}
.flex-col-center {
  display: flex;
  align-items: center;
  .uni-icons {
    line-height: 0;
  }
}
.img-content {
  width: 50px;
  height: 50px;
  box-shadow: 0 0 5px #ccc;
  border-radius: 50px;
  overflow: hidden;
  image {
    width: 50px;
    height: 50px;
  }
}
.file_add_icon {
  position: absolute;
  right: 0;
  top: 8px;
  line-height: 22px;
  font-size: 28px !important;
  color: #bbb;
}
.file_list {
  .file_item {
    text-decoration: none;
    font-size: 14px;
    color: #333333;
    margin: 5px 10px 10px;
    padding: 3px 10px;
    border: 1px solid #eeeeee;
    border-radius: 5px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    .file_item_info {
      text-decoration: none;
      flex: 1;
      text-align: left;
      display: flex;
      align-items: center;
      .file_item_name {
        flex: 1;
        margin: 0 10px;
      }
    }
    .oa-icon {
      font-size: 20px;
    }
    .delete_file {
      color: #005bac;
      margin-left: 8px;
    }
    .file_name {
      font-size: 14px;
      color: #333333;
    }
    .file_size {
      color: #999999;
      font-size: 12px;
      margin-left: 10px;
    }
  }
}
</style>
