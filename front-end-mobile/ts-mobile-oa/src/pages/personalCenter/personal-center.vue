<template>
  <view class="ts-content">
    <view class="content-top">
      <view class="content-user">
        <view class="edit-icon-wrapper">
          <text
            class="oa-icon oa-icon-bianji2 edit-icon"
            @click="editInfo()"
          ></text>
        </view>
        <image class="user-img" :src="userHeadImg"></image>
        <view class="user-info">
          <view class="user-name"
            >{{ username }} {{ empPhone ? empPhone : empPhoneSecond }}</view
          >
          <view class="user-dept">{{ deptname }}</view>
          <view class="user-duty">{{ dutyname }}</view>
        </view>
      </view>
    </view>
    <view class="content-wrap">
      <view class="nav-list">
        <view class="nav-item" v-for="(item, index) in navList" :key="index">
          <view
            class="oa-icon"
            :class="item.icon"
            :style="{ color: item.color }"
            @click="quickAccess(item.url)"
          ></view>
          <text class="item-text">{{ item.text }}</text>
        </view>
      </view>
      <view class="mylist">
        <view class="uni-list" v-for="(item, index) in mylist" :key="index">
          <view
            class="uni-list-cell"
            v-for="subItem in item"
            :key="subItem.url"
          >
            <view
              class="uni-list-cell-navigate uni-navigate-right"
              @click="goDetailPage(subItem.url)"
            >
              <view class="item-icon" v-if="subItem.icon">
                <text class="uni-icons oa-icon" :class="subItem.icon"></text>
              </view>
              <text class="item-title">{{ subItem.text }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState } from 'vuex';
export default {
  data() {
    return {
      navigateFlag: false,
      userHeadImg: '',
      navList: [
        {
          icon: 'oa-icon-gongzitiao',
          color: '#f59a23',
          text: '工资条',
          url: '/pages/payslip/salary-details'
        },
        {
          icon: 'oa-icon-wendangzhushou',
          color: '#005BAC',
          text: '文档',
          url: '/pages/file/file'
        },
        {
          icon: 'oa-icon-richeng',
          color: '#3aad73',
          text: '日程',
          url: '/pages/schedule/schedule-calendar'
        },
        {
          icon: 'oa-icon-youxiang',
          color: '#8080ff',
          text: '邮箱',
          url: '/pages/email/email-list'
        }
      ],
      mylist: [
        [
          {
            text: '常用语',
            icon: 'oa-icon-changyongyu',
            url: '/pages/personalCenter/settingApproval/common-words'
          },
          {
            text: '个人设置',
            icon: 'oa-icon-shezhi',
            url: '/pages/personalCenter/setting/setting'
            // url: 'settingPersonal/personal'
          },
          {
            text: '版本信息',
            icon: 'oa-icon-banben',
            url: '/pages/personalCenter/settingPersonal/version'
          }
        ]
      ]
    };
  },
  computed: {
    ...mapState([
      'username',
      'deptname',
      'dutyname',
      'headimg',
      'empPhone',
      'empPhoneSecond'
    ])
  },
  watch: {
    headimg: {
      handler(newName) {
        if (newName) this.userHeadImg = this.$config.BASE_HOST + newName;
        else this.userHeadImg = '../../static/img/headImg.png';
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    editInfo() {
      uni.navigateTo({
        url: '/pages/personalCenter/edit/edit'
      });
    },
    goDetailPage(path) {
      if (this.navigateFlag) return;
      this.navigateFlag = true;
      uni.navigateTo({
        url: `${path}?fromPage=personalCenter`
      });
      setTimeout(() => {
        this.navigateFlag = false;
      }, 200);
    },
    quickAccess(path) {
      if (this.navigateFlag) return;
      this.navigateFlag = true;
      uni.navigateTo({
        url: `/pages/${path}?fromPage=personalCenter`
      });
      setTimeout(() => {
        this.navigateFlag = false;
      }, 200);
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .content-top {
    background: url(../../static/img/myBg.png) no-repeat;
    background-size: 100% 100%;
    height: 360rpx;
    .edit-icon-wrapper {
      height: 80rpx;
      padding: 0 30rpx;
      text-align: right;
      line-height: 80rpx;
      .edit-icon {
        font-size: 36rpx;
        color: #999;
      }
    }
    .user-img {
      width: 130rpx;
      height: 130rpx;
      border-radius: 100%;
      overflow: hidden;
      margin: 0 20rpx;
      background-color: transparent;
      position: relative;
      float: left;
    }
    .user-info {
      font-size: 32rpx;
      float: left;
      .user-name {
        color: #333;
        font-size: 32rpx;
      }
      .user-dept,
      .user-duty {
        color: #666;
        font-size: 24rpx;
      }
    }
  }
  .content-wrap {
    width: 100%;
    margin: 0 auto;
    position: relative;
    top: -80rpx;
    .nav-list {
      background-color: $uni-bg-color;
      display: flex;
      position: relative;
      margin: 0 30rpx 30rpx;
      box-shadow: 0 6rpx 20rpx 0 rgba(45, 118, 235, 0.08);
      border-radius: 16rpx;
      .nav-item {
        width: 25%;
        text-align: center;
        margin: 10px auto;
        .item-text {
          color: #333;
          font-size: 24rpx;
        }
        .oa-icon {
          border-radius: 100%;
          font-size: 80rpx;
          line-height: 1.2;
        }
      }
      &::after {
        position: absolute;
        z-index: 10;
        right: 0;
        bottom: 0;
        left: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #eee;
      }
    }
    .uni-list {
      margin-bottom: 10px;
      .uni-list-cell-navigate {
        justify-content: start;
      }
      .item-icon {
        margin-right: 12rpx;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        line-height: 1;
        .oa-icon {
          font-size: 42rpx;
          color: #666;
        }
      }
      .item-title {
        color: #333;
        font-size: 32rpx;
      }
      &::before,
      &::after {
        height: 0;
      }
    }
  }
}
</style>
