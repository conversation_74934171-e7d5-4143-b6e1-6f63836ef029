<template>
  <view class="ts-content edit-personal-information-container">
    <page-head :title="title" right-text="" @clickLeft="returnBack"></page-head>

    <head-swiper
      v-model="activeTab"
      :tabs="headTabs"
      @change="handleActiveTabChange"
    />

    <view ref="editContent" class="edit-container">
      <view
        v-for="item of personalEditSetting"
        :key="item.id"
        :ref="item.id"
        class="edit-section-container"
      >
        <view class="section-title">
          <text>
            {{ item.groupName }}
          </text>
        </view>
        <template v-if="item.isDetailed != 1">
          <template v-for="field of item.fields">
            <form-item
              v-if="
                (field.isHide == 0 && field.isAllowDeleted == 0) ||
                  (field.isDisabled == 0 && field.isAllowDeleted == 1)
              "
              :key="field.id"
              v-model="userInfo[item.id][0][field.fieldName]"
              :setting="field"
              :form="form[item.id]"
              :formData="userInfo[item.id][0]"
              :jobData="jobData"
              :lineDeptData="lineDeptData"
              @wake-up-popup="handleWakeUpChooseBox"
              @choose-img="handleChooseImg"
            ></form-item>
          </template>
        </template>
        <template v-else>
          <template v-if="userInfo[item.id] && userInfo[item.id].length">
            <view
              v-for="(data, dIndex) of userInfo[item.id]"
              :key="dIndex"
              class="child-form-item-container"
            >
              <view class="child-form-action-content">
                <view
                  class="oa-icon oa-icon-fanhui-copy"
                  @click.native="handleFoldItem($event)"
                ></view>
              </view>
              <view class="child-form-content">
                <template v-for="field of item.fields">
                  <form-item
                    v-if="
                      (field.isHide == 0 && field.isAllowDeleted == 0) ||
                        (field.isDisabled == 0 && field.isAllowDeleted == 1)
                    "
                    :key="field.id"
                    v-model="data[field.fieldName]"
                    :setting="field"
                    :form="form[item.id][dIndex]"
                    :formData="data"
                    :jobData="jobData"
                    :lineDeptData="lineDeptData"
                    @wake-up-popup="handleWakeUpChooseBox"
                    @choose-img="handleChooseImg"
                  ></form-item>
                </template>
              </view>
            </view>
          </template>
        </template>
      </view>
    </view>

    <uni-popup
      ref="popup"
      :type="picker.popupType"
      :popup-choice="picker.popupChoice"
      @maskclick="maskConfirm"
    >
      <view class="popup-content">
        <template v-if="['checkbox', 'radio'].includes(picker.popupChoice)">
          <view
            v-for="item of picker.optionsList"
            :key="item.value"
            class="option-item checkbox-item"
            @click="handleOptionSelect(item)"
          >
            {{ item.label }}

            <uni-icons
              v-if="picker.value == item.value"
              type="checkmarkempty"
              color="#005BAC"
              size="44"
            />
          </view>
        </template>
      </view>
    </uni-popup>

    <date-picker
      ref="datePicker"
      :mode="picker.mode"
      :fields="picker.fields"
      :value="picker.value"
      @confirm="handleDatePickerConfirm"
    ></date-picker>

    <!-- 地区选择 -->
    <u-select
      v-model="regionSelectShow"
      :list="cityDatas"
      :default-value="picker.regionDefault"
      mode="mutil-column-auto"
      @confirm="regionSelectConfirm"
    ></u-select>

    <input-prompt
      v-if="cancelShow"
      :value="inputVal"
      :type="inputType"
      :pattern="inputPattern"
      :title="titleText"
      :name="fieldName"
      :placeholder="placeholderText"
      @confirm="confirm"
      @cancel="cancel"
    ></input-prompt>
  </view>
</template>

<script>
import common from '@/common/js/common.js';
import inputPrompt from '@/components/input-prompt/input-prompt.vue';
import FormItem from './form-item.vue';
import DatePicker from '@/components/picker/date-picker.vue';
import UniPopup from '../../../components/uni-popup/uni-popup.vue';
//导入vuex的mapState值和mapMutations方法
import { mapState, mapMutations } from 'vuex';
import { cityDatas } from '@/common/js/cityData.js';
import formJs from './form.js';
//#region
/**
 * @typedef userInfoSettingGroupItem
 * @property {string} id 唯一标识
 * @property {string} groupName 分组名称
 * @property {1 | 0 | null} isDetailed 是否是多组数据
 * @property {1 | 0 | null} isEdit 该分组是否可以编辑，该属性将会影响此分组下所有的参数是否可以编辑
 * @property {1 | 0 | null} isAllowDeleted 是否可停用
 * @property {field[]} fields 该分组下参数列表
 */
/**
 * @namespace userInfoEdit
 * @typedef {object} field 详情可以参考接口返回 /ts-basics-bottom/employeeField/getList
 * @property {string} id 唯一标识
 * @property {string} fieldName 字段值名称，即value
 * @property {string} showName 字段名称，即label
 * @property {string | 'input' | 'textarea' | 'number' | 'address' | 'date' | 'radio' | 'checkbox' | 'select' | 'file' | 'img' | 'deptChose' | 'jobType_1' | 'jobType_2' | 'jobType3'} fieldType 字段内容类型
 * @property {1 | 0 | null} isMust 是否必填
 * @property {1 | 0 | null} isOnly 是否只读
 * @property {1 | 0 | null} isHide 是否隐藏
 * @property {1 | 0 | null} isAllowDeleted 是否可停用
 * @property {1 | 0 | null} isDisabled 是否禁用
 * @property {1 | 0 | null} isEdit 是否可编辑
 * @property {string} promptText 提示文字
 * @property {string | number} fieldLength 字段长度
 * @property {string | 'idCard' | 'isPhone' | 'email'} decimalDigit 校验规则
 * @property {string} dataFormat 时间格式
 */
//#endregion
export default {
  mixins: [formJs],
  components: {
    inputPrompt,
    FormItem,
    UniPopup,
    DatePicker
  },
  data() {
    return {
      title: '',
      editStatus: null,

      fromPage: '',
      submiting: false,
      activeTab: '',
      scrollLock: null, // 滚动计时
      /**@type userInfoSettingGroupItem*/
      personalEditSetting: [], // 人员信息编辑设置

      form: {}, // 表单对象
      userInfo: {}, // 用户信息
      jobData: [], // 工作选项数据
      lineDeptData: [], // 平铺的科室数据

      regionSelectShow: false, // 城市选择弹窗是否显示
      cityDatas, // 城市选择数据

      showContent: false,
      cancelShow: false
    };
  },
  computed: {
    employeeId() {
      let commonInfo = this.$store.state.userInfo || {};
      return commonInfo.employeeId;
    },
    headTabs() {
      return this.personalEditSetting.map(item => ({
        label: item.groupName,
        value: item.id
      }));
    }
  },
  async onLoad(opt) {
    this.fromPage = opt?.fromPage || '';

    // 获取修改信息记录
    await this.ajax.employeeReviewStatus(this.employeeId).then(res => {
      if (res.success) {
        if (res.object !== null) {
          let dic = {
            1: '待审核信息',
            2: '审核通过',
            3: '审核不通过'
          };

          this.title = dic[res.object.status];
        }
        if (res.object && res.object.data && res.object.data.length > 0) {
          let info = {};
          res.object.data.forEach(item => {
            if (item.updateType == 1) {
              if (!info[item.groupId]) {
                info[item.groupId] = [{}];
                info[item.groupId][0][item.fieldName] = item.afterData;
              } else {
                info[item.groupId][0][item.fieldName] = item.afterData;
              }
            } else {
              item.afterData = JSON.parse(item.afterData);
              info[item.groupId] = [];

              if (item.afterData instanceof Array) {
                item.afterData.forEach(i => {
                  info[item.groupId].push(i);
                });
              }
            }
          });
          this.userInfo = common.deepClone(info);
          this.showContent = true;
        }
        this.editStatus = common.deepClone(res.object);
      }
    });

    this.getInitEditFormData();
    this.getJobData();
  },
  onReady() {
    this.$nextTick(() => {
      this.$refs.editContent.$el.removeEventListener(
        'scrollend',
        this.handleScrollEnd
      );
      this.$refs.editContent.$el.addEventListener(
        'scrollend',
        this.handleScrollEnd
      );
      this.$refs.editContent.$el.removeEventListener(
        'scroll',
        this.handleEditContent
      );
      this.$refs.editContent.$el.addEventListener(
        'scroll',
        this.handleEditContent
      );
    });
  },
  methods: {
    ...mapMutations(['changeState']),
    /**@desc 获取渲染表格数据 */
    getInitEditFormData() {
      this.ajax
        .getPersonalInformationEditSettings({
          employeeId: this.employeeId
        })
        .then(res => {
          if (!res.success) {
            return;
          }
          this.form = {};

          // 处理信息记录 对应展示的表单项
          if (
            this.editStatus &&
            this.editStatus.data &&
            this.editStatus.data.length > 0
          ) {
            let groupIds = this.editStatus.data.map(item => item.groupId);
            res.object = res.object.filter(item => groupIds.includes(item.id));
          }

          Object.keys(this.userInfo).forEach(key => {
            let find = res.object.find(item => item.id === key);
            if (find.fields && find.fields.length > 0) {
              find.fields.forEach(item => {
                item.isEdit = 0;
                item.isOnly = 1;
              });

              find.fields = find.fields.filter(item => {
                return Object.keys(this.userInfo[key][0]).includes(
                  item.fieldName
                );
              });
            }
          });
          this.personalEditSetting = res.object;

          this.computedSetting();
          // 判断是否获取科室数据
          res.object &&
            res.object.some(
              col =>
                col.fields &&
                col.fields.some(filed => filed.fieldType == 'deptChose')
            ) &&
            this.getDeptTreeData();
        });
    },
    /**@desc 获取工作选项数据 */
    getJobData() {
      this.ajax.getUserInfoEditJobData().then(res => {
        if (res.object) {
          let reSetOptions = function(list) {
            return list.map(item => {
              if (item.children && item.children.length) {
                item.children = reSetOptions(item.children);
              }
              let newItem = {
                ...item,
                label: item.name,
                value: item.id
              };
              delete newItem.name;
              delete newItem.id;
              return newItem;
            });
          };
          this.jobData = reSetOptions(res.object);
        }
      });
    },
    /**@desc 获取科室树数据 */
    getDeptTreeData() {
      this.ajax.getOneLineDeptListData().then(res => {
        if (res.object) {
          this.lineDeptData = res.object;
        }
      });
    },
    /**@desc 处理 tab 点击事件，用于滚动至指定位置 */
    handleActiveTabChange({ value }, index) {
      let pageDom = this.$refs[value][0];
      if (pageDom && pageDom.$el) {
        this.scrollLock = true;
        this.$refs.editContent.$el.scrollTo({
          top: pageDom.$el.offsetTop,
          behavior: 'smooth'
        });
      }
    },
    handleEditContent() {
      if (this.scrollLock) {
        return;
      }
      let scrollTop = this.$refs.editContent.$el.scrollTop,
        childNodeList = Array.from(this.$refs.editContent.$el.childNodes),
        index = childNodeList.findIndex(dom => dom.offsetTop > scrollTop + 50),
        activeTab = this.headTabs[index - 1]?.value;
      this.activeTab != activeTab && (this.activeTab = activeTab);
    },
    handleScrollEnd() {
      this.scrollLock = false;
    },
    handleFoldItem(e) {
      e.target.parentNode.parentNode.classList.toggle('is-fold');
    },
    /**@desc 重新处理填写数据规则 */
    computedSetting() {
      this.personalEditSetting.forEach(setting => {
        if (setting.isDetailed != 1 && setting.fields) {
          /**@desc select 为政治面貌 且 默认值为 中共党员 时，入党时间必填 */
          //#region
          let index = setting.fields.findIndex(
            field =>
              field.fieldType == 'select' &&
              field.dataSource == 4 &&
              field.fieldName == 'political_status' &&
              field.value == 1
          );
          if (index >= 0) {
            let partyDateIndex = setting.fields.findIndex(field => {
              field.fieldName == 'party_date';
            });
            partyDateIndex >= 0 && (setting.fields[partyDateIndex].isMust = 1);
          }
          //#endregion
        }

        if (setting.isDetailed != 1) {
          if (!this.userInfo[setting.id] || !this.userInfo[setting.id].length) {
            this.userInfo[setting.id] = [{}];
          }
          this.form[setting.id] = {};
        } else {
          setting.fields &&
            setting.fields.forEach(field => {
              field.isEdit = setting.isEdit;
            });
          this.form[setting.id] = new Array(
            this.userInfo[setting.id].length
          ).fill({});
        }
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/ts-mobile-oa/pages/personalCenter/edit/edit?fromPage=my&index=0`
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.edit-personal-information-container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .edit-status-container {
    z-index: 9999;
    width: 100%;
    height: 40px;
    background: rgb(22, 132, 252);
    padding: 0 9px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: rgb(251, 251, 252);
    .font {
      font-size: 12px !important;
    }
  }
}
.edit-container {
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  margin-top: 8px;
  background-color: #fff;
  position: relative;
}

.edit-section-container {
  padding: 10px 0;
  border-bottom: 1px dashed #eee;
}

.section-title {
  font-weight: bold;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  uni-text:first-child {
    display: inline-flex;
    align-items: center;
    &::before {
      content: ' ';
      height: 16px;
      width: 4px;
      border-radius: 4px;
      background-color: $theme-color;
      margin-right: 4px;
    }
  }
  .add-item-icon {
    font-size: 20px;
    font-weight: normal;
  }
}

.child-form-item-container {
  display: flex;
  overflow: hidden;
  max-height: 900vh;
  transition: all 0.3s;
  &:not(:last-child) {
    border-bottom: 1px solid #eee;
  }
  &.is-fold {
    max-height: 36px;
    .child-form-action-content .oa-icon-fanhui-copy {
      transform: rotate(180deg);
    }
  }
  .child-form-action-content {
    width: 32px;
    flex-shrink: 0;
    .oa-icon {
      text-align: center;
      line-height: 36px;
      transition: 0.5s transform;
    }
    .oa-icon-gengduo1 {
      transform: rotate(45deg);
      color: $u-type-error;
    }
  }
  .child-form-content {
    flex: 1;
  }
  .form-item {
    margin-left: 0;
  }
}

.popup-content {
  background: #fff;
  max-height: 80vh;
  overflow-x: hidden;
  overflow-y: auto;
  .option-item {
    padding: 8px 0;
    margin: 0 16px;
    &:not(:last-child) {
      border-bottom: 1px solid #eee;
    }
    &.checkbox-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .uni-icons {
        height: 44rpx;
      }
    }
  }
}
</style>
