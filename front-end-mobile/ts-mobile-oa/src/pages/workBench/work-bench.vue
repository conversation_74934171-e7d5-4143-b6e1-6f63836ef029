<template>
  <view class="ts-content" v-if="showContent">
    <view class="content_top">
      <view class="process_statistics">
        <view
          class="process_item"
          v-for="(item, index) in processList"
          :key="index"
          @tap="checkList(item.index, item.path)"
        >
          <view class="process_item_num" :style="{ color: item.color }">{{
            item.num
          }}</view>
          <view class="process_item_name">{{ item.typeName }}</view>
        </view>
      </view>
    </view>
    <view class="content_menus" v-for="item in menuList" :key="item.id">
      <view class="menus_top">
        <text class="menus_title">{{ item.menuname }}</text>
        <!-- <view class="eidtBtn">
					<text class="oa-icon oa-icon-bianji1"></text>
					<text>编辑</text>
				</view> -->
      </view>
      <view class="menus_list">
        <view
          class="menus_item"
          v-for="row in item.menus"
          :key="row.id"
          @tap="jumpPage(row.menuname, row.alink)"
        >
          <text
            class="menus_item_icon oa-icon"
            :class="row.icon.split(';')[0]"
            :style="{ color: row.icon.split(';')[1] }"
          ></text>
          <text class="menus_item_name">{{ row.menuname }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState } from 'vuex';
import wxInit from '@/common/js/wxInit.js';
export default {
  data() {
    return {
      showContent: false,
      processList: [
        {
          typeName: '待办',
          num: null,
          color: '#f59a23',
          index: 0,
          path: '/pages/workflow/workflow-approval-list'
        },
        {
          typeName: '在办',
          num: null,
          color: '#005BAC',
          index: 0,
          path: '/pages/workflow/my-workflow-list'
        },
        {
          typeName: '退回',
          num: null,
          color: '#dd1f36',
          index: 1,
          path: '/pages/workflow/my-workflow-list'
        },
        {
          typeName: '抄送',
          num: null,
          color: '#333333',
          index: null,
          path: '/pages/workflow/workflow-copy-list'
        }
      ],
      menuList: []
    };
  },
  computed: {
    ...mapState(['username', 'headimg'])
  },
  onLoad() {
    this.getWxJsapiSignature();
  },
  onShow() {
    this.getProcessStatistics();
    this.getMenuPermissions();
  },
  methods: {
    //获取签名信息并初始化jdk
    getWxJsapiSignature() {
      let _self = this;
      _self.ajax
        .getWxJsapiSignature({
          REFERER: _self.token
        })
        .then(res => {
          wxInit.initwxJdk(res.object);
        });
    },
    //获取流程统计数
    getProcessStatistics() {
      let _self = this;
      _self.ajax.getWorkBenchMyWfStatistical().then(res => {
        _self.showContent = true;
        let data = res.object;
        _self.processList[0]['num'] = data.toDoCount;
        _self.processList[1]['num'] = data.inProcessCount;
        _self.processList[2]['num'] = data.sendBackCount;
        _self.processList[3]['num'] = data.copyToMeCount;
      });
    },
    //获取菜单权限
    getMenuPermissions() {
      let _self = this;
      _self.ajax
        .getWorkBenchMenuPermissions({
          syscode: 'ts-platform-phone'
        })
        .then(res => {
          let data = res.object;
          _self.menuList = data.length ? res.object[0].menus : [];
        });
    },
    checkList(index, path) {
      uni.navigateTo({
        url: `${path}?fromPage=workBench${
          index != null ? '&index=' + index : ''
        }`
      });
    },
    //跳转页面
    jumpPage(name, path) {
      let _self = this;
      if (name === '会议扫码') {
        wxInit.scanQRCode(_self);
      }
      if (path.indexOf('http://') != -1) {
        window.location.href = path;
      } else {
        let connector = '';
        if (path.indexOf('?') === -1) connector = '?';
        else connector = '&';
        uni.navigateTo({
          url: `${path}${connector}fromPage=workBench&index=0`
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  overflow: auto;
}
.content_top {
  width: 100%;
  background-color: #ffffff;
  margin-top: 20rpx;
  .process_statistics {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 20rpx 0;
    text-align: center;
    font-weight: bold;
    .process_item {
      padding: 20rpx 30rpx;
      .process_item_num {
        font-size: 36rpx;
      }
      .process_item_name {
        font-size: 28rpx;
        color: #333333;
      }
    }
  }
  .setupIcon {
    color: #ffffff;
    position: absolute;
    right: 15px;
    top: 15px;
    line-height: 1;
  }
}
.content_menus {
  margin-top: 20rpx;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  .menus_top {
    padding-bottom: 10rpx;
    position: relative;
    font-weight: bold;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 1px;
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
  }
  .menus_list {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    .menus_item {
      width: 20%;
      display: inline-block;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      margin-top: 20rpx;
      padding-top: 10rpx;
      .menus_item_icon {
        line-height: 1;
        font-size: 72rpx;
      }
      .menus_item_name {
        color: #999999;
        font-size: 24rpx;
        padding: 10rpx 0 0;
        text-align: center;
      }
    }
  }
}
</style>
