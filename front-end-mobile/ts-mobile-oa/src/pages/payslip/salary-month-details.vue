<template>
  <view
    class="ts-content"
    :style="{ backgroundImage: imgSrc }"
    style="background-color: #fff; background-position-x: -60rpx;"
  >
    <page-head title="工资条" @clickLeft="returnBack"></page-head>
    <view class="salary_wrap">
      <view class="content_top_month">
        <view class="month_text">{{ currentDateStr }}工资</view>
      </view>
      <view class="salary_content" v-if="showList">
        <view class="salary_list" v-if="fieldList.length != 0">
          <view
            class="salary_item"
            v-for="(item, index) in fieldList"
            :key="index"
          >
            <view class="salary_item-text">{{ item.title }}</view>
            <view class="salary">
              <view class="salary_item-content salary_item-title">
                <view class="field_text">工资项</view>
                <view class="field_value">元</view>
              </view>
            </view>
            <view class="salary" v-for="(one, key, i) in item.data" :key="i">
              <view
                class="salary_item-content"
                v-if="!excludeKey.includes(key)"
              >
                <view class="field_text">{{ key }}</view>
                <view class="field_value">{{ one }}</view>
              </view>
            </view>
          </view>
        </view>
        <view class="nothing" v-else>
          <view class="img_content">
            <image
              class="nothing_img"
              src="../../static/img/empty.png"
              mode="aspectFit"
            ></image>
          </view>
          <view class="nothing-text">暂未查到数据</view>
        </view>
      </view>
      <canvas
        v-if="showCanvas"
        style="width:150px;height:100px;z-index: -1;"
        canvas-id="salaryCanvas"
        id="salaryCanvas"
      ></canvas>
    </view>
    <date-picker
      startDate="2000"
      mode="date"
      :value="currentDate"
      @confirm="onConfirm"
      ref="date"
      fields="month"
      :disabled-after="true"
    ></date-picker>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import datePicker from '@/components/picker/date-picker.vue';
import loginModule from '@/common/js/loginModule.js';
export default {
  components: {
    datePicker
  },
  data() {
    return {
      tableName: '',
      currentDate: '',
      currentDateStr: '',
      fieldList: [],
      fromPage: '',
      excludeKey: ['主键', '批次号', '发送次数', '状态', '工资发放年月'],
      imgSrc: '',
      showCanvas: true,
      showList: false
    };
  },
  computed: {
    ...mapState(['empcode', 'username'])
  },
  async onLoad(opt) {
    if (opt && opt.token && !this.$config.ENABLE_ACCOUNT_LOGIN) {
      let userInfo = await loginModule.loginInfo(opt);
      if (JSON.stringify(userInfo) != '{}') this.changeState(userInfo);
    }
    this.fromPage = opt.fromPage;
    this.currentDate =
      opt.dateStr != '' && opt.dateStr != undefined
        ? opt.dateStr
        : this.$common.getMonth('', 1).timeStr;
    let dateArray = this.currentDate.split('-');
    this.currentDateStr = `${dateArray[0]}年${dateArray[1]}月`;
    await this.getSalaryDetailsList('/toa_salary_jbgz', this.currentDate);
    await this.getSalaryDetailsList('/toa_salary_jxgz', this.currentDate);
  },
  mounted() {
    let context = uni.createCanvasContext('salaryCanvas', this),
      watermarkText = this.username + '-' + this.empcode;
    context.rotate((330 * Math.PI) / 180);
    context.setFontSize(12);
    context.setFillStyle('rgba(169,169,169,.3)');
    context.fillText(watermarkText, -20, 100);
    setTimeout(() => {
      context.draw();
      uni.canvasToTempFilePath({
        x: 0, // 从canvas的x轴的0点开始选中
        y: 0, // 从canvas的y轴的0点开始选中
        width: 150, // 选中canvas多宽
        height: 100, // 选中canvas多宽
        destWidth: 150, // 生成的图片多宽
        destHeight: 100, // 生成的图片多高
        canvasId: 'salaryCanvas', // canvas的id
        success: res => {
          this.showCanvas = false;
          uni.hideLoading();
          // 在H5平台下，tempFilePath 为 base64
          this.imgSrc = `url(${res.tempFilePath})`;
          this.showList = true;
        }
      });
    }, 1000);
  },
  methods: {
    ...mapMutations(['changeState']),
    showPicker(type) {
      this.$refs[type].show();
    },
    onConfirm(e) {
      this.currentDate = e.result;
      let dateArray = e.result.split('-');
      this.currentDateStr = `${dateArray[0]}年${dateArray[1]}月`;
      this.fieldList = [];
      this.$nextTick(() => {
        this.getSalaryDetailsList('/toa_salary_jbgz', e.result);
        this.getSalaryDetailsList('/toa_salary_jxgz', e.result);
      });
    },
    async getSalaryDetailsList(tableName, dateStr) {
      await this.ajax.getSalaryDetailsList(tableName, dateStr).then(res => {
        if (Object.keys(res.object).length != 0) {
          let salary = {};
          if (tableName == 'toa_salary_jbgz') salary.title = '基本工资';
          else if (tableName == 'toa_salary_jxgz') salary.title = '绩效工资';
          salary.data = res.object;
          this.fieldList.push(salary);
        }
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  .salary_wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .content_top_month {
      width: 100%;
      margin: 20rpx auto;
      .month_text {
        display: inline-block;
        padding: 0 20rpx;
        font-size: 32rpx;
        color: $theme-color;
      }
    }
    .salary_content {
      flex: 1;
      display: flex;
      flex-direction: column;
      width: 94%;
      margin: 0 auto 3%;
      box-sizing: border-box;
      .salary_list {
        width: 100%;
        .salary_item {
          border-bottom: 1px solid #e5e5e5;
        }
        .salary_item-text {
          color: #333;
          font-size: 32rpx;
          padding: 10rpx 30rpx;
          box-sizing: border-box;
          text-align: center;
          font-weight: bold;
        }
        .salary_item-content {
          display: table;
          width: 100%;
          min-height: 70rpx;
          .field_text {
            color: #333;
            width: 50%;
            text-align: center;
            border-right: 1px solid #e5e5e5;
            border-top: 1px solid #e5e5e5;
            border-left: 1px solid #e5e5e5;
            display: table-cell;
            vertical-align: middle;
          }
          .field_value {
            text-align: center;
            width: 50%;
            border-right: 1px solid #e5e5e5;
            border-top: 1px solid #e5e5e5;
            display: table-cell;
            vertical-align: middle;
          }
        }
        .salary_item-title {
          background-color: $theme-color;
          .field_text,
          .field_value {
            color: #ffffff;
            border-color: $theme-color;
          }
        }
      }
      .nothing {
        width: 100%;
        text-align: center;
        position: relative;
        .img_content {
          width: 300rpx;
          height: 300rpx;
          margin: auto;
          .nothing_img {
            width: 100%;
            height: 100%;
          }
        }
        .nothing-text {
          color: #999;
          font-size: 28rpx;
          position: absolute;
          bottom: 40rpx;
          transform: translateX(-50%);
          left: 50%;
        }
      }
    }
  }
}
</style>
