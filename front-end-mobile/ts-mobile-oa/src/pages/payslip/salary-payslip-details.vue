<template>
  <view class="ts-content" style="background-color: #fff;">
    <page-head title="工资条" @clickLeft="returnBack"></page-head>

    <view class="payslip-show">
      <view class="first">
        <p>{{ payrollDate }}</p>
        <p class="money">¥{{ netSalary }}</p>
        <p>实发工资</p>
      </view>

      <div v-show="tips" class="second">
        <p>温馨提示: {{ tips }}</p>
      </div>

      <view class="third">
        <view
          class="show-payslip-item"
          v-for="(value, key) in basicInfo"
          :key="value + key"
        >
          <view class="label-tips">
            <view class="label"> {{ key }} </view>
          </view>
          <view class="value">{{ value }}</view>
        </view>

        <!-- <view class="line-box">'</view> -->

        <view
          class="show-payslip-item"
          v-for="(item, index) in salaryItemArr"
          :key="index"
          :style="item.group ? 'background-color: #eee;' : ''"
        >
          <view :class="{ 'label-tips': true, group: item.group }">
            <view class="label"> {{ item.label }}</view>
            <tooltip
              v-if="item.tips"
              :content="item.tips"
              v-model="item.open"
              placement="top"
            >
              <img class="item-tips" src="@/static/img/dd_tips.svg" alt="" />
            </tooltip>
          </view>
          <view class="value">{{ item.value }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import common from '@/common/js/common.js';

import tooltip from '@/components/tooltip/zb-tooltip.vue';
export default {
  components: {
    tooltip
  },
  data() {
    return {
      salaryItemArr: [],
      payrollDate: '',
      netSalary: '',
      tips: '',
      basicInfo: {}
    };
  },
  computed: {
    ...mapState(['userInfo'])
  },
  async onLoad(opt) {
    this.fromPage = opt.fromPage;

    const res = await this.ajax.getPayrollByEmployeeDetails({
      employeeId: this.userInfo.employeeId,
      payrollDate: opt.payrollDate,
      optionId: opt.optionId
    });
    if (!res.success) {
      uni.showToast({
        icon: 'none',
        title: '获取工资条详情失败!'
      });
      return;
    }
    this.salaryItemArr = [];

    let data = common.deepClone(res.object);
    this.basicInfo = data['基本信息'];
    this.payrollDate = data['payrollDate'];
    this.netSalary = data['实发工资'];
    this.tips = data['温馨提示'];

    let salaryItemData = data['薪资项'];
    if (salaryItemData && !Object.keys(salaryItemData).length) return;

    for (const pKey in salaryItemData) {
      if (!pKey || !salaryItemData[pKey]) break;

      this.salaryItemArr.push({
        label: pKey,
        group: true,
        value: '',
        tips: ''
      });

      if (salaryItemData[pKey] && !Object.keys(salaryItemData[pKey]).length)
        break;

      for (const ckey in salaryItemData[pKey]) {
        let value = salaryItemData[pKey][ckey],
          tips = '';

        if (value && String(value).includes('-')) {
          value = String(value)
            .split('-')
            .shift();
          tips = String(value)
            .split('-')
            .pop();
        }

        this.salaryItemArr.push({
          label: ckey,
          group: false,
          value,
          tips
        });
      }
    }
  },

  methods: {
    returnBack() {
      uni.redirectTo({
        url: '/pages/payslip/salary-payslip-list?fromPage=workBench&index=0'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  font-family: 14px / 1.4 'Microsoft Yahei', Arial, Helvetica, sans-serif;

  .payslip-show {
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .first {
      background: #7504d8;
      background: -webkit-linear-gradient(to right, #4a00e0, #7504d8);
      background: linear-gradient(to right, #4a00e0, #7504d8);

      color: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 16px;
      padding-bottom: 4px;
      > p {
        line-height: 14px;
        margin-bottom: 12px;
        font-size: 14px;
        &.money {
          font-size: 20px;
        }
      }
    }

    .second {
      color: #f78b42;
      border-bottom: 1px solid #eee;
      > p {
        padding: 8px;
        margin: 0;
      }
    }

    .third {
      flex: 1;
      background: #fff;
      display: flex;
      flex-direction: column;
      overflow: auto;
      padding: 0 8px;

      .line-box {
        width: 100%;
        height: 12px;
        background-color: #eee;
        color: #eee;
      }

      .show-payslip-item {
        width: 100%;
        border-bottom: 1px solid #eee;
        padding: 6px 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .label-tips {
          display: flex;

          &.group {
            .label {
              font-weight: 800;
            }
          }

          .item-tips {
            width: 20px;
            height: 20px;
            margin-left: 8px;
            transform: translateY(3px);
          }
        }
      }
    }
  }
}
</style>
