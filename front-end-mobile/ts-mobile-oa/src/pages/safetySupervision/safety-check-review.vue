<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" :title="formTitle"></page-head>
    <view class="content-wrap">
      <view class="row">
        <view class="row_lable">
          <text class="is-required">*</text>{{ title }}</view
        >
        <view class="row_value row_value_textarea">
          <textarea
            class="row_value_textarea_text"
            :focus="true"
            v-model="textareaValue"
            :placeholder="placeholder"
          />
        </view>
      </view>
    </view>
    <view class="bottom_btn">
      <button class="btn-item uni-bg-blue" @tap="submit">
        提交
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formIndex: '',
      formTitle: '',
      type: '',
      title: '',
      placeholder: '',
      textareaValue: '',
      details: {},
      isSubmit: false
    };
  },
  onLoad(opt) {
    this.fromPage = opt.index;
    this.type = opt.type;
    this.details = JSON.parse(uni.getStorageSync('checkDetails'));
  },
  watch: {
    type(newValue) {
      if (newValue == 1) {
        this.formTitle = '确认不通过';
        this.title = '说明';
        this.placeholder = '请说明原因';
      }
    }
  },
  methods: {
    //撤销操作
    async submit() {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;
      if (!this.textareaValue) {
        this.$common.toast('请说明原因');
        this.isSubmit = false;
        return false;
      }
      this.details.checkResult = this.type;
      this.details.remark = this.textareaValue;
      await this.ajax
        .confirmSafety(this.details)
        .then(res => {
          uni.redirectTo({
            url: `/pages/safetySupervision/safety-check-list?index=${this.fromPage}`
          });
        })
        .catch(e => {
          this.isSubmit = false;
        });
    },
    //返回上一层
    returnBack() {
      uni.removeStorageSync('checkDetails');
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.content-wrap {
  flex: 1;
}
.row {
  background-color: #fff;
}
.is-required {
  color: #f00;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 15rpx;
  font-size: 24rpx;
}
.row_lable {
  position: relative;
  padding: 22rpx 30rpx;
}
.row_value {
  flex: 1;
  font-size: 32rpx;
  color: #666;
  padding: 22rpx 30rpx;
  padding-left: 0;
  box-sizing: border-box;
  text-align: right;
}
.row_value_textarea {
  width: 100%;
  padding-left: 30rpx;
  padding-top: 0;
  text-align: left;
}
.row_value_textarea_text {
  width: 100%;
  min-height: 160rpx;
  font-size: 32rpx;
}
.bottom_btn {
  background-color: #ffffff;
  box-shadow: 0 1px 6px #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 22rpx 30rpx;
  box-sizing: border-box;
  z-index: 10;
}
.btn-item {
  flex: 1;
  box-sizing: border-box;
  text-align: center;
  font-size: 32rpx;
  position: relative;
  border-radius: 10rpx;
}
</style>
