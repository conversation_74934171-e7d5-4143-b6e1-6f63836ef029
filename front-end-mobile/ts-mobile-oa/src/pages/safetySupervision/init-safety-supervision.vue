<template>
  <view class="ts-content">
    <page-head :title="title" @clickLeft="returnBack" />
    <view
      class="select-container"
      v-if="contentList.length > 0"
      data-ref="popup"
      data-popup-type="allBottom"
      data-popup-choice="radio"
      @click="showPopup"
    >
      <text class="select-text">{{ selectContent }}</text>
      <text class="select-icon oa-icon oa-icon-down-copy"></text>
    </view>
    <view class="uni-list">
      <scroll-view
        id="safety-content"
        class="safety-list"
        :scroll-top="scrollTop"
        scroll-y="true"
        :scroll-into-view="scrollViewId"
        :scroll-with-animation="true"
        @scroll="scroll"
      >
        <view
          v-for="(item, index) in contentList"
          :key="item.id"
          class="safety-list-item"
          :id="`safetylist_${index}`"
        >
          <data-group :title="item.checkContent" marginTop="0">
            <data-check
              v-model="item.checkResult"
              :checkList="checkList"
              selectedColor="#005BAC"
            ></data-check>
            <view class="border-top">
              <view class="form-item">
                <uni-easyinput
                  v-model="item.remark"
                  :input-border="false"
                  type="textarea"
                  :maxlength="50"
                  placeholder="请简要说明，不超过50个字"
                />
              </view>
              <view class="form-item" v-if="item.checkResult == 1">
                <text class="is-required">*</text>
                <text class="form-item-lable">交办人</text>
                <uni-easyinput
                  v-model="item.assignUserName"
                  :input-border="false"
                  type="input"
                  text-align="right"
                  suffix-icon="arrowright"
                  :disabled="true"
                  placeholder="请选择交办人"
                  @click="showPersonSelet(index)"
                />
              </view>
            </view>
            <form-file
              class="file-wrap border-top"
              title="附件"
              addIconStyle="font-size: 56rpx"
              uploadPath="ts-basics-bottom/fileAttachment/upload?moduleName=hrms"
              :uploadParam="{
                businessId: item.businessId
              }"
              @click="addFile(item.businessId, index)"
            >
              <img-list v-if="item.imgList && item.imgList.length > 0">
                <img-list-item
                  v-for="one in item.imgList"
                  :key="one.id"
                  :imgSrc="`${$config.BASE_HOST}${one.realPath}`"
                  @click="downloadFile(one)"
                >
                  <text
                    class="img-icon-wrapper delet-icon oa-icon oa-icon-shanchu1"
                    @click="deletFile(item.businessId, one.id, index)"
                  >
                  </text>
                </img-list-item>
              </img-list>
              <uni-list v-if="item.fileList && item.fileList.length > 0">
                <uni-list-item
                  v-for="(one, i) in item.fileList"
                  :key="one.id"
                  :title="one.originalName"
                  listItemStyle="padding-left: 0;padding-right: 0;"
                  :showArrow="false"
                  @click="downloadFile(one)"
                >
                  <text
                    slot="right"
                    class="delet-icon oa-icon oa-icon-shanchu1"
                    @click="deletFile(item.businessId, one.id, i)"
                  ></text>
                </uni-list-item>
              </uni-list>
            </form-file>
          </data-group>
        </view>
      </scroll-view>
    </view>
    <view class="btn-wrap">
      <button class="btn-item" @tap="returnBack">
        取消
      </button>
      <button class="btn-item theme-color" @tap="saveSafetyContent">
        确定
      </button>
    </view>
    <uni-popup
      :type="picker.popupType"
      :popup-choice="picker.popupChoice"
      top-height="45%"
      ref="popup"
    >
      <uni-list>
        <uni-list-item
          v-for="(one, index) in contentList"
          :key="one.id"
          :title="one.checkContent"
          titleStyle="overflow: hidden; white-space: nowrap; text-overflow: ellipsis;display: block;"
          :showArrow="false"
          @click="selectSafety(index)"
        >
          <view class="select_icon" slot="right">
            <uni-icons
              v-if="activeIndex === index"
              type="checkmarkempty"
              color="#005BAC"
              size="44"
            />
          </view>
        </uni-list-item>
      </uni-list>
    </uni-popup>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import Base64 from '../../common/js/base64.min.js';
export default {
  data() {
    return {
      checkList: [
        {
          text: '达标',
          value: 0
        },
        {
          text: '未达标',
          value: 1
        }
      ],
      title: '',
      activeIndex: 0,
      picker: {},
      pickIndex: 0,
      contentList: [],
      nodeInfoList: [], //dom节点
      scrollStatus: true, //点击状态，是否能点击
      scrollViewId: '',
      old: {
        scrollTop: 0
      },
      scrollTop: 0
    };
  },
  async onLoad(opt) {
    let details = JSON.parse(uni.getStorageSync('routeDetails')),
      content = details['addressList'][opt.index];
    this.title = content.checkAddress;
    this.contentList = content.startCheckDetails;
    this.$nextTick(() => {
      this.initScroll();
    });
  },
  computed: {
    ...mapState(['empcode']),
    isShowHead() {
      return this.contentList.length > 0;
    },
    selectContent() {
      return this.contentList[this.activeIndex].checkContent;
    }
  },
  watch: {
    contentList: {
      handler() {
        this.initScroll();
      },
      deep: true
    }
  },
  methods: {
    initScroll() {
      const query = uni.createSelectorQuery().in(this);
      query
        .selectAll('#safety-content .safety-list-item')
        .boundingClientRect(data => {
          let head = 89,
            top = 0,
            bottom = 0,
            res = data.map((item, index) => {
              if (index == 0) top = head;
              else top = bottom;
              bottom = top + item.height;
              return {
                top: top,
                bottom: bottom
              };
            });
          this.$set(this, 'nodeInfoList', res);
        })
        .exec();
    },
    //显示弹出层
    showPopup(e) {
      let data = e.currentTarget.dataset;
      this.$set(this.picker, 'popupType', data.popupType);
      this.$set(this.picker, 'popupChoice', data.popupChoice);
      this.$set(this.picker, 'tap', data.tap);
      this.$set(this.picker, 'ref', data.ref);
      this.$nextTick(() => {
        this.$refs[data.ref].open();
      });
    },
    scroll(e) {
      if (!this.scrollStatus) return;
      let scrollTop = (this.old.scrollTop = e.detail.scrollTop);
      let current = this.nodeInfoList
        .map((item, index) => ({ index, ...item }))
        .filter(item => item.top <= scrollTop + 89)
        .sort((a, b) => b.top - a.top)[0];
      if (this.pickIndex != current.index) this.activeIndex = this.pickIndex;
      else this.activeIndex = current.index;
      setTimeout(() => {
        // 10毫秒才能执行下次点击
        this.pickIndex = current.index;
      }, 10);
    },
    //点击跳到指定位置事件
    selectSafety(index) {
      if (this.picker.ref) this.$refs[this.picker.ref].close();
      if (!this.scrollStatus) return;
      // 200毫秒才能执行下次点击
      if (this.scrollStatus) {
        this.scrollStatus = false;
        // 解决clickedNavIndex相同触发更新失败
        if (this.activeIndex == index) {
          this.scrollViewId = 'safetylist';
        }
        this.$nextTick(() => {
          this.scrollViewId = `safetylist_${index}`;
          this.activeIndex = this.pickIndex = index;
        });
        setTimeout(() => {
          // 200毫秒才能执行下次点击
          this.scrollStatus = true;
        }, 200);
      }
    },
    async addFile(businessId, index) {
      let list = await this.getFileList(businessId);
      this.$set(this.contentList[index], 'imgList', list.img);
      this.$set(this.contentList[index], 'fileList', list.file);
    },
    //获取文件列表
    async getFileList(businessId) {
      let listObj = { file: [], img: [] };
      await this.ajax
        .getFileAttachmentByBusinessId({
          businessId: businessId
        })
        .then(res => {
          res.object.forEach(i => {
            if (
              i.fileExtension == 'jpg' ||
              i.fileExtension == 'jpeg' ||
              i.fileExtension == 'png' ||
              i.fileExtension == 'gif' ||
              i.fileExtension == 'img'
            )
              listObj.img.push(i);
            else listObj.file.push(i);
          });
        });
      return listObj;
    },
    async deletFile(businessId, fileId, index) {
      await this.ajax
        .deletFileById({
          fileid: fileId
        })
        .then(async res => {
          await this.addFile(businessId, index);
        });
    },
    //跳转至人员选择
    showPersonSelet(index) {
      //监听事件
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        this.$set(this.contentList[index], 'personList', data);
        let agentNameArr = [],
          agentIdArr = [];
        data.forEach(item => {
          agentNameArr.push(item.name);
          agentIdArr.push(item.id);
        });
        this.$set(
          this.contentList[index],
          'assignUserName',
          agentNameArr.join(',')
        );
        this.$set(this.contentList[index], 'assignUser', agentIdArr.join(','));
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      uni.setStorageSync(
        'person_list',
        JSON.stringify(this.contentList[index].personList)
      );
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=radio'
      });
    },
    saveSafetyContent() {
      let datas = JSON.parse(JSON.stringify(this.contentList));
      for (var i = 0; i < datas.length; i++) {
        if (
          datas[i].checkResult == 1 &&
          (!datas[i].assignUser || !datas[i].remark)
        ) {
          this.selectSafety(i);
          this.$common.toast('有必填项未填');
          return false;
        }
      }
      uni.$emit('safetyDetails', this.contentList);
      uni.navigateBack({
        delta: 1
      });
    },
    //查看附件详情
    downloadFile(item) {
      let filePath = `${
        this.$config.ENABLE_FILE_PREVIEW
          ? this.$config.DOCUMENT_BASE_HOST
          : this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/downloadFile/${item.id}?fullfilename=${
        item.originalName
      }&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      } else {
        this.$downloadFile.downloadFile(filePath);
      }
    },
    //返回
    returnBack() {
      uni.removeStorageSync('routeDetails');
      uni.$off('safetyDetails');
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.select-container {
  overflow: hidden;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 10px;
}
.select-icon {
  font-size: 16px;
  color: #999;
}
.select-text {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 14px;
}
.uni-list {
  background-color: transparent;
  flex: 1;
  overflow: hidden;
}
.safety-list {
  height: 100%;
  overflow: hidden;
}
.safety-list-item {
  padding-top: 10px;
}
.delet-icon {
  font-size: 40rpx;
  color: #f00;
}
.form-item {
  position: relative;
  display: flex;
  flex-direction: row;
}
.form-item-lable {
  min-height: 36px;
  line-height: 36px;
  width: 100px;
}
.is-required {
  color: #f00;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: -20rpx;
  font-size: 24rpx;
}
.file-wrap {
  padding: 20rpx 0;
}
.border-top {
  border-top: 1px solid #eee;
}
.img-icon-wrapper {
  position: absolute;
  top: 2px;
  right: 2px;
  line-height: 40rpx;
  border-radius: 100%;
  background-color: #ffffff;
}
.select_icon {
  width: 44rpx;
  height: 44rpx;
  line-height: 44rpx;
}
.btn-wrap {
  background-color: #ffffff;
  box-shadow: 0 1px 6px #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}
.theme-color {
  color: $theme-color !important;
}
.btn-item {
  height: 90rpx;
  flex: 1;
  box-sizing: border-box;
  text-align: center;
  font-size: 32rpx;
  position: relative;
  background-color: transparent;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  &::after {
    border: 0;
    top: 20rpx;
    bottom: 20rpx;
    right: 0;
    left: unset;
    transform: scaleX(-0.5);
    width: 1px;
    height: unset;
    background-color: #ccc;
  }
  &:last-child::after {
    width: 0;
  }
}
</style>
