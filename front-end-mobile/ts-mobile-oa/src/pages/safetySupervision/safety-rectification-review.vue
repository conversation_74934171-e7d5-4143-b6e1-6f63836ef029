<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" :title="formTitle"></page-head>
    <view class="content-wrap">
      <view class="uni-list">
        <view class="uni-list-item">
          <view class="item-lable">
            <text class="is-required">*</text>
            {{ title }}
          </view>
          <view class="item-value item-value_textarea">
            <textarea
              class="item-value_textarea-text"
              :maxlength="maxlength"
              :focus="true"
              v-model="textareaValue"
              :placeholder="placeholder"
            />
          </view>
        </view>
        <view class="uni-list-item">
          <view style="padding: 20rpx;">
            <form-file
              class="border-top"
              title="附件"
              addIconStyle="font-size: 56rpx"
              uploadPath="ts-basics-bottom/fileAttachment/upload?moduleName=hrms"
              :uploadParam="{
                businessId: datas.businessId
              }"
              @click="addFile(datas.businessId)"
            >
              <img-list v-if="imgList.length > 0">
                <img-list-item
                  v-for="one in imgList"
                  :key="one.id"
                  :imgSrc="`${$config.BASE_HOST}${one.realPath}`"
                  @click="downloadFile(one)"
                >
                  <text
                    class="img-icon-wrapper delet-icon oa-icon oa-icon-shanchu1"
                    @click="deletFile(datas.businessId, one.id)"
                  >
                  </text>
                </img-list-item>
              </img-list>
              <uni-list v-if="fileList.length > 0">
                <uni-list-item
                  v-for="one in fileList"
                  :key="one.id"
                  :title="one.originalName"
                  listItemStyle="padding-left: 0;padding-right: 0;"
                  :showArrow="false"
                  @click="downloadFile(one)"
                >
                  <text
                    slot="right"
                    class="delet-icon oa-icon oa-icon-shanchu1"
                    @click="deletFile(datas.businessId, one.id)"
                  ></text>
                </uni-list-item>
              </uni-list>
            </form-file>
          </view>
        </view>
      </view>
    </view>
    <view class="bottom-btn">
      <button class="btn-item uni-bg-blue" @tap="submit">
        提交
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      fromPage: '',
      formTitle: '',
      type: '',
      title: '',
      placeholder: '',
      textareaValue: '',
      maxlength: 50,
      datas: {
        ids: '',
        businessId: this.$common.guid()
      },
      imgList: [],
      fileList: [],
      isSubmit: false
    };
  },
  onLoad(opt) {
    this.fromPage = opt.index;
    this.type = opt.type;
    this.datas.ids = decodeURIComponent(opt.ids);
  },
  watch: {
    type(newValue) {
      if (newValue == 'complete') {
        this.formTitle = '完成整改';
        this.title = '整改措施';
        this.placeholder = '请输入整改措施';
        this.maxlength = 50;
      } else if (newValue == 'notPass') {
        this.formTitle = '确认不通过';
        this.title = '说明';
        this.placeholder = '必填，请简要说明，不超过50个字';
        this.maxlength = 50;
      }
    }
  },
  methods: {
    async addFile(businessId) {
      let list = await this.getFileList(businessId);
      this.$set(this, 'imgList', list.img);
      this.$set(this, 'fileList', list.file);
    },
    //获取文件列表
    async getFileList(businessId) {
      let listObj = { file: [], img: [] };
      await this.ajax
        .getFileAttachmentByBusinessId({
          businessId: businessId
        })
        .then(res => {
          res.object.forEach(i => {
            if (
              i.fileExtension == 'jpg' ||
              i.fileExtension == 'jpeg' ||
              i.fileExtension == 'png' ||
              i.fileExtension == 'gif' ||
              i.fileExtension == 'img'
            )
              listObj.img.push(i);
            else listObj.file.push(i);
          });
        });
      return listObj;
    },
    async deletFile(businessId, fileId) {
      await this.ajax
        .deletFileById({
          fileid: fileId
        })
        .then(async res => {
          await this.addFile(businessId);
        });
    },
    //提交
    submit() {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;
      if (!this.textareaValue) {
        this.$common.toast(this.placeholder);
        this.isSubmit = false;
        return false;
      }
      if (this.type == 'complete') this.complete();
      else this.notPass();
    },
    //完成整改
    async complete() {
      this.datas.rectificationRemark = this.textareaValue;
      await this.ajax
        .finishRectification(this.datas)
        .then(res => {
          uni.redirectTo({
            url: `/pages/safetySupervision/safety-rectification-list?index=${this.fromPage}`
          });
        })
        .catch(e => {
          this.isSubmit = false;
        });
    },
    //复查不通过
    async notPass() {
      this.datas.unqualifiedRemark = this.textareaValue;
      this.datas.checkResult = '1';
      await this.ajax
        .batchCheckRectification(this.datas)
        .then(res => {
          uni.redirectTo({
            url: `/pages/safetySupervision/safety-rectification-list?index=${this.fromPage}`
          });
        })
        .catch(e => {
          this.isSubmit = false;
        });
    },
    //返回上一层
    returnBack() {
      uni.removeStorageSync('checkDetails');
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.content-wrap {
  flex: 1;
}
.uni-list-item {
  position: relative;
}
.uni-list-item:after {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: $uni-border-color;
}
.is-required {
  color: #f00;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 15rpx;
  font-size: 24rpx;
}
.item-lable {
  padding: 22rpx 30rpx;
  position: relative;
}
.item-value {
  flex: 1;
  font-size: 32rpx;
  color: #666;
  padding: 22rpx 30rpx;
  padding-left: 0;
  box-sizing: border-box;
  text-align: right;
}
.item-value_textarea {
  width: 100%;
  text-align: left;
  padding-left: 30rpx;
  padding-top: 0;
}
.item-value_textarea-text {
  width: 100%;
  min-height: 160rpx;
  font-size: 32rpx;
}
.delet-icon {
  font-size: 40rpx;
  color: #f00;
}
.img-icon-wrapper {
  position: absolute;
  top: 2px;
  right: 2px;
  line-height: 40rpx;
  border-radius: 100%;
  background-color: #ffffff;
}
.bottom-btn {
  background-color: #ffffff;
  box-shadow: 0 1px 6px #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 22rpx 30rpx;
  box-sizing: border-box;
  z-index: 10;
}
.btn-item {
  flex: 1;
  box-sizing: border-box;
  text-align: center;
  font-size: 32rpx;
  position: relative;
  border-radius: 10rpx;
}
</style>
