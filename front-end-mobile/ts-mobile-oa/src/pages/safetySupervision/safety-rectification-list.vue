<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="安全整改"></page-head>
    <view class="content-top">
      <view class="top-tab">
        <scroll-view
          class="swiper-head"
          :scroll-x="true"
          :show-scrollbar="false"
        >
          <view
            v-for="(tab, index) in tabBars"
            :key="index"
            class="uni-tab-item"
            :data-current="index"
            @click="ontabtap"
          >
            <view :class="tabIndex == index ? 'uni-tab-item-title-active' : ''">
              <text class="uni-tab-item-title">{{ tab.name }}</text>
              <text
                class="uni-tab-item-num"
                v-if="tab.total != null && tab.total != 0 && index != 2"
                >{{ tab.total >= 100 ? '99+' : tab.total }}</text
              >
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="search-sift" @click="changeDrawer">
        <text class="search-sift-icon oa-icon oa-icon-shaixuan"></text>
      </view>
      <view class="search-sift" @click="changeOperation">
        <text
          class="search-sift-icon oa-icon oa-icon-bianji1"
          :class="tabIndex == 2 ? 'search-sift-icon-disable' : ''"
        ></text>
      </view>
    </view>
    <swiper
      :current="tabIndex"
      class="swiper-box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in tabBars"
        :key="index"
      >
        <mescroll
          :ref="'mescroll' + index"
          :mescrollIndex="index"
          :down="item.downOption"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view class="uni-list">
            <view
              class="uni-list-item"
              v-for="row in item['list']"
              :key="row.id"
            >
              <uni-icons
                class="item-choose"
                v-if="item.isShowCheck"
                :type="row.choose ? 'checkbox-filled' : 'circle'"
                :color="row.choose ? '#005BAC' : '#aaa'"
                size="48"
              />
              <view
                class="item-content"
                @click="
                  item.isShowCheck ? chooseItem(row.id) : jumpDetails(row.id)
                "
              >
                <view class="item-head">
                  <text class="item-safety-title">
                    {{ row.projectName }} - {{ row.checkAddress }}
                  </text>
                  <text class="item-check-date">{{ row.checkDate }}</text>
                </view>
                <view class="item-safety-text">
                  {{ row.checkContent }}
                </view>
                <view
                  class="item-safety-text notUpToStandard"
                  v-if="!item.handleStatus"
                >
                  说明：{{ row.unqualifiedRemark }}
                </view>
                <view class="item-safety-person" v-if="item.handleStatus">
                  整改时间：{{ row.updateDate | formatTime }}
                </view>
                <view class="item-safety-person">
                  <text class="item-safety-checkperson"
                    >检查人：{{ row.checkUserName }}</text
                  >
                  <text class="item-safety-checkperson"
                    >核查人：{{ row.examineUserName }}</text
                  >
                  <text class="item-safety-checkperson"
                    >交办人：{{ row.assignUserName }}</text
                  >
                </view>
              </view>
            </view>
          </view>
        </mescroll>
      </swiper-item>
    </swiper>
    <transition name="slide-fade">
      <view class="btn-wrap" v-show="tabBars[tabIndex]['isShowCheck']">
        <view
          class="btn-item theme-color"
          v-if="tabIndex == 0"
          @tap="confirm('complete')"
          >完成整改</view
        >
        <view class="btn-item" v-if="tabIndex == 1" @click="confirm('notPass')"
          >不通过</view
        >
        <view
          class="btn-item theme-color"
          v-if="tabIndex == 1"
          @click="confirm('pass')"
          >通过</view
        >
      </view>
    </transition>
    <uni-drawer :visible="showRight" mode="right" @close="closeDrawer('right')">
      <view
        class="drawer-option-tap"
        v-for="(value, key, index) in filterCondition"
        :key="index"
      >
        <view class="drawer-option-title" v-if="key != 'endDate'">{{
          key | titleFilter
        }}</view>
        <view
          class="drawer-option-list"
          v-if="key != 'startDate' && key != 'endDate'"
        >
          <view class="drawer-option-item">
            <input
              class="drawer-option-item-text"
              v-model="filterCondition[key]"
              type="text"
              :placeholder="key | titleFilter('请输入')"
            />
          </view>
        </view>
        <view class="drawer-option-list" v-else-if="key == 'startDate'">
          <text
            class="drawer-option-item time-item"
            @tap="showPicker('range')"
            >{{ filterCondition.startDate }}</text
          >
          <text class="drawer-option-item divider">-</text>
          <text
            class="drawer-option-item time-item"
            @tap="showPicker('range')"
            >{{ filterCondition.endDate }}</text
          >
        </view>
      </view>
      <view class="drawer-btn-wrap">
        <view class="drawer-btn-item cancle-btn" @tap="reset">重置</view>
        <view class="drawer-btn-item them-btn" @tap="submit">确定</view>
      </view>
    </uni-drawer>
    <date-picker
      startDate="2000-01-01"
      :value="agentTimeArr"
      endDate="2100-12-31"
      pickerZindex="999"
      mode="range"
      :current="true"
      @confirm="onConfirm"
      @cancel="onCancel"
      ref="range"
    ></date-picker>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import datePicker from '@/components/picker/date-picker.vue';
import uniDrawer from '@/components/uni-drawer/uni-drawer.vue';
export default {
  components: {
    mescroll,
    datePicker,
    uniDrawer
  },
  data() {
    return {
      tabIndex: 0, //当前选中的tab索引值，从0计数
      tabBars: [
        {
          name: '待整改',
          handleStatus: 0,
          downOption: false,
          isInit: false,
          isShowCheck: false,
          list: [],
          total: 0
        },
        {
          name: '待复查',
          handleStatus: 1, //办理状态
          downOption: false, //初始化时是否下拉加载
          isInit: false, //是否已初始化
          isShowCheck: false,
          list: [],
          total: 0
        },
        {
          name: '已完成',
          handleStatus: 3,
          downOption: false,
          isInit: false,
          isShowCheck: false,
          list: [],
          total: 0
        }
      ],
      showRight: false,
      routerList: [],
      filterCondition: {
        checkAddress: '',
        checkContent: '',
        assignUserName: '',
        startDate: '',
        endDate: '',
        checkUserName: '',
        examineUserName: ''
      }
    };
  },
  computed: {
    agentTimeArr: function() {
      return [this.filterCondition.startDate, this.filterCondition.endDate];
    }
  },
  filters: {
    titleFilter(value, str = '') {
      let key = '';
      switch (value) {
        case 'checkAddress':
          key = '检查地点';
          break;
        case 'checkContent':
          key = '检查内容';
          break;
        case 'assignUserName':
          key = '交办人';
          break;
        case 'startDate':
          key = '检查时间';
          break;
        case 'checkUserName':
          key = '检查人';
          break;
        case 'examineUserName':
          key = '核查人';
          break;
      }
      return str + key;
    }
  },
  onLoad(opt) {
    if (opt.index) this.tabIndex = Number(opt.index);
    this.tabBars.map((item, index) => {
      if (this.tabIndex == index) {
        item.downOption = true;
        item.isInit = true;
      } else {
        item.downOption = false;
        item.isInit = false;
      }
    });
  },
  methods: {
    changeOperation() {
      if (this.tabIndex == 2) return false;
      let isShowCheck = this.tabBars[this.tabIndex]['isShowCheck'];
      this.$set(this.tabBars[this.tabIndex], 'isShowCheck', !isShowCheck);
      if (!isShowCheck) {
        this.tabBars[this.tabIndex]['list'].forEach(one => {
          one.choose = false; // 全部设为false
        });
      }
    },
    chooseItem(id) {
      this.tabBars[this.tabIndex]['list'].forEach(one => {
        if (one.id == id) one.choose = !one.choose;
      });
      this.$forceUpdate();
    },
    //分类抽屉切换
    changeDrawer() {
      this.showRight = !this.showRight;
    },
    showDrawer() {
      this.showRight = true;
    },
    closeDrawer() {
      this.showRight = false;
    },
    //显示时间弹出层
    showPicker(dateRef) {
      this.$refs[dateRef].show();
    },
    //时间选择确认
    onConfirm(res) {
      this.filterCondition.startDate = `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`;
      this.filterCondition.endDate = `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`;
    },
    //时间取消
    onCancel() {},
    //重置
    reset() {
      for (var key in this.filterCondition) {
        this.filterCondition[key] = '';
      }
      this.tabBars.forEach(item => {
        item.list = [];
        item.isInit = false;
      });
      this.tabBars[this.tabIndex]['isInit'] = true;
      this.$refs[`mescroll${this.tabIndex}`][0].downCallback();
    },
    //确定
    submit() {
      this.tabBars.forEach(item => {
        item.list = [];
        item.isInit = false;
      });
      this.tabBars[this.tabIndex]['isInit'] = true;
      this.$nextTick(() => {
        this.closeDrawer();
        this.$refs[`mescroll${this.tabIndex}`][0].downCallback();
      });
    },
    //tab点解切换
    async ontabtap(e) {
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      await this.switchTab(Number(index));
    },
    //tab滑动切换
    async ontabchange(e) {
      let index = e.target.current || e.detail.current;
      await this.switchTab(Number(index));
    },
    async switchTab(index) {
      if (this.tabIndex === index) {
        return;
      } else if (!this.tabBars[index]['isInit']) {
        this.tabBars[index]['isInit'] = true;
        await this.$refs[`mescroll${index}`][0].downCallback();
      }
      this.tabIndex = index;
    },
    //根据类型获取数据
    async getListData(page, successCallback, errorCallback, keywords, index) {
      await this.ajax
        .getRectificationList({
          status: this.tabBars[index]['handleStatus'],
          pageSize: page.size,
          pageNo: page.num,
          checkAddress: this.filterCondition.checkAddress,
          checkContent: this.filterCondition.checkContent,
          startDate: this.filterCondition.startDate,
          endDate: this.filterCondition.endDate,
          checkUserName: this.filterCondition.checkUserName,
          examineUserName: this.filterCondition.examineUserName,
          assignUserName: this.filterCondition.assignUserName,
          sidx: 'create_date',
          sord: 'desc'
        })
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      rows.forEach(item => {
        this.tabBars[index]['list'].push(item);
      });
    },
    datasInit(keywords, index) {
      this.tabBars[index]['list'] = [];
    },
    jumpDetails(id) {
      uni.navigateTo({
        url: `/pages/safetySupervision/safety-rectification-details?id=${id}&index=${this.tabIndex}`
      });
    },
    confirm(type) {
      let selectedIdStr = [];
      this.tabBars[this.tabIndex].list.forEach(item => {
        if (item.choose) selectedIdStr.push(item.id);
      });
      let ids = selectedIdStr.join(',');
      if (!ids) {
        this.$common.toast('请选择数据');
        return false;
      }
      if (type == 'pass') {
        this.pass(ids);
      } else {
        uni.navigateTo({
          url: `/pages/safetySupervision/safety-rectification-review?type=${type}&index=${
            this.tabIndex
          }&ids=${encodeURIComponent(ids)}`
        });
      }
    },
    async pass(ids) {
      await this.ajax
        .examineRectification({
          ids: ids,
          checkResult: '0'
        })
        .then(res => {
          uni.redirectTo({
            url: `/pages/safetySupervision/safety-rectification-list?index=${this.tabIndex}`
          });
        });
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.content-top {
  display: flex;
  flex-direction: row;
  position: relative;
  &::before,
  &::after {
    position: absolute;
    z-index: 10;
    right: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #eeeeee;
  }
  &::before {
    top: 0;
  }
}
.top-tab {
  flex: 1;
}
.search-sift {
  font-size: 28rpx;
  color: #666666;
  padding: 0 16rpx;
  line-height: 76rpx;
  background-color: #ffffff;
}
.search-sift-icon {
  font-size: 36rpx;
  color: #666666;
}
.search-sift-icon-disable {
  color: #cccccc;
}
.swiper-head {
  position: relative;
  width: 100%;
  height: 80rpx;
  background-color: #ffffff;
  flex-direction: row;
  box-sizing: border-box;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
  /* flex-wrap: nowrap; */
  /* border-color: #cccccc;
		border-bottom-style: solid;
		border-bottom-width: 1px; */
}
.uni-tab-item {
  /* #ifndef APP-PLUS */
  display: inline-block;
  /* #endif */
  flex-wrap: nowrap;
  width: 33.33333%;
  box-sizing: border-box;
  text-align: center;
}
.uni-tab-item-title,
.uni-tab-item-num {
  color: #555;
  font-size: 30rpx;
  height: 80rpx;
  line-height: 76rpx;
  flex-wrap: nowrap;
  box-sizing: border-box;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
}
.uni-tab-item-num {
  font-size: 28rpx;
  color: #fff;
  background-color: #f59a23;
  border-radius: 40rpx;
  padding: 0 10rpx;
  margin: 0 10rpx;
}
.uni-tab-item-title-active {
  color: $theme-color;
  border-bottom: 2px solid $theme-color;
}
.swiper-box {
  flex: 1;
}
.swiper-item {
  flex: 1;
  flex-direction: row;
}
.uni-list-item {
  position: relative;
  padding: 20rpx 30rpx;
  display: flex;
  /* align-items: center; */
}
.uni-list-item:after {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: $uni-border-color;
}
.item-choose {
  margin-right: 20rpx;
}
.item-content {
  flex: 1;
  min-width: 0;
}
.item-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.item-safety-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
  flex: 1;
}
.item-safety-text {
  font-size: 28rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}
.item-check-date {
  font-size: 24rpx;
  color: #999;
}
.item-safety-person {
  font-size: 28rpx;
  color: #666666;
}
.item-safety-checkperson {
  margin-right: 30rpx;
}
.notUpToStandard {
  color: $uni-color-error;
}
.drawer-option-tap {
  padding: 0 20rpx;
}
.drawer-option-title {
  height: 56rpx;
  line-height: 56rpx;
  font-size: 28rpx;
  margin: 10rpx 0;
  color: #666;
}
.drawer-option-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.drawer-option-item {
  line-height: 70rpx;
  height: 70rpx;
  width: 100%;
  text-align: center;
  background: #ffffff;
  display: flex;
  align-items: center;
  font-size: 28rpx;
  box-sizing: border-box;
  color: #333333;
  padding: 0 20rpx;
  border: 0;
  border-radius: 40rpx;
  background-color: #f2f2f2;
}
.drawer-option-item-text {
  font-size: 28rpx;
  color: #333333;
  width: 100%;
  text-align: left;
}
.time-item {
  width: 40%;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.divider {
  width: auto;
  border: 0;
  display: inline-block;
  box-sizing: border-box;
  background-color: transparent;
}
.drawer-btn-wrap {
  margin: 80rpx 0;
  padding: 0 20rpx;
  text-align: right;
}
.drawer-btn-item {
  border-radius: 8rpx;
  line-height: 70rpx;
  height: 70rpx;
  width: 30%;
  text-align: center;
  background: #ffffff;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 28rpx;
  border: 1px solid #ddd;
  border-right: 0;
  box-sizing: border-box;
  color: #333333;
}
.cancle-btn {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.them-btn {
  border-left: 0;
  border: 1px solid #005bac;
  background-color: #005bac;
  color: #ffffff;
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
  margin-right: 4%;
}
.scroll-list {
  max-height: 800rpx;
  overflow: auto;
}
.pop-list-item {
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333333;
  position: relative;
}
.pop-list-item::after {
  position: absolute;
  z-index: 10;
  right: 0;
  left: 30rpx;
  bottom: 0;
  height: 1px;
  content: '';
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: #eeeeee;
}
.pop-list-item:last-child::after {
  height: 0;
}
.pop-list-item-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}
.pop-selected-icon {
  line-height: 1;
  position: absolute;
  right: 10rpx;
  top: 50%;
  transform: translateY(-50%);
}
.pop-button-list {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  background-color: #ffffff;
  box-shadow: 0 1px 6px #cccccc;
  z-index: 10;
}
.pop-button-item {
  font-size: 28rpx;
  margin: 0 20rpx;
  width: 160rpx;
}
.btn-wrap {
  z-index: 9;
  background-color: #ffffff;
  box-shadow: 0 1px 6px #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}
.theme-color {
  color: $theme-color !important;
}
.btn-item {
  height: 90rpx;
  flex: 1;
  box-sizing: border-box;
  text-align: center;
  font-size: 32rpx;
  position: relative;
  background-color: transparent;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  &::after {
    border: 0;
    top: 20rpx;
    bottom: 20rpx;
    right: 0;
    left: unset;
    transform: scaleX(-0.5);
    width: 1px;
    height: unset;
    background-color: #eee;
  }
  &:last-child::after {
    width: 0;
  }
}
</style>
