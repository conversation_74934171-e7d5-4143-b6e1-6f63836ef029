<template>
  <view class="ts-content" v-if="showContent">
    <page-head :title="title" @clickLeft="returnBack" />
    <scroll-view
      class="swiper-head"
      :scroll-x="true"
      :show-scrollbar="false"
      v-if="status != 0"
    >
      <view
        v-for="(tabItem, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        :data-view-id="`safety_${tabItem.viewId}`"
        :data-current="index"
        @click="ontabtap"
      >
        <text
          class="uni-tab-item-title"
          :class="tabIndex == index ? 'uni-tab-item-title-active' : ''"
          >{{ tabItem.name }}</text
        >
      </view>
    </scroll-view>
    <view class="content-wrap">
      <scroll-view
        id="safetyCheckResultDetails"
        style="height: 100%;"
        scroll-y="true"
        :scroll-into-view="scrollViewId"
        :scroll-with-animation="true"
        @scroll="scroll"
      >
        <view class="scroll-view-item result-wrap" id="safety_result">
          <view class="result-top">
            <view
              class="result-top-text result-text"
              :class="
                details.unqualifiedCount ? 'notUpToStandard' : 'upToStandard'
              "
            >
              {{
                details.unqualifiedCount
                  ? `${details.unqualifiedCount}项未达标`
                  : '全部达标'
              }}
            </view>
            <view class="result-top-text">
              检查日期：{{ details.checkDate }}
            </view>
            <view class="result-top-text">
              所属项目：{{ details.projectName }}
            </view>
            <view class="result-top-text">
              被检查科室：{{ details.inspectedDepartmentName }}
            </view>
            <view class="result-top-text">
              检查人(记录人)：{{ details.checkUserName }}
            </view>
            <view class="result-top-text">
              核查人(带队领导)：{{ details.examineUserName }}
            </view>
            <view class="result-top-text">
              其他参与人：{{ details.otherUserName }}
            </view>
          </view>
          <view class="address-list">
            <view
              class="address-list-item"
              v-for="item in details.addressList"
              :key="item.id"
            >
              <view class="address-wrap">
                <view class="address-title">
                  <view class="address-title-text">{{
                    item.checkAddress
                  }}</view>
                  <view
                    class="notUpToStandard"
                    style="font-size: 28rpx;"
                    v-if="item.unqualifiedlist.length > 0"
                    >{{ `${item.unqualifiedlist.length}项未达标` }}</view
                  >
                </view>
                <view class="address-title-note">
                  <text class="oa-icon oa-icon-didian"></text>
                  <text>{{ item.checkCoordinate }}</text>
                </view>
              </view>
              <view class="address-content-wrap">
                <view
                  class="address-content-item"
                  v-for="(row, index) in item.startCheckDetails"
                  :key="row.id"
                >
                  <view class="content-text address-content-title">
                    <text
                      class="content-result-text"
                      :class="
                        row.checkResult == 0
                          ? 'upToStandardBg'
                          : 'notUpToStandardBg'
                      "
                      >{{ row.checkResult == 0 ? '达标' : '不达标' }}</text
                    >
                    {{ `${index + 1}、${row.checkContent}` }}
                  </view>
                  <view
                    class="content-text"
                    v-if="row.imgList.length > 0 || row.fileList.length > 0"
                  >
                    <img-list v-if="row.imgList && row.imgList.length > 0">
                      <img-list-item
                        v-for="one in row.imgList"
                        :key="one.id"
                        :imgSrc="`${$config.BASE_HOST}${one.realPath}`"
                        @click="downloadFile(one)"
                      />
                    </img-list>
                    <uni-list v-if="row.fileList && row.fileList.length > 0">
                      <uni-list-item
                        v-for="one in row.fileList"
                        :key="one.id"
                        :title="one.originalName"
                        titleStyle="font-size: 28rpx;color: #005BAC"
                        listItemStyle="padding-left: 0;padding-right: 0;"
                        :showArrow="false"
                        @click="downloadFile(one)"
                      />
                    </uni-list>
                  </view>
                  <view class="content-text" v-if="row.checkResult == 1">
                    说明：{{ row.remark }}
                  </view>
                  <view class="content-text" v-if="row.checkResult == 1">
                    交办人：{{ row.assignUserName }}
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view
          class="scroll-view-item log-wrap"
          id="safety_log"
          v-if="status != 0"
        >
          <view class="log-title">操作日志</view>
          <logs-list>
            <logs-list-item
              v-for="item in logs"
              :key="item.id"
              :node="item.updateDate | formatTime"
              :content="item.operationContent"
            ></logs-list-item>
          </logs-list>
        </view>
      </scroll-view>
    </view>
    <view class="btn-wrap">
      <button class="btn-item" @click="deleteSafety" v-if="showDeleteBtn">
        删除
      </button>
      <button
        class="btn-item theme-color"
        @click="editSafety"
        v-if="showEditBtn"
      >
        编辑
      </button>
      <button
        class="btn-item theme-color"
        @click="noPassSafety"
        v-if="showOperationBtn"
      >
        不通过
      </button>
      <button
        class="btn-item theme-color"
        @click="passSafety"
        v-if="showOperationBtn"
      >
        通过
      </button>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import Base64 from '@/common/js/base64.min.js';
export default {
  data() {
    return {
      fromPage: '',
      title: '',
      tabBars: [
        {
          name: '检查结果',
          viewId: 'result'
        },
        {
          name: '操作日志',
          viewId: 'log'
        }
      ],
      tabIndex: 0,
      pickIndex: 0,
      scrollStatus: true, //点击状态，是否能点击
      scrollViewId: '',
      old: {
        scrollTop: 0
      },
      scrollTop: 0,
      nodeInfoList: [],
      details: {},
      status: '',
      logs: [],
      isSubmit: false
    };
  },
  computed: {
    ...mapState(['empcode']),
    showContent() {
      return Object.keys(this.details).length == 0 ? false : true;
    },
    showDeleteBtn() {
      return this.status == 0;
    },
    showEditBtn() {
      return (
        this.status == 0 ||
        (this.status == 2 &&
          (this.details.checkUser == this.empcode ||
            this.details.isPermissions))
      );
    },
    showOperationBtn() {
      let examineUser = this.details.examineUser.split(',');
      return (
        this.status == 1 &&
        (examineUser.some(item => item == this.empcode) ||
          this.details.isPermissions)
      );
    }
  },
  async onLoad(opt) {
    this.fromPage = opt.index;
    await this.getRouteDetail(opt.id);
    this.initScroll();
  },
  methods: {
    initScroll() {
      const query = uni.createSelectorQuery().in(this);
      query
        .selectAll('#safetyCheckResultDetails .scroll-view-item')
        .boundingClientRect(data => {
          this.nodeInfoList = data;
        })
        .exec();
    },
    //tab点击事件
    ontabtap(e) {
      if (!this.scrollStatus) return;
      // 200毫秒才能执行下次点击
      if (this.scrollStatus) {
        this.scrollStatus = false;
        let data = e.currentTarget.dataset;
        // 解决clickedNavIndex相同触发更新失败
        if (this.tabIndex == data.current) {
          this.scrollViewId = 'approval';
        }
        this.scrollViewId = data.viewId;
        this.tabIndex = data.current;
        setTimeout(() => {
          // 200毫秒才能执行下次点击
          this.scrollStatus = true;
        }, 200);
      }
    },
    scroll(e) {
      if (!this.scrollStatus) return;
      let scrollTop = (this.old.scrollTop = e.detail.scrollTop),
        current = this.nodeInfoList
          .map((item, index) => ({ index, ...item }))
          .filter(item => item.top <= scrollTop + 84)
          .sort((a, b) => b.top - a.top)[0];
      this.tabIndex = current.index;
    },
    //点击跳到指定位置事件
    selectSafety(index) {
      if (!this.scrollStatus) return;
      // 200毫秒才能执行下次点击
      if (this.scrollStatus) {
        this.scrollStatus = false;
        // 解决clickedNavIndex相同触发更新失败
        if (this.tabIndex == index) {
          this.scrollViewId = 'safety';
        }
        this.$nextTick(() => {
          this.scrollViewId = `safety_${this.tabBars[index]['viewId']}`;
          this.tabIndex = this.pickIndex = index;
        });
        setTimeout(() => {
          // 200毫秒才能执行下次点击
          this.scrollStatus = true;
        }, 200);
      }
    },
    async getRouteDetail(id) {
      await this.ajax.getRouteDetail(id).then(async res => {
        let datas = res.object;
        this.status = datas.status;
        this.title = datas.routeName;
        datas.addressList = await Promise.all(
          datas.addressList.map(async item => {
            item.unqualifiedlist = item.startCheckDetails.filter(
              i => i.checkResult == 1
            );
            item.startCheckDetails = await Promise.all(
              item.startCheckDetails.map(async i => {
                let list = await this.getFileList(i.businessId),
                  personList = [];
                i.imgList = list.img;
                i.fileList = list.file;
                if (i.assignUser) {
                  personList.push({
                    id: i.assignUser,
                    name: i.assignUserName,
                    empFirstName: i.assignUserName.substring(
                      i.assignUserName.length - 2
                    ),
                    choose: true
                  });
                }
                i.personList = personList;
                return i;
              })
            );
            return item;
          })
        );
        this.details = datas;
        if (datas.status != 0) this.getLogsList(id);
      });
    },
    async getLogsList(id) {
      await this.ajax.getCheckOperationLogsByCheckId(id).then(async res => {
        this.logs = res.object;
      });
    },
    //获取文件列表
    async getFileList(businessId) {
      let listObj = { file: [], img: [] };
      await this.ajax
        .getFileAttachmentByBusinessId({
          businessId: businessId
        })
        .then(res => {
          res.object.forEach(i => {
            if (/gif|jpg|jpeg|png|bmp/i.test(i.fileExtension.toLowerCase()))
              listObj.img.push(i);
            else listObj.file.push(i);
          });
        });
      return listObj;
    },
    //查看附件详情
    downloadFile(item) {
      let filePath = `${
        this.$config.ENABLE_FILE_PREVIEW
          ? this.$config.DOCUMENT_BASE_HOST
          : this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/downloadFile/${item.id}?fullfilename=${
        item.originalName
      }&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      } else {
        this.$downloadFile.downloadFile(filePath);
      }
    },
    editSafety() {
      uni.navigateTo({
        url: `/pages/safetySupervision/select-device?type=update&index=${this.fromPage}&id=${this.details.id}`
      });
    },
    deleteSafety() {
      this.$common
        .confirm('确定删除吗？', '提示', '确定', true, '取消')
        .then(async e => {
          await this.ajax.deleteSafety(this.details.id).then(res => {
            uni.redirectTo({
              url: `/pages/safetySupervision/safety-check-list?index=${this.fromPage}`
            });
          });
        });
    },
    noPassSafety() {
      uni.setStorageSync('checkDetails', JSON.stringify(this.details));
      uni.navigateTo({
        url: `/pages/safetySupervision/safety-check-review?type=1&index=${this.fromPage}`
      });
    },
    async passSafety() {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;
      this.details.checkResult = 0;
      this.details.remark = '';
      await this.ajax
        .confirmSafety(this.details)
        .then(res => {
          uni.redirectTo({
            url: `/pages/safetySupervision/safety-check-list?index=${this.fromPage}`
          });
        })
        .catch(e => {
          this.isSubmit = false;
        });
    },
    //返回
    returnBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.swiper-head {
  position: relative;
  width: 100%;
  height: 40px;
  background-color: #ffffff;
  flex-direction: row;
  box-sizing: border-box;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
  &::before,
  &::after {
    position: absolute;
    z-index: 10;
    right: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #eeeeee;
  }
  &::before {
    top: 0;
  }
  &::after {
    bottom: 0;
  }
}
.uni-tab-item {
  /* #ifndef APP-PLUS */
  display: inline-block;
  /* #endif */
  flex-wrap: nowrap;
  width: 50%;
  padding-left: 34rpx;
  padding-right: 34rpx;
  box-sizing: border-box;
  text-align: center;
}
.uni-tab-item-title {
  color: #555;
  font-size: 30rpx;
  height: 40px;
  line-height: 38px;
  flex-wrap: nowrap;
  display: block;
  box-sizing: border-box;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
}
.uni-tab-item-title-active {
  color: $theme-color;
  border-bottom: 2px solid $theme-color;
}
.content-wrap {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.result-wrap {
  margin-bottom: 20rpx;
}
.result-top {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
}
.result-top-text {
  color: #333333;
  font-size: 28rpx;
}
.result-text {
  font-weight: bold;
}
.address-list-item {
  margin-top: 20rpx;
  background-color: #ffffff;
}
.address-wrap {
  padding: 20rpx 30rpx;
  position: relative;
}
.address-title {
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
}
.address-title-text {
  flex: 1;
}
.address-title-note {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}
.address-title-note .oa-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
  color: $theme-color;
}
.address-content-wrap,
.content-text {
  position: relative;
}
.address-content-wrap::before {
  top: 0;
}
.content-text::after {
  bottom: 0;
}
.address-content-wrap::before,
.content-text::after {
  content: '';
  position: absolute;
  right: 0;
  left: 0;
  transform: scaleY(-0.5);
  height: 1px;
  background-color: #eee;
}
.content-text {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
}
.content-result-text {
  font-size: 24rpx;
  width: 92rpx;
  text-align: center;
  display: inline-block;
  border-radius: 90rpx;
  margin-right: 10rpx;
  padding: 4rpx 0;
}
.address-content-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}
.btn-wrap {
  z-index: 9;
  background-color: #ffffff;
  box-shadow: 0 1px 6px #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}
.theme-color {
  color: $theme-color !important;
}
.btn-item {
  height: 90rpx;
  flex: 1;
  box-sizing: border-box;
  text-align: center;
  font-size: 32rpx;
  position: relative;
  background-color: transparent;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  &::after {
    border: 0;
    top: 20rpx;
    bottom: 20rpx;
    right: 0;
    left: unset;
    transform: scaleX(-0.5);
    width: 1px;
    height: unset;
    background-color: #eee;
  }
  &:last-child::after {
    width: 0;
  }
}
.upToStandardBg {
  color: #ffffff;
  background-color: $uni-color-success;
}
.notUpToStandardBg {
  color: #ffffff;
  background-color: $uni-color-error;
}
.upToStandard {
  color: $uni-color-success;
}
.notUpToStandard {
  color: $uni-color-error;
}
.log-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.log-wrap {
  padding: 20rpx 30rpx 20rpx 40rpx;
  background-color: #ffffff;
}
</style>
