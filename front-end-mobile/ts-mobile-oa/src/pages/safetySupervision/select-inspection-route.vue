<template>
  <view class="ts-content">
    <page-head title="选择检查路线" @clickLeft="returnBack" />
    <uni-search-bar
      radius="100"
      bgColor="#FFFFFF"
      searchBgColor="#eeeeee"
      borderColor="transparent"
      @confirm="search"
      placeholder="请输入路线名称"
    ></uni-search-bar>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        @getDatas="getRouteList"
        @setDatas="setRouteList"
        @datasInit="datasInit"
      >
        <uni-collapse>
          <uni-collapse-item
            v-for="(list, key) in routeList"
            :key="key"
            :title="key"
            :collapse-text-style="collapseTextStyle"
            :open="true"
          >
            <view
              class="contact_item collapse_contact_item"
              v-for="row in list"
              :key="row.id"
              @tap="goDetailPage(row.id, row.routeName)"
            >
              <text class="contact_item_text">{{ row.routeName }}</text>
            </view>
          </uni-collapse-item>
        </uni-collapse>
      </mescroll>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      routeList: [], // 所属项目 选项列表
      routeName: '',

      collapseTextStyle: {
        color: '#333',
        'font-weight': 'bold'
      }
    };
  },
  methods: {
    search({ value = '' }) {
      this.routeName = value;
      this.$refs.mescroll.downCallback();
    },
    //获取路线列表
    async getRouteList(page, successCallback, errorCallback, keywords) {
      let _self = this;
      await _self.ajax
        .getSafetyRouteList({
          pageNo: page.num,
          pageSize: 99999,
          routeName: this.routeName
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setRouteList(row) {
      let projectNameObj = row.reduce((prev, next) => {
        let projectName = next.projectName;
        if (!prev.hasOwnProperty(projectName)) {
          prev[projectName] = [];
        }
        prev[projectName].push(next);
        return prev;
      }, {});
      this.routeList = projectNameObj;
    },
    datasInit() {
      this.routeList = [];
    },
    goDetailPage(id, routeName) {
      uni.navigateTo({
        url: `/pages/safetySupervision/select-device?type=add&id=${id}`
      });
    },
    //返回
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  .mescroll-content {
    position: absolute;
    width: 100%;
    top: 104px;
    bottom: 0;
  }
}

.contact_item {
  padding: 20rpx;
  display: flex;
  font-size: 28rpx;
  align-items: center;
  background-color: #ffffff;
  position: relative;
  &::after {
    position: absolute;
    bottom: 0;
    left: 20rpx;
    right: 0;
    height: 1px;
    transform: scaleY(0.5);
    background-color: #c8c7cc;
    content: '';
  }
  &:last-child::after {
    height: 0;
  }
  .contact_item_text {
    font-size: 28rpx;
    color: #333333;
  }
}
.collapse_contact_item {
  padding-left: 60rpx;
  &::after {
    left: 60rpx;
  }
}
</style>
