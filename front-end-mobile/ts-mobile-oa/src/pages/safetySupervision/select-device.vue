<template>
  <view class="ts-content">
    <page-head :title="title" @clickLeft="returnBack" />
    <view class="uni-input-group">
      <view class="uni-input-row">
        <view class="uni-label">
          <text class="required_red oa-icon oa-icon-asterisks"></text>
          检查日期
        </view>
        <view class="input_container" @tap="showPicker('datePicker')">
          <view class="uni-input" :class="{ slectedInp: datas.checkDate }">
            {{ datas.checkDate ? datas.checkDate : '请选择检查日期' }}
          </view>
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
      <view class="uni-input-row">
        <view class="uni-label">
          <text class="required_red oa-icon oa-icon-asterisks"></text>
          被检查科室
        </view>
        <view
          class="input_container"
          @tap="showDeptSelect('inspectedDepartment', 'radio')"
        >
          <view
            class="uni-input"
            :class="{ slectedInp: datas.inspectedDepartmentName }"
          >
            {{
              datas.inspectedDepartmentId
                ? datas.inspectedDepartmentName
                : '请选择被检查科室'
            }}
          </view>
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          ></uni-icons>
        </view>
      </view>
      <view class="uni-input-row">
        <view class="uni-label">核查人(带队领导)</view>
        <view
          class="input_container"
          @tap="showPersonSelet('examineUser', 'radio')"
        >
          <view class="uni-input" :class="{ slectedInp: datas.examineUser }">
            {{
              datas.examineUser
                ? datas.examineUserName
                : '请选择核查人(带队领导)'
            }}
          </view>
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          ></uni-icons>
        </view>
      </view>
      <view class="uni-input-row">
        <view class="uni-label">其他参与人</view>
        <view
          class="input_container"
          @tap="showPersonSelet('otherUser', 'checkBox')"
        >
          <view class="uni-input" :class="{ slectedInp: otherUserStr }">
            {{ otherUserStr ? otherUserStr : '请选择其他参与人' }}
          </view>
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          ></uni-icons>
        </view>
      </view>
    </view>
    <view class="address-contant">
      <view class="uni-list">
        <view
          class="uni-list-item"
          v-for="(item, index) in datas.addressList"
          :key="index"
          @click="goDetailPage(index)"
        >
          <view class="uni-list-item__content-title__container">
            <text class="uni-list-item__content-title">{{
              item.checkAddress
            }}</text>
            <text
              v-if="item.startCheckDetails.length"
              class="uni-list-item__content-number"
            >
              共{{ item.startCheckDetails.length }}项</text
            >
          </view>
          <view class="uni-list-item__content-note__container">
            <view class="uni-list-item__content-note">
              <text class="oa-icon oa-icon-didian"></text>
              <text class="uni-list-item__content-note-text">{{
                item.checkCoordinate
              }}</text>
            </view>
            <text v-if="item.checkStatus" class="uni-list-item__note-extra"
              >已检查</text
            >
          </view>
        </view>
      </view>
    </view>
    <view class="btn_wrap">
      <button class="btn_item" @tap="returnBack">
        取消
      </button>
      <button class="btn_item theme-color" @tap="temporarySave">
        暂存
      </button>
      <button class="btn_item theme-color" @tap="submit">
        提交
      </button>
    </view>
    <date-picker
      startDate="2020-01-01"
      endDate="2100-12-31"
      mode="date"
      fields="day"
      :current="true"
      @confirm="onConfirm"
      @cancel="onCancel"
      ref="datePicker"
    ></date-picker>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import datePicker from '@/components/picker/date-picker.vue';
export default {
  components: {
    datePicker
  },
  data() {
    return {
      isCommitted: false,
      fromPage: '',
      title: '',
      datas: {
        checkDate: '',
        checkUser: '',
        checkUserName: '',
        examineUser: '',
        examineUserName: '',
        otherUser: '',
        otherUserName: '',
        inspectedDepartmentId: '',
        inspectedDepartmentName: '',
        routeId: '',
        status: 0 //检查单状态(0:待提交 1:待确认 2 不通过 3 整改中 4 已完结 )
      },
      otherUserStr: '',
      personlist: {
        examineUser: [],
        otherUser: []
      },
      deptList: {
        inspectedDepartment: []
      },
      isSubmit: false
    };
  },
  async onLoad(opt) {
    this.fromPage = opt.index || '';
    if (opt.type == 'add') {
      this.datas.checkDate = this.$common.getDate().timeStr;
      this.datas.routeId = opt.id;
      this.datas.checkUser = this.empcode;
      this.datas.checkUserName = this.username;
      await this.getRouteList(opt.id);
    } else if (opt.type == 'update') {
      await this.getRouteDetail(opt.id);
    }
  },
  computed: {
    ...mapState(['token', 'empcode', 'username'])
  },
  watch: {
    'datas.examineUser': {
      handler(newValue, oldValue) {
        if (newValue) {
          let userIdArr = newValue.split(','),
            userNameArr = this.datas.examineUserName.split(',');
          this.personlist.examineUser = userIdArr.map((item, index) => {
            return {
              id: item,
              name: userNameArr[index],
              empFirstName: userNameArr[index].substring(
                userNameArr[index].length - 2
              ),
              empHeadImg: '',
              choose: true
            };
          });
        } else this.personlist.examineUser = [];
      },
      deep: true
    },
    'datas.otherUser': {
      handler(newValue, oldValue) {
        if (newValue) {
          let userIdArr = newValue.split(','),
            userNameArr = this.datas.otherUserName.split(',');
          this.personlist.otherUser = userIdArr.map((item, index) => {
            return {
              id: item,
              name: userNameArr[index],
              empFirstName: userNameArr[index].substring(
                userNameArr[index].length - 2
              ),
              empHeadImg: '',
              choose: true
            };
          });
        } else this.personlist.otherUser = [];
      },
      deep: true
    },
    'datas.otherUserName': {
      handler(newValue, oldValue) {
        let userNameArr = newValue.split(','),
          userNameEllipsisArr = [];
        for (var i = 0; i < userNameArr.length; i++) {
          if (i >= 3) break;
          userNameEllipsisArr.push(userNameArr[i]);
        }
        this.otherUserStr = userNameEllipsisArr.join('、');
        if (userNameArr.length > 3) {
          this.otherUserStr += `等${userNameArr.length}人`;
        }
      },
      deep: true
    },
    'datas.inspectedDepartmentId': {
      handler(newValue, oldValue) {
        if (newValue) {
          let deptIdArr = newValue.split(','),
            deptNameArr = this.datas.inspectedDepartmentName.split(',');
          this.deptList.inspectedDepartment = deptIdArr.map((item, index) => {
            return {
              id: item,
              name: deptNameArr[index]
            };
          });
        } else this.deptList.inspectedDepartment = [];
      },
      deep: true
    }
  },
  methods: {
    //显示时间选择
    showPicker(type) {
      this.$refs[type].show();
    },
    //时间确定
    onConfirm(res) {
      this.datas.checkDate = res.value;
    },
    //时间取消
    onCancel() {},
    //跳转至人员选择
    showPersonSelet(param, type) {
      let _self = this;
      //监听事件
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', function(data) {
        //_self.personlist[param] = data;
        let agentNameArr = [],
          agentIdArr = [];
        data.forEach((item, index) => {
          agentNameArr.push(item.name);
          agentIdArr.push(item.id);
        });
        _self.datas[`${param}Name`] = agentNameArr.join(',');
        _self.datas[param] = agentIdArr.join(',');
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      uni.setStorageSync(
        'person_list',
        JSON.stringify(_self.personlist[param])
      );
      uni.navigateTo({
        url: `/pages/selectPerson/select-person?checkType=${type}`
      });
    },
    //选择部门
    showDeptSelect(param, type) {
      let _self = this;
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('deptlist', function(res) {
        let arrName = [],
          arrId = [];
        res.forEach(item => {
          arrName.push(item.name);
          arrId.push(item.id);
        });
        _self.datas[`${param}Name`] = arrName.join(',');
        _self.datas[`${param}Id`] = arrId.join(',');
        uni.removeStorageSync('dept_list');
        //清除监听，不清除会消耗资源
        uni.$off('deptlist');
      });
      uni.setStorageSync('dept_list', JSON.stringify(_self.deptList[param]));
      uni.navigateTo({
        url: `/pages/selectDept/select-dept?checkType=${type}`
      });
    },
    //获取地点
    async getRouteList(id) {
      let _self = this;
      await _self.ajax.getRouteAdressList(id).then(res => {
        this.title = res.object.routeName;
        let list = res.object.checkRouteDetails.map(i => {
          let contents = i.checkRouteContents.map(r => {
            return {
              checkContent: r.checkContent,
              checkContentId: r.id,
              checkResult: 0,
              assignUser: '',
              assignUserName: '',
              remark: '',
              personList: [],
              businessId: _self.$common.guid(),
              routeDetailId: r.routeDetailId,
              routeId: r.routeId
            };
          });
          return {
            addressId: i.id,
            checkAddress: i.checkAddress,
            checkCoordinate: i.checkCoordinate,
            routeId: i.routeId,
            checkStatus: 0,
            startCheckDetails: contents
          };
        });
        _self.$set(_self.datas, 'addressList', list);
      });
    },
    async getRouteDetail(id) {
      let _self = this;
      await _self.ajax.getRouteDetail(id).then(async res => {
        let datas = res.object;
        _self.title = datas.routeName;
        _self.datas.id = datas.id;
        _self.datas.checkDate = datas.checkDate;
        _self.datas.routeId = datas.routeId;
        _self.datas.checkUser = datas.checkUser;
        _self.datas.checkUserName = datas.checkUserName;
        _self.datas.examineUserName = datas.examineUserName;
        _self.datas.examineUser = datas.examineUser;
        _self.datas.otherUserName = datas.otherUserName;
        _self.datas.otherUser = datas.otherUser;
        _self.datas.inspectedDepartmentName = datas.inspectedDepartmentName;
        _self.datas.inspectedDepartmentId = datas.inspectedDepartmentId;
        _self.datas.addressList = await Promise.all(
          datas.addressList.map(async item => {
            item.startCheckDetails = await Promise.all(
              item.startCheckDetails.map(async i => {
                let list = await _self.getFileList(i.businessId),
                  personList = [];
                i.imgList = list.img;
                i.fileList = list.file;
                if (i.assignUser) {
                  personList.push({
                    id: i.assignUser,
                    name: i.assignUserName,
                    empFirstName: i.assignUserName.substring(
                      i.assignUserName.length - 2
                    ),
                    choose: true
                  });
                }
                i.personList = personList;
                return i;
              })
            );
            return item;
          })
        );
        _self.$forceUpdate();
      });
    },
    //获取文件列表
    async getFileList(businessId) {
      let _self = this,
        listObj = { file: [], img: [] };
      await _self.ajax
        .getFileAttachmentByBusinessId({
          businessId: businessId
        })
        .then(res => {
          res.object.forEach(i => {
            if (
              i.fileExtension == 'jpg' ||
              i.fileExtension == 'jpeg' ||
              i.fileExtension == 'png' ||
              i.fileExtension == 'gif' ||
              i.fileExtension == 'img'
            )
              listObj.img.push(i);
            else listObj.file.push(i);
          });
        });
      return listObj;
    },
    goDetailPage(i) {
      let _self = this;
      uni.$on('safetyDetails', function(data) {
        if (data) {
          _self.$set(_self.datas['addressList'][i], 'checkStatus', 1);
          _self.$set(_self.datas['addressList'][i], 'startCheckDetails', data);
        }
        _self.$forceUpdate();
        uni.removeStorageSync('routeDetails');
        //清除监听，不清除会消耗资源
        uni.$off('safetyDetails');
      });
      uni.setStorageSync('routeDetails', JSON.stringify(_self.datas));
      uni.navigateTo({
        url: `init-safety-supervision?index=${i}`
      });
    },
    async saveSafetyContent(type, successCallback, errorCallback) {
      let _self = this;
      await _self.ajax
        .saveSafetyContent(type, this.datas)
        .then(res => {
          successCallback(res.object);
        })
        .catch(res => {
          errorCallback();
        });
    },
    //暂存
    async temporarySave() {
      let _self = this;
      if (_self.isCommitted) return false;
      else _self.isCommitted = true;
      _self.$set(_self.datas, 'status', 0);
      if (!_self.datas.inspectedDepartmentId) {
        _self.$common.toast('请选择被检查科室');
        _self.isCommitted = false;
        return false;
      }
      if (_self.datas.id) {
        await _self.saveSafetyContent(
          'update',
          () => {
            _self.$common.toast('暂存成功');
          },
          () => {
            _self.isCommitted = false;
          }
        );
      } else {
        await _self.saveSafetyContent(
          'save',
          res => {
            _self.datas.id = res;
            _self.$common.toast('暂存成功');
          },
          () => {
            _self.isCommitted = false;
          }
        );
      }
    },
    //提交
    async submit() {
      let _self = this;
      if (_self.isCommitted) return false;
      else _self.isCommitted = true;
      let notChecked = _self.datas.addressList.filter(
        item => item.checkStatus != 1
      );
      if (!_self.datas.inspectedDepartmentId) {
        _self.$common.toast('请选择被检查科室');
        _self.isCommitted = false;
        return false;
      }
      if (notChecked.length > 0) {
        _self.$common.toast('存在未检查的地点，请补充完整信息');
        _self.isCommitted = false;
        return false;
      }
      _self.$set(_self.datas, 'status', 1);
      await _self.saveSafetyContent(
        _self.datas.id ? 'update' : 'save',
        () => {
          _self.$common.toast('提交成功');
          if (_self.fromPage == '') {
            this.$parentTypeFun({
              type: 'jumpPage',
              path: '/workbench'
            });
          } else {
            uni.redirectTo({
              url: `safety-check-list?index=${_self.fromPage}`
            });
          }
        },
        () => {
          _self.isCommitted = false;
        }
      );
    },
    //返回
    returnBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
$list-item-pd: $uni-spacing-col-base $uni-spacing-row-lg;
.ts-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
}
.uni-input-group {
  width: 100%;
}
.input_container {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex: 1;
  overflow: hidden;
  flex-direction: row;
  align-items: center;
  color: $uni-text-color-placeholder;
}
.slectedInp {
  color: $uni-text-color;
}
.uni-input {
  text-align: right;
  font-size: $uni-font-size-lg;
}
.address-contant {
  flex: 1;
  margin-top: $uni-spacing-col-base;
}
.uni-list-item {
  padding: $list-item-pd;
  position: relative;
}
.uni-list-item::after {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 30rpx;
  height: 1px;
  content: '';
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: #eeeeee;
}
.uni-list-item__content {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex: 1;
  overflow: hidden;
  flex-direction: column;
  align-items: center;
  color: $uni-text-color;
}
.uni-list-item__content-title__container {
  line-height: 1.3;
}
.uni-list-item__content-title {
  font-size: $uni-font-size-lg;
  color: $uni-text-color;
  overflow: hidden;
}
.uni-list-item__content-number {
  font-size: $uni-font-size-base;
  color: $uni-text-color;
  overflow: hidden;
}
.uni-list-item__content-title__container,
.uni-list-item__content-note__container {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  overflow: hidden;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  color: $uni-text-color;
}
.uni-list-item__content-note {
  color: $uni-text-color-subtitle;
  font-size: $uni-font-size-base;
  overflow: hidden;
}
.uni-list-item__content-note-text {
  margin-left: $uni-spacing-row-sm;
}
.uni-list-item__note-extra {
  color: $uni-color-success;
  font-size: $uni-font-size-base;
}
.btn_wrap {
  background-color: #ffffff;
  box-shadow: 0 1px 6px #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}
.theme-color {
  color: $theme-color !important;
}
.btn_item {
  height: 90rpx;
  flex: 1;
  box-sizing: border-box;
  text-align: center;
  font-size: 32rpx;
  position: relative;
  background-color: transparent;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  &::after {
    border: 0;
    top: 20rpx;
    bottom: 20rpx;
    right: 0;
    left: unset;
    transform: scaleX(-0.5);
    width: 1px;
    height: unset;
    background-color: #ccc;
  }
  &:last-child::after {
    width: 0;
  }
}
.required_red {
  color: #f00;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 2px;
  font-size: 12px;
}
</style>
