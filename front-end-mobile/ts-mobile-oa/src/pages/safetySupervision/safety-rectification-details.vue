<template>
  <view class="ts-content" v-if="showContent">
    <page-head :title="details.checkAddress" @clickLeft="returnBack" />
    <scroll-view class="swiper-head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tabItem, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        :data-view-id="`safety_${tabItem.viewId}`"
        :data-current="index"
        @click="ontabtap"
      >
        <text
          class="uni-tab-item-title"
          :class="tabIndex == index ? 'uni-tab-item-title-active' : ''"
          >{{ tabItem.name }}</text
        >
      </view>
    </scroll-view>
    <view class="content-wrap">
      <scroll-view
        id="safetyResultDetails"
        style="height: 100%;"
        scroll-y="true"
        :scroll-into-view="scrollViewId"
        :scroll-with-animation="true"
        @scroll="scroll"
      >
        <view class="scroll-view-item" id="safety_result">
          <view class="result-wrap">
            <view class="result-item">
              检查日期：{{ details.createDate | formatTime }}
            </view>
            <view class="result-item">
              被检查科室：{{ details.inspectedDepartmentName }}
            </view>
            <view class="result-item">
              检查人（记录人）：{{ details.checkUserName }}
            </view>
            <view class="result-item">
              核查人员（带队领导）：{{ details.examineUserName }}
            </view>
            <view class="result-item">
              其他参与人：{{ details.otherUserName }}
            </view>
          </view>
          <view class="result-wrap address-contant">
            <view class="result-item" style="font-weight: bold;">{{
              details.checkContent
            }}</view>
            <view
              class="result-item"
              v-if="
                (details.imgList && details.imgList.length > 0) ||
                  (details.fileList && details.fileList.length > 0)
              "
            >
              <img-list v-if="details.imgList.length > 0">
                <img-list-item
                  v-for="one in details.imgList"
                  :key="one.id"
                  :imgSrc="`${$config.BASE_HOST}${one.realPath}`"
                  @click="downloadFile(one)"
                />
              </img-list>
              <uni-list v-if="details.fileList.length > 0">
                <uni-list-item
                  v-for="one in details.fileList"
                  :key="one.id"
                  :title="one.originalName"
                  titleStyle="font-size: 28rpx;color: #005BAC"
                  listItemStyle="padding-left: 0;padding-right: 0;"
                  :showArrow="false"
                  @click="downloadFile(one)"
                />
              </uni-list>
            </view>
            <view class="result-item"
              >说明：{{ details.unqualifiedRemark }}</view
            >
            <view class="result-item"
              >交办人：{{ details.assignUserName }}</view
            >
          </view>
        </view>
        <view class="scroll-view-item log-wrap" id="safety_log">
          <view class="log-title">操作日志</view>
          <logs-list>
            <logs-list-item
              v-for="item in details.logs"
              :key="item.id"
              :node="item.updateDate | formatTime"
              :content="
                `${item.operationOrgName}-${item.operationUserName}${item.operationContent}`
              "
            >
              <view
                v-if="
                  (item.imgList && item.imgList.length > 0) ||
                    (item.fileList && item.fileList.length > 0)
                "
              >
                <img-list v-if="item.imgList.length > 0">
                  <img-list-item
                    v-for="one in item.imgList"
                    :key="one.id"
                    :imgSrc="`${$config.BASE_HOST}${one.realPath}`"
                    @click="downloadFile(one)"
                  />
                </img-list>
                <uni-list v-if="item.fileList.length > 0">
                  <uni-list-item
                    v-for="one in item.fileList"
                    :key="one.id"
                    :title="one.originalName"
                    titleStyle="font-size: 28rpx;color: #005BAC"
                    listItemStyle="padding-left: 0;padding-right: 0;"
                    :showArrow="false"
                    @click="downloadFile(one)"
                  />
                </uni-list>
              </view>
            </logs-list-item>
          </logs-list>
        </view>
      </scroll-view>
    </view>
    <view class="btn-wrap">
      <button
        class="btn-item theme-color"
        @click="submit('complete')"
        v-if="showCompleteBtn"
      >
        完成整改
      </button>
      <button
        class="btn-item theme-color"
        @click="submit('notPass')"
        v-if="showOperationBtn"
      >
        不通过
      </button>
      <button
        class="btn-item theme-color"
        @click="pass"
        v-if="showOperationBtn"
      >
        通过
      </button>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import Base64 from '@/common/js/base64.min.js';
export default {
  data() {
    return {
      fromPage: '',
      tabBars: [
        {
          name: '检查结果',
          viewId: 'result'
        },
        {
          name: '操作日志',
          viewId: 'log'
        }
      ],
      tabIndex: 0,
      pickIndex: 0,
      scrollStatus: true, //点击状态，是否能点击
      scrollViewId: '',
      old: {
        scrollTop: 0
      },
      scrollTop: 0,
      nodeInfoList: [],
      details: {},
      isSubmit: false
    };
  },
  computed: {
    ...mapState(['empcode']),
    showContent() {
      return Object.keys(this.details).length == 0 ? false : true;
    },
    showCompleteBtn() {
      return (
        this.details.status == 0 &&
        (this.details.assignUserCode == this.empcode ||
          this.details.isPermissions)
      );
    },
    showOperationBtn() {
      return (
        this.details.status == 1 &&
        (this.details.checkUserCode == this.empcode ||
          this.details.isPermissions)
      );
    }
  },
  async onLoad(opt) {
    this.fromPage = opt.index || '';
    await this.getRectificationDetail(opt.id);
    this.initScroll();
  },
  methods: {
    initScroll() {
      const query = uni.createSelectorQuery().in(this);
      query
        .selectAll('#safetyResultDetails .scroll-view-item')
        .boundingClientRect(data => {
          this.nodeInfoList = data;
        })
        .exec();
    },
    //tab点击事件
    ontabtap(e) {
      if (!this.scrollStatus) return;
      // 200毫秒才能执行下次点击
      if (this.scrollStatus) {
        this.scrollStatus = false;
        let data = e.currentTarget.dataset;
        // 解决clickedNavIndex相同触发更新失败
        if (this.tabIndex == data.current) {
          this.scrollViewId = 'approval';
        }
        this.scrollViewId = data.viewId;
        this.tabIndex = data.current;
        setTimeout(() => {
          // 200毫秒才能执行下次点击
          this.scrollStatus = true;
        }, 200);
      }
    },
    scroll(e) {
      if (!this.scrollStatus) return;
      let scrollTop = (this.old.scrollTop = e.detail.scrollTop),
        current = this.nodeInfoList
          .map((item, index) => ({ index, ...item }))
          .filter(item => item.top <= scrollTop + 84)
          .sort((a, b) => b.top - a.top)[0];
      this.tabIndex = current.index;
    },
    //点击跳到指定位置事件
    selectSafety(index) {
      if (!this.scrollStatus) return;
      // 200毫秒才能执行下次点击
      if (this.scrollStatus) {
        this.scrollStatus = false;
        // 解决clickedNavIndex相同触发更新失败
        if (this.tabIndex == index) {
          this.scrollViewId = 'safety';
        }
        this.$nextTick(() => {
          this.scrollViewId = `safety_${this.tabBars[index]['viewId']}`;
          this.tabIndex = this.pickIndex = index;
        });
        setTimeout(() => {
          // 200毫秒才能执行下次点击
          this.scrollStatus = true;
        }, 200);
      }
    },
    async getRectificationDetail(id) {
      await this.ajax.getCheckRectificationDetailById(id).then(async res => {
        this.details = res.object;
        if (this.details.checkBusinessId) {
          let list = await this.getFileList(this.details.checkBusinessId);
          this.$set(this.details, 'imgList', list.img);
          this.$set(this.details, 'fileList', list.file);
        }
        this.details.logs = await Promise.all(
          this.details.logs.map(async item => {
            if (item.businessId) {
              let list = await this.getFileList(item.businessId);
              item.imgList = list.img;
              item.fileList = list.file;
            }
            return item;
          })
        );
      });
    },
    //获取文件列表
    async getFileList(businessId) {
      let listObj = { file: [], img: [] };
      await this.ajax
        .getFileAttachmentByBusinessId({
          businessId: businessId
        })
        .then(res => {
          res.object.forEach(i => {
            if (
              /\.(gif|jpg|jpeg|png|bmp|img)$/.test(
                i.fileExtension.toLowerCase()
              )
            )
              listObj.img.push(i);
            else listObj.file.push(i);
          });
        });
      return listObj;
    },
    //查看附件详情
    downloadFile(item) {
      let filePath = `${
        this.$config.ENABLE_FILE_PREVIEW
          ? this.$config.DOCUMENT_BASE_HOST
          : this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/downloadFile/${item.id}?fullfilename=${
        item.originalName
      }&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      } else {
        this.$downloadFile.downloadFile(filePath);
      }
    },
    submit(type) {
      uni.navigateTo({
        url: `/pages/safetySupervision/safety-rectification-review?type=${type}&index=${
          this.fromPage
        }&ids=${encodeURIComponent(this.details.id)}`
      });
    },
    async pass() {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;
      await this.ajax
        .batchCheckRectification({
          ids: this.details.id,
          checkResult: '0'
        })
        .then(res => {
          uni.redirectTo({
            url: `/pages/safetySupervision/safety-rectification-list?index=${this.fromPage}`
          });
        })
        .catch(e => {
          this.isSubmit = false;
        });
    },
    //返回
    returnBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.swiper-head {
  position: relative;
  width: 100%;
  height: 40px;
  background-color: #ffffff;
  flex-direction: row;
  box-sizing: border-box;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
  &::before,
  &::after {
    position: absolute;
    z-index: 10;
    right: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #eeeeee;
  }
  &::before {
    top: 0;
  }
  &::after {
    bottom: 0;
  }
}
.uni-tab-item {
  /* #ifndef APP-PLUS */
  display: inline-block;
  /* #endif */
  flex-wrap: nowrap;
  width: 50%;
  padding-left: 34rpx;
  padding-right: 34rpx;
  box-sizing: border-box;
  text-align: center;
}
.uni-tab-item-title {
  color: #555;
  font-size: 30rpx;
  height: 40px;
  line-height: 38px;
  flex-wrap: nowrap;
  display: block;
  box-sizing: border-box;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
}
.uni-tab-item-title-active {
  color: $theme-color;
  border-bottom: 2px solid $theme-color;
}
.content-wrap {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.result-wrap {
  margin-bottom: 20rpx;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
}
.result-item {
  font-size: 28rpx;
  position: relative;
}
.address-contant {
  padding: 0;
}
.address-contant .result-item {
  padding: 20rpx 30rpx;
}
.address-contant .result-item::after {
  content: '';
  position: absolute;
  right: 0;
  left: 0;
  bottom: 0;
  transform: scaleY(-0.5);
  height: 1px;
  background-color: #eee;
}
.btn-wrap {
  z-index: 9;
  background-color: #ffffff;
  box-shadow: 0 1px 6px #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}
.theme-color {
  color: $theme-color !important;
}
.btn-item {
  height: 90rpx;
  flex: 1;
  box-sizing: border-box;
  text-align: center;
  font-size: 32rpx;
  position: relative;
  background-color: transparent;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  &::after {
    border: 0;
    top: 20rpx;
    bottom: 20rpx;
    right: 0;
    left: unset;
    transform: scaleX(-0.5);
    width: 1px;
    height: unset;
    background-color: #eee;
  }
  &:last-child::after {
    width: 0;
  }
}
.upToStandardBg {
  color: #ffffff;
  background-color: $uni-color-success;
}
.notUpToStandardBg {
  color: #ffffff;
  background-color: $uni-color-error;
}
.upToStandard {
  color: $uni-color-success;
}
.notUpToStandard {
  color: $uni-color-error;
}
.log-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.log-wrap {
  padding: 20rpx 30rpx 20rpx 40rpx;
  background-color: #ffffff;
}
</style>
