<template>
  <view class="my-train-container">
    <page-head title="我的培训" @clickLeft="returnBack"></page-head>
    <view class="outside-search-container">
      <uni-search-bar
        radius="100"
        placeholder="培训主题"
        borderColor="transparent"
        bgColor="#F4F4F4"
        cancelButton="none"
        @confirm="search"
      />
      <i @click="handleShowPopup" class="oa-icon oa-icon-shaixuan"></i>
    </view>
    <view ref="tabHeader" class="swiper_head">
      <view
        v-for="(tab, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        :data-current="index"
        @click="ontabtap(index)"
        :class="tabIndex == index ? 'uni-tab-item-title-active' : ''"
      >
        <text class="uni-tab-item-title">{{ tab.name }}</text>
        <text
          class="uni-tab-item-num"
          v-if="tab.total != null && tab.total != 0 && index < 2"
        >
          ({{ tab.total >= 100 ? '99+' : tab.total }})
        </text>
      </view>
      <view
        class="uni-tab-item score-board-line"
        @click="handleJumpToScoreBoard"
      >
        查看积分榜
      </view>
      <view ref="activeLine" class="text-line"></view>
    </view>

    <swiper
      :current="tabIndex"
      class="swiper_box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper_item"
        v-for="(item, index) in tabBars"
        :key="index"
      >
        <mescroll
          :ref="'mescroll' + (index + 1)"
          :mescrollIndex="index"
          :down="item.downOption"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view
            v-for="trainItem of item.list"
            :key="trainItem.id"
            class="train-item"
            @click="showTrainDetails(trainItem)"
          >
            <view class="train-title">
              <view>{{ trainItem.trainTopic }}</view>
              <view>
                {{ trainTypeFormatter(trainItem) }}
              </view>
            </view>
            <view
              v-if="index > 0 && trainItem.planStateText == '已结束'"
              class="status-tag"
            >
              可评论
            </view>
            <view class="train-info-line">
              <text>{{ trainItem.trainLecturer }}</text>
              <text>{{ trainItem.trainStartTime }}</text>
            </view>
            <view class="train-info-line">{{ trainItem.trainPlace }}</view>
          </view>
        </mescroll>
      </swiper-item>
    </swiper>

    <uni-popup ref="popup" type="right" zIndex="9999">
      <view class="more-search-container">
        <view class="filter-content">
          <view class="filter-title">培训类型</view>
          <view class="filter-options">
            <view
              v-for="item of trainType"
              :key="item.value"
              class="option-item"
              :class="{ active: searchForm.trainType == item.value }"
              @click="handleChooseOption('trainType', item.value)"
            >
              {{ item.label }}
            </view>
          </view>
          <view class="filter-title">培训状态</view>
          <view class="filter-options">
            <view
              v-for="item of statusList"
              :key="item.value"
              class="option-item"
              :class="{ active: searchForm.planStateText == item.value }"
              @click="handleChooseOption('planStateText', item.value)"
            >
              {{ item.label }}
            </view>
          </view>
        </view>
        <view class="action-content">
          <view @click="handleResetSearch">重置</view>
          <view class="primary" @click="handleConfirmSearch">确定</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      tabIndex: 0, //当前选中的tab索引值，从0计数
      tabBars: [
        {
          name: '待培训',
          handleStatus: 1, //办理状态
          downOption: false, //初始化时是否下拉加载
          isInit: false, //是否已初始化
          total: null,
          list: []
        },
        {
          name: '已培训',
          handleStatus: 2,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        },
        {
          name: '全部',
          handleStatus: 3,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        }
      ],

      searchForm: {},
      trainType: [],
      statusList: [
        {
          label: '待培训',
          value: '1'
        },
        {
          label: '已培训',
          value: '2'
        },
        {
          label: '未培训',
          value: '3'
        }
      ]
    };
  },
  onLoad() {
    this.$nextTick(() => {
      this.ontabtap(0);
    });
  },
  methods: {
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        this.$parentTypeFun({
          type: 'jumpPage',
          path: '/workbench'
        });
      } else {
        uni.navigateBack();
      }
    },
    handleShowPopup() {
      this.$refs.popup.open();
    },
    handleChooseOption(key, value) {
      this.$set(this.searchForm, key, value);
    },
    //tab点解切换
    async ontabtap(index) {
      let header = this.$refs.tabHeader.$el,
        activeNode = header.querySelector(
          `.uni-tab-item:nth-child(${index + 1})`
        ),
        offsetLeft = activeNode.offsetLeft,
        line = this.$refs.activeLine.$el,
        left = activeNode.offsetWidth / 2 + offsetLeft - line.offsetWidth / 2;
      line.style.transform = `translate(${left}px)`;
      await this.switchTab(Number(index));
    },
    async switchTab(index) {
      this.getTrainingType();
      if (this.tabIndex === index) {
        return;
      } else if (!this.tabBars[index]['isInit']) {
        this.tabBars[index]['isInit'] = true;
        await this.$refs[`mescroll${index + 1}`][0].downCallback();
      }
      this.tabIndex = index;
    },
    search({ value }) {
      this.searchForm.trainTopic = value;
      this.$refs[`mescroll${this.tabIndex + 1}`][0].downCallback();
    },
    ontabchange() {
      this.$nextTick(() => {
        this.$refs[`mescroll${this.tabIndex + 1}`][0].downCallback();
      });
    },
    handleResetSearch() {
      this.searchForm = {};
      this.handleConfirmSearch();
    },
    handleConfirmSearch() {
      this.$refs[`mescroll${this.tabIndex + 1}`][0].downCallback();
      this.$refs.popup.close();
    },
    async getListData(page, successCallback, errorCallback, keywords, index) {
      let data = {
          myPlan: 'Y',
          pageSize: page.size,
          pageNo: page.num,
          recordStatus: this.tabIndex + 1,
          ...this.searchForm
        },
        searchList = Object.keys(data)
          .map(key => {
            return `${key}=${data[key]}`;
          })
          .join('&');
      await this.ajax
        .getMyTrainTableDataList(searchList)
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      this.tabBars[index]['total'] = totalCount;
      let list = rows.filter(item => item.status != 0);
      this.tabBars[index]['list'] = this.tabBars[index]['list'].concat(list);
    },
    datasInit(keywords, index) {
      this.tabBars[index]['list'] = [];
    },
    /**@desc 获取培训类型 */
    getTrainingType() {
      this.ajax.getDataByDataLibrary('TRAINING_TYPE').then(res => {
        if (!res.success) {
          this.$message.error(res.message || '培训类型获取失败');
          return;
        }
        this.trainType = res.object.map(item => ({
          label: item.itemName,
          value: item.itemNameValue
        }));
      });
    },
    trainTypeFormatter(row) {
      return this.trainType.find(item => item.value == row.trainType)?.label;
    },
    showTrainDetails(row) {
      uni.navigateTo({
        url: `/pages/trainManagement/train-detail?id=` + row.trainPlanId
      });
    },
    handleJumpToScoreBoard() {
      uni.navigateTo({
        url: `/pages/trainManagement/train-score`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.my-train-container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.outside-search-container {
  padding-left: 8px;
  overflow: hidden;
  margin: 8px 0;
  background-color: #fff;
  display: flex;
  align-items: center;
  .uni-searchbar {
    flex: 1;
  }
  .oa-icon-shaixuan {
    padding-left: 8px;
    padding-right: 16px;
  }
}
.more-search-container {
  height: 100vh;
  width: 260px;
  max-width: 80vw;
  background-color: #fff;
  padding: 40px 16px;
  display: flex;
  flex-direction: column;
  .filter-content {
    flex: 1;
  }
  .filter-title {
    font-size: 16px;
    color: #333;
    font-weight: normal;
    margin-bottom: 8px;
  }
  .filter-options {
    display: grid;
    grid-row-gap: 8px;
    grid-column-gap: 24px;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(auto-fill, 24px);
    margin-bottom: 16px;
  }
  .option-item {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #999;
    border: 1px solid #eee;
    border-radius: 2px;
    &.active {
      border-color: $theme-color;
      color: $theme-color;
    }
  }
  .action-content {
    display: flex;
    justify-content: center;
    view + view {
      margin-left: 16px;
    }
    view {
      width: 180px;
      border: 1px solid #eee;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 38px;
      &.primary {
        color: #fff;
        background-color: $theme-color;
        border-color: $theme-color;
      }
    }
  }
}
.swiper_head {
  display: flex;
  background-color: #ffffff;
  overflow: hidden;
  line-height: 0;
  position: relative;
  .uni-tab-item {
    display: inline-block;
    flex-wrap: nowrap;
    flex: 1;
    height: 42px;
    line-height: 42px;
    font-size: 14px;
    box-sizing: border-box;
    text-align: center;
    .uni-tab-item-title,
    .uni-tab-item-num {
      color: #666;
      height: 100%;
      font-size: 14px;
      flex-wrap: nowrap;
      box-sizing: border-box;
      /* #ifndef APP-PLUS */
      white-space: nowrap;
      /* #endif */
    }
    &.uni-tab-item-title-active .uni-tab-item-title {
      color: $theme-color;
    }
  }
  .score-board-line {
    color: $theme-color;
  }
  .text-line {
    position: absolute;
    bottom: 0;
    border-radius: 100px;
    width: 30px;
    transition-duration: 300ms;
    height: 2px;
    background-color: $theme-color;
  }
}

.swiper_box {
  flex: 1;
  .swiper_item {
    flex: 1;
    flex-direction: row;
  }
}
/deep/ .train-item {
  margin-top: 8px;
  background-color: #fff;
  padding: 8px 16px;
  .train-title {
    display: flex;
    overflow: hidden;
    view {
      font-size: 14px;
    }
    view:nth-child(1) {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 8px;
      font-weight: bold;
    }
  }
  .train-info-line {
    font-size: 12px;
    color: #666;
    text + text {
      margin-left: 16px;
    }
  }
}
.status-tag {
  border-radius: 2px;
  margin-right: 4px;
  color: #fff;
  font-size: 10px;
  padding: 2px 8px;
  background-color: #5aaaea;
  width: 52px;
  line-height: 18px;
}
</style>
