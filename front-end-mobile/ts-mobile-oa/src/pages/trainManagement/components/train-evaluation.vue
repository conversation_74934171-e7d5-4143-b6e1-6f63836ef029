<template>
  <view class="evaluation-container">
    <view
      class="evaluation-action-container"
      :class="{ 'is-folded': isFolded }"
    >
      <view class="evaluation-score">
        培训评分：
        <i
          v-for="index in 5"
          :key="index"
          @click="handleGiveAMark(index)"
          class="oa-icon"
          :class="score >= index ? 'oa-icon-shoucang1' : 'oa-icon-shoucang11'"
        >
        </i>
      </view>
      <view>
        <textarea
          v-model="content"
          maxlength="200"
          placeholder="请输入最少10字评论"
          class="row_value_textarea_text"
        />
      </view>
      <view class="post-comment-btn" @click="handleSaveComment">
        发&nbsp;布
      </view>
      <i class="oa-icon oa-icon-fanhui-copy" @click="isFolded = !isFolded"></i>
    </view>

    <view class="evaluation-list">
      <mescroll
        ref="mescroll"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view
          v-for="comment of commentList"
          :key="comment.trainCommentId"
          class="evaluation-item"
        >
          <view class="evaluation-user-info">
            <!-- <image :src="comment.employeeAvatar" class="user-head-image" /> -->
            <image
              src="../../../static/img/nothing.png"
              class="user-head-image"
            />
            <view class="evaluation-user flex-row-between">
              {{ comment.createUserName }}
              <text v-if="comment.orgName">（{{ comment.orgName }}）</text>
              <text v-if="comment.score">-{{ comment.score }}分</text>
            </view>
          </view>
          <view class="evaluation-content-box">
            <view class="evaluation-time">{{ comment.createDate }}</view>
            <view class="comment-content">
              {{ comment.content }}
            </view>
            <view class="action-line">
              <view class="cursor-span" @click="handleEvokeReply(comment)">
                <i class="oa-icon oa-icon-xiaoxi1"></i>
                {{
                  comment.children && comment.children.length
                    ? comment.children.length
                    : '回复'
                }}
              </view>

              <view class="cursor-span" @click="handleExpressingPoint(comment)">
                <i
                  class="oa-icon"
                  :class="
                    comment.praise == 1
                      ? 'oa-icon-jushoucanggift'
                      : 'oa-icon-jushoucang'
                  "
                ></i>
                {{ comment.praiseSize || '点赞' }}
              </view>
            </view>
          </view>
        </view>
      </mescroll>
    </view>

    <uni-popup ref="popup" type="bottom">
      <view class="reply-popup-title">
        {{ replayComment.children && replayComment.children.length }}条回复
        <i class="uni-icon uni-icon-closeempty" @click="handleCloseReply"></i>
      </view>
      <view class="reply-list-content">
        <view
          v-for="reply of replayComment.children"
          :key="reply.trainCommentId"
          class="reply-item"
        >
          <!-- <image :src="comment.employeeAvatar" class="user-head-image" /> -->
          <image
            src="../../../static/img/nothing.png"
            class="user-head-image"
          />
          <view class="reply-info">
            <view class="reply-user">
              {{ reply.createUserName }}
            </view>
            <view class="reply-content-text">
              {{ reply.content }}
            </view>
            <view class="reply-time">
              {{ reply.createDate }}
            </view>
          </view>
          <view class="point-content">
            <i
              class="oa-icon"
              :class="
                reply.praise == 1
                  ? 'oa-icon-jushoucanggift'
                  : 'oa-icon-jushoucang'
              "
              @click="handleExpressingPoint(reply)"
            ></i>
            {{ reply.praiseSize || '' }}
          </view>
        </view>
      </view>
      <view class="reply-content">
        <!-- <image :src="comment.employeeAvatar" class="user-head-image" /> -->
        <view
          v-show="showLoginUserHeadImg"
          class="reply-mack"
          @click.stop
        ></view>
        <view
          class="reply-input-content"
          :class="{
            'is-focus': showLoginUserHeadImg
          }"
        >
          <image
            v-show="!showLoginUserHeadImg"
            src="../../../static/img/nothing.png"
            class="user-head-image"
          />
          <u-input
            v-model="replyContent"
            :auto-blur="true"
            :focus="false"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
            @confirm="handleReply"
          ></u-input>
          <view
            v-show="showLoginUserHeadImg"
            @click="handleReply"
            class="reply-btn"
          >
            回复
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';

export default {
  components: {
    mescroll
  },
  props: {
    trainData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      score: 0, // 评分
      isFolded: false, // 评论内容是否折叠

      content: '', // 评论内容
      commentList: [], // 一级评论列表

      replyModal: false, // 二级评论是否显示
      replayComment: {}, // 正在显示二级评论的一级评论
      replyContent: '', // 回复内容
      showLoginUserHeadImg: false // 是否显示头像
    };
  },
  methods: {
    refresh() {
      this.$refs.mescroll && this.$refs.mescroll.downCallback();
      this.getSelfTrainingItemCommentData();
    },
    /**@desc 获取评论列表内容 */
    getListData(page, successCallback, errorCallback) {
      this.ajax
        .getTrainingItemCommentDataList({
          trainPlanId: this.trainData.trainPlanId,
          pageNo: page.num,
          pageSize: page.size
        })
        .then(res => {
          successCallback(res.rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    /**@desc 更新评论数据数组 */
    setListData(rows) {
      this.commentList = this.commentList.concat(rows);
      if (Object.keys(this.replayComment).length) {
        this.replayComment = this.commentList.find(
          item => item.trainCommentId == this.replayComment.trainCommentId
        );
      }
    },
    /**@desc 评论数据重新初始化 */
    datasInit() {
      this.commentList = [];
    },
    /**@desc 获取评分数据 */
    getSelfTrainingItemCommentData() {
      this.ajax
        .getSelfTrainingItemCommentData(this.trainData.trainPlanId)
        .then(res => {
          if (res.success == false) {
            uni.showToast({
              title: res.message || '评分数据获取失败',
              icon: 'none'
            });
            return;
          }
          this.score = res.object.score;
        });
    },
    /**@desc 评分 */
    handleGiveAMark(score) {
      this.ajax
        .handleSaveTrainingScoreData({
          trainPlanId: this.trainData.trainPlanId,
          score
        })
        .then(res => {
          if (res.success == false) {
            uni.showToast({
              title: res.message || '评分失败',
              icon: 'none'
            });
            return;
          }
          this.score = score;
          uni.showToast({
            title: '评分成功',
            icon: 'none'
          });
        });
    },
    /**@desc 保存评价 */
    handleSaveComment() {
      if (!this.content || this.content.length < 10) {
        uni.showToast({
          title: '请输入最少10字评价',
          icon: 'none'
        });
        return;
      }
      this.ajax
        .handleSaveTrainingCommentData({
          trainPlanId: this.trainData.trainPlanId,
          content: this.content
        })
        .then(res => {
          if (!res.success) {
            uni.showToast({
              title: res.message || '评论发布失败',
              icon: 'none'
            });
            return;
          }
          uni.showToast({
            title: '评论发布成功',
            icon: 'none'
          });
          this.refresh();
          this.content = '';
        });
    },
    /**@desc 点赞 */
    handleExpressingPoint(comment) {
      this.ajax
        .handleExpressingPointForTrainingComment({
          trainCommentId: comment.trainCommentId
        })
        .then(res => {
          if (!res.success) {
            uni.showToast({
              title: res.message || '操作失败',
              icon: 'none'
            });
            return;
          }
          uni.showToast({
            title: '操作成功',
            icon: 'none'
          });
          let { praiseSize = 0, praise } = comment;
          praiseSize = praise == 1 ? praiseSize - 1 : praiseSize + 1;
          this.$set(comment, 'praise', praise == 1 ? 0 : 1);
          this.$set(comment, 'praiseSize', praiseSize);
        });
    },
    /**@desc 唤起回复弹窗 */
    handleEvokeReply(comment) {
      this.replayComment = comment;
      this.replyContent = '';
      this.$refs.popup.open();
    },
    /**@desc 关闭回复弹窗 */
    handleCloseReply() {
      this.$refs.popup.close();
      this.replayComment = {};
    },
    /**@desc 处理输入框聚焦 */
    handleInputFocus() {
      this.showLoginUserHeadImg = true;
      //TODO 解决苹果页面上移的问题
    },
    /**@desc 处理输入框失焦 */
    handleInputBlur() {
      //由于直接隐藏会导致回复按钮无法点击，因此延迟一下
      setTimeout(() => {
        this.showLoginUserHeadImg = false;
      }, 200);
    },
    /**@desc 回复评论 */
    handleReply() {
      this.ajax
        .handleSaveTrainingCommentData({
          trainPlanId: this.trainData.trainPlanId,
          parentId: this.replayComment.trainCommentId,
          content: this.replyContent
        })
        .then(res => {
          if (!res.success) {
            uni.showToast({
              title: res.message || '回复失败',
              icon: 'none'
            });
            return;
          }
          uni.showToast({
            title: '回复成功',
            icon: 'none'
          });
          this.replyContent = '';
          this.refresh();
        });
    }
  },
  watch: {
    'trainData.trainPlanId': {
      handler(val) {
        val && this.$nextTick(this.refresh);
      },
      immediate: true
    }
  }
};
</script>

<style lang="scss" scoped>
view {
  font-size: 16px;
}
.oa-icon {
  font-size: 16px;
}
.static-content {
  overflow: hidden !important;
}
.evaluation-container {
  background-color: #fff;
  .evaluation-list {
    flex: 1;
    overflow: hidden;
    position: relative;
    background-color: #f8f8f8;
  }
  .evaluation-action-container {
    padding: 8px 16px;
    box-shadow: 0 4rpx 4rpx rgba(0, 0, 0, 0.15);
    z-index: 1;
    position: relative;
    overflow: hidden;
    height: 245px;
    transition: height 0.3s;
    .evaluation-score {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      .oa-icon {
        font-size: 20px;
        margin-right: 8px;
      }
      .oa-icon-shoucang1 {
        color: $theme-color;
      }
    }
    uni-textarea {
      width: 100%;
      height: 150px;
    }
    .post-comment-btn {
      background-color: $theme-color;
      color: #fff;
      padding: 4px 8px;
      font-size: 14px;
      border-radius: 2px;
      width: 49px;
      margin-top: 8px;
    }
    .oa-icon-fanhui-copy {
      position: absolute;
      right: 16px;
      bottom: 9px;
      transition: all 0.3s;
    }
    &.is-folded {
      height: 48px;
      .oa-icon-fanhui-copy {
        transform: rotate(180deg);
      }
    }
  }

  .evaluation-item {
    padding: 8px 16px;
  }
  .evaluation-user-info {
    display: flex;
    align-items: center;
    .user-head-image {
      height: 38px;
      width: 38px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 8px;
      background: red;
    }
    .evaluation-user {
      font-size: 14px;
      font-weight: bold;
    }
  }
  .evaluation-content-box {
    background-color: #fff;
    border-radius: 16px;
    padding: 8px 16px;
    margin-top: 8px;
    margin-left: 40px;
    .evaluation-time {
      text-align: right;
      font-size: 12px;
      color: #999;
    }
    .comment-content {
      font-size: 15px;
    }
    .action-line {
      height: 26px;
      position: relative;
      > view:first-child {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
      }
      > view:last-child {
        position: absolute;
        right: 0;
      }
      .cursor-span {
        font-weight: bold;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        .oa-icon {
          font-weight: normal;
          margin-right: 4px;
        }
      }
    }
  }
}
.oa-icon-jushoucanggift {
  color: #f44444;
}
/deep/ .upwarp-nodata {
  font-size: 16px !important;
}
/deep/ .uni-transition:not(.fade-out) {
  background-color: transparent !important;
}
.reply-popup-title {
  height: 40px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  border-radius: 8px 8px 0 0;
  padding: 8px;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  position: relative;
  .uni-icon-closeempty {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
  }
}
.reply-list-content {
  background-color: #fff;
  height: 360px;
  overflow-y: auto;
  margin-bottom: calc(
    40px + constant(safe-area-inset-bottom)
  ); // 兼容 IOS 11.2 以下
  margin-bottom: calc(40px + env(safe-area-inset-bottom));
  .reply-item {
    display: flex;
    padding: 8px 16px;
    .user-head-image {
      width: 26px;
      height: 26px;
      border-radius: 50%;
      background: red;
      flex-shrink: 0;
    }
    .reply-info {
      flex: 1;
      margin: 0 8px;
      .reply-content-text {
        font-size: 14px;
      }
      .reply-user,
      .reply-time {
        font-size: 12px;
        color: #999;
      }
    }
    .point-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 14px;
      i {
        line-height: 16px;
      }
    }
  }
}
.reply-content {
  .reply-mack {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.2);
  }
  .reply-input-content {
    background-color: #fff;
    border-top: 1px solid #eee;
    min-height: 40px;
    width: 100vw;
    padding: 4px 8px;
    position: fixed;
    bottom: 0;
    display: flex;
    align-items: center;
    &:not(.is-focus) {
      padding-bottom: calc(
        4px + constant(safe-area-inset-bottom)
      ); // 兼容 IOS 11.2 以下
      padding-bottom: calc(
        4px + env(safe-area-inset-bottom)
      ); // 兼容 IOS 11.2 以上
    }
    .u-input {
      margin: 0 8px;
      background-color: #f4f4f4;
    }

    .reply-btn {
      font-size: 12px;
      color: #fff;
      background-color: $theme-color;
      padding: 2px 8px;
      border-radius: 24px;
    }
  }
  .user-head-image {
    width: 24px;
    height: 24px;
    border-radius: 50%;
  }
}
</style>
