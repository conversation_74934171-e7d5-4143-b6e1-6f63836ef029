<template>
  <view class="sign-container">
    <page-head title="" @clickLeft="returnBack"> </page-head>
    <view class="sign-res-content">
      <view
        v-if="signRes == 'true'"
        class="oa-icon oa-icon-tijiaobanlichenggong"
      ></view>
      <view v-if="signRes == 'false'" class="oa-icon oa-icon-guanbi"></view>
      <view class="toast">
        {{ signToast }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      signinType: '',
      trainPlanId: '',
      signRes: '',
      signToast: ''
    };
  },
  onLoad(opt) {
    let { signinType, trainPlanId } = opt || {};
    this.signinType = signinType;
    this.trainPlanId = trainPlanId;
    if (!this.signinType || !this.trainPlanId) {
      uni.redirectTo({
        url: '/pages/trainManagement/my-train'
      });
      return;
    }
    signinType && trainPlanId && this.handleSign();
  },
  methods: {
    returnBack() {
      uni.redirectTo({
        url: `/pages/trainManagement/train-detail?id=${this.trainPlanId}&signinType=${this.signinType}`
      });
    },
    handleSign() {
      this.ajax
        .handleTrainPlanSignInOrOut(
          `signinType=${this.signinType}&trainPlanId=${this.trainPlanId}`
        )
        .then(res => {
          if (!res.success) {
            uni.showToast(
              res.message ||
                { 1: '签到失败！', 2: '签退失败！' }[this.signinType]
            );
            this.signRes = 'false';
            this.signToast =
              { 1: '签到失败！', 2: '签退失败！' }[this.signinType] +
              '请重新扫码';
          } else {
            this.signRes = 'true';
            this.signToast = { 1: '签到成功！', 2: '签退成功！' }[
              this.signinType
            ];
            uni.showToast({
              title: this.signToast + '3s后返回详情页',
              icon: 'none'
            });
            setTimeout(() => {
              this.returnBack();
            }, 3000);
          }
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.sign-container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .sign-res-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .oa-icon {
      font-size: 48px;
    }
    .oa-icon-tijiaobanlichenggong {
      color: $u-type-success;
    }
    .oa-icon-guanbi {
      color: $u-type-error;
    }
  }
}
</style>
