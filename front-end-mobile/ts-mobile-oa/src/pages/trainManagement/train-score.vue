<template>
  <view class="train-detail-container">
    <page-head title="积分榜" @clickLeft="returnBack"> </page-head>
    <view class="outside-search-container">
      <uni-search-bar
        radius="100"
        placeholder="姓名"
        borderColor="transparent"
        bgColor="#F4F4F4"
        cancelButton="none"
        @confirm="search"
      />
    </view>

    <view class="table-container">
      <u-table>
        <u-tr class="u-tr">
          <u-th width="60px">排名</u-th>
          <u-th>姓名</u-th>
          <u-th width="90px" @click.native="handleChangeSortRule('byjf')">
            <view class="head-cell">
              本月积分
              <template v-if="sidx == 'byjf'">
                <u-icon
                  :name="sord == 'desc' ? 'arrow-down-fill' : 'arrow-up-fill'"
                  size="24"
                />
              </template>
            </view>
          </u-th>
          <u-th width="90px" @click.native="handleChangeSortRule('zjf')">
            <view class="head-cell">
              总积分
              <template v-if="sidx == 'zjf'">
                <u-icon
                  :name="sord == 'desc' ? 'arrow-down-fill' : 'arrow-up-fill'"
                  size="24"
                />
              </template>
            </view>
          </u-th>
        </u-tr>
      </u-table>
      <view class="table-content">
        <u-table>
          <mescroll
            ref="mescroll"
            @getDatas="getListData"
            @setDatas="setListData"
            @datasInit="datasInit"
          >
            <u-tr v-for="(row, index) of tableDataList" :key="index">
              <u-td width="60px">{{ row.no }}</u-td>
              <u-td>{{ row.employeeName }}</u-td>
              <u-td width="90px">{{ row.byjf }}</u-td>
              <u-td width="90px">{{ row.zjf }}</u-td>
            </u-tr>
          </mescroll>
        </u-table>
      </view>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';

export default {
  components: {
    mescroll
  },
  data() {
    return {
      sidx: 'zjf',
      sord: 'desc',
      employeeName: '',
      tableDataList: []
    };
  },
  methods: {
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        this.$parentTypeFun({
          type: 'jumpPage',
          path: '/workbench'
        });
      } else {
        uni.navigateBack();
      }
    },
    handleChangeSortRule(sidx) {
      if (this.sidx == sidx) {
        this.sord = this.sord == 'asc' ? 'desc' : 'asc';
      } else {
        this.sidx = sidx;
        this.sord = 'desc';
      }
      this.refresh();
    },
    search(e) {
      this.employeeName = e ? e.value : '';
      this.refresh();
    },
    refresh() {
      this.$refs.mescroll.downCallback();
    },
    getListData(page, successCallback, errorCallback) {
      this.ajax
        .getTrainingAccumulatePointsData({
          pageNo: page.num,
          pageSize: page.size,
          sord: this.sord,
          sidx: this.sidx,
          employeeName: this.employeeName,
          rankingType: 1
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(list = []) {
      this.tableDataList.push(...list);
    },
    datasInit() {
      this.tableDataList = [];
    }
  }
};
</script>

<style lang="scss" scoped>
.train-detail-container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .table-container {
    background-color: #fff;
    flex: 1;
    display: flex;
    flex-direction: column;
    .table-content {
      position: relative;
      flex: 1;
      .u-td {
        height: unset;
      }
    }
  }
  .head-cell {
    font-size: inherit;
    font-weight: inherit;
    color: inherit;
    .u-icon {
      margin-left: 4px;
      transform: scale(0.8);
    }
  }
}
.outside-search-container {
  padding-left: 8px;
  overflow: hidden;
  margin: 8px 0;
  background-color: #fff;
  display: flex;
  align-items: center;
  .uni-searchbar {
    flex: 1;
  }
  .oa-icon-shaixuan {
    padding-left: 8px;
    padding-right: 16px;
  }
}
</style>
