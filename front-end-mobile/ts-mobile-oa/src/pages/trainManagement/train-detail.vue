<template>
  <view class="train-detail-container">
    <page-head title="培训详情" @clickLeft="returnBack"> </page-head>

    <base-tabs v-model="tabIndex" :tabs="tabBars"></base-tabs>

    <swiper :current="tabIndex" class="swiper_box" :duration="300">
      <swiper-item class="swiper_item">
        <view class="swiper-info-content">
          <view
            v-for="(item, index) of columns"
            :key="index"
            class="base-info-item"
          >
            <view class="info-label">
              {{ item.label }}
            </view>

            <view class="info-content">{{ detailData[item.prop] }}</view>
          </view>
        </view>
      </swiper-item>
      <swiper-item class="swiper_item">
        <view class="swiper-table-info-content">
          <view class="table-content">
            <table class="file-table">
              <tr class="table-title">
                <th
                  v-for="item of fileColumns"
                  :key="item.key"
                  :style="{ width: item.width }"
                >
                  {{ item.title }}
                </th>
              </tr>
              <tbody>
                <tr v-for="(row, index) of fileData" :key="row.id">
                  <td
                    v-for="col of fileColumns"
                    :key="col.key"
                    :style="{
                      textAlign: col.align
                    }"
                  >
                    <template v-if="col.formatter">
                      <tool-comp
                        :formatter="col.formatter(row, index)"
                      ></tool-comp>
                    </template>
                    <template v-else>
                      {{ row[col.key] }}
                    </template>
                  </td>
                </tr>
              </tbody>
            </table>
          </view>
        </view>
      </swiper-item>
      <swiper-item class="swiper_item">
        <trainEvaluation
          :trainData="detailData"
          class="swiper-table-info-content"
        />
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import toolComp from './components/tool-comp.vue';
import trainEvaluation from './components/train-evaluation.vue';

import Base64 from '../../common/js/base64.min.js';
export default {
  components: {
    toolComp,
    trainEvaluation
  },
  data() {
    return {
      tabIndex: 0,
      tabBars: [
        {
          name: '基本信息'
        },
        {
          name: '附件'
        },
        {
          name: '评论'
        }
      ],

      detailData: {},
      trainType: [],
      columns: [
        {
          label: '培训主题',
          prop: 'trainTopic'
        },
        {
          label: '培训讲师',
          prop: 'trainLecturer'
        },
        {
          label: '培训类别',
          prop: 'trainType'
        },
        {
          label: '预算(元)',
          prop: 'budget'
        },
        {
          label: '培训时间',
          prop: 'trainStartTime'
        },
        {
          label: '申请人',
          prop: 'applicantName'
        },
        {
          label: '申请科室',
          prop: 'applicantDeptName'
        },
        {
          label: '学时',
          prop: 'creditHour'
        },
        {
          label: '积分',
          prop: 'credit'
        },
        {
          label: '培训地点',
          prop: 'trainPlace'
        },
        {
          label: '培训内容',
          prop: 'trainContent'
        },
        {
          label: '备注',
          prop: 'remark'
        }
      ],

      userTableData: [
        {
          id: 'admin',
          name: 'adminName',
          sex: '男',
          dept: '科室'
        }
      ],
      userColumns: [
        {
          title: '工号',
          key: 'id'
        },
        {
          title: '姓名',
          key: 'name'
        },
        {
          title: '性别',
          key: 'sex'
        },
        {
          title: '科室',
          key: 'dept'
        }
      ],

      fileData: [],
      fileColumns: [
        {
          title: '',
          key: 'pageIndex',
          align: 'center',
          width: '40px',
          formatter: (row, index) => {
            return <view>{index + 1}</view>;
          }
        },
        {
          title: '文件名',
          key: 'originalName',
          formatter: row => {
            let icon = row.hasIcon ? <view class="file-icon">回放</view> : '';
            return (
              <view onClick={this.downloadFile.bind(this, row)}>
                {icon}
                {row.originalName}
              </view>
            );
          }
        },
        {
          title: '上传人',
          key: 'createUserName'
        },
        {
          title: '上传时间',
          key: 'createDate'
        }
      ]
    };
  },
  async onLoad(opt) {
    await this.getTrainingType();
    this.getDetailData(opt.id);
    if (opt.signinType == 2) {
      this.tabIndex = 2;
    }
  },
  methods: {
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        uni.redirectTo({
          url: '/pages/trainManagement/my-train'
        });
      } else {
        uni.navigateBack();
      }
    },
    ontabtap(index) {
      this.tabIndex = index;
      let header = this.$refs.tabHeader.$el,
        activeNode = header.querySelector(
          `.uni-tab-item:nth-child(${index + 1})`
        ),
        offsetLeft = activeNode.offsetLeft,
        line = this.$refs.activeLine.$el,
        left = activeNode.offsetWidth / 2 + offsetLeft - line.offsetWidth / 2;
      line.style.transform = `translate(${left}px)`;
    },
    getDetailData(id) {
      this.ajax.getTrainPlanDetailData(id).then(res => {
        if (!res.success) {
          uni.showToast({
            title: res.message || '详情数据获取失败',
            icon: 'none'
          });
          return;
        }
        this.detailData = res.object;
        this.detailData.trainType = this.trainType.find(
          item => item.value == this.detailData.trainType
        )?.label;
        let { trainFile, huifangFile } = this.detailData;
        if (trainFile || huifangFile) {
          trainFile && this.getFileDetail(trainFile);
          huifangFile && this.getFileDetail(huifangFile, true);
        }
      });
    },
    /**@desc 获取培训类型 */
    getTrainingType() {
      return this.ajax.getDataByDataLibrary('TRAINING_TYPE').then(res => {
        if (!res.success) {
          this.$message.error(res.message || '培训类型获取失败');
          return;
        }
        this.trainType = res.object.map(item => ({
          label: item.itemName,
          value: item.itemNameValue
        }));
      });
    },
    // /**@desc 点击扫码签到 */
    // handleScanQRCode() {
    //   wxInit.scanQRCode(this);
    // }
    getFileDetail(businessId, hasIcon = false) {
      this.ajax
        .getFileAttachmentByBusinessId({
          businessId
        })
        .then(res => {
          this.fileData = this.fileData.concat(
            res.object.map(item => ({ ...item, hasIcon }))
          );
        });
    },
    //查看附件详情
    downloadFile(item) {
      let filePath = `${
        this.$config.ENABLE_FILE_PREVIEW
          ? this.$config.DOCUMENT_BASE_HOST
          : this.$config.BASE_HOST
      }/ts-basics-bottom/fileAttachment/downloadFile/${item.id}?fullfilename=${
        item.originalName
      }&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      } else {
        this.$downloadFile.downloadFile(filePath);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.train-detail-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.base-tabs {
  margin-bottom: 8px;
}
.swiper_box {
  flex: 1;
  overflow: hidden;
  .swiper_item {
    flex: 1;
    flex-direction: row;
  }
}
.swiper-info-content {
  height: 100%;
  overflow: auto;
}
.swiper-table-info-content {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  line-height: 0;
  .table-content {
    flex: 1;
    position: relative;
    overflow: auto;
    background: #fff;
  }
}
.base-info-item {
  display: flex;
  overflow: hidden;
  padding: 8px 16px;
  background-color: #fff;
  &:not(:last-child) {
    border-bottom: 1px solid #eee;
  }
  .info-label {
    width: 80px;
    flex-shrink: 0;
    font-size: 14px;
  }
  .info-content {
    font-size: 14px;
    flex: 1;
    display: flex;
    justify-content: flex-end;
  }
}
.qr-code-btn {
  height: 40px;
  background-color: $theme-color;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
}
.file-table {
  width: 100%;
  background-color: #fff;
  border-collapse: collapse;
  th {
    font-size: 14px;
    background-color: #f7f8f8;
    padding: 5px;
    box-sizing: border-box;
    border: 1px solid #f4f4f4;
  }
  td {
    font-size: 14px;
    padding: 5px;
    border: 1px solid #f4f4f4;
  }
  .file-icon {
    border-radius: 2px;
    font-size: 8px;
    background-color: $theme-color;
    color: #fff;
    padding: 2px 4px;
  }
}
</style>
