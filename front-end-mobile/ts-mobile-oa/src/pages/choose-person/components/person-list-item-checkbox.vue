<template>
  <view class="person-list-item-checkbox-group">
    <view
      class="person-list-item-checkbox"
      v-for="item in list"
      :key="item.userId"
      @click="toggle(item)"
    >
      <view class="checkbox__icon-wrap" :class="item.checked | iconClass">
        <u-icon
          class="checkbox__icon-wrap__icon"
          name="checkbox-mark"
          :size="iconSize"
          :color="item.checked | iconColor"
        />
      </view>
      <person-list-item-info
        class="checkbox__info-wrap"
        :person="item"
      ></person-list-item-info>
    </view>
  </view>
</template>

<script>
import personListItemInfo from './person-list-item-info.vue';
export default {
  name: 'person-list-item-checkbox',
  components: {
    personListItemInfo
  },
  props: {
    list: {
      type: Array,
      default() {
        return [];
      }
    },
    max: {
      type: [String, Number],
      default: '9999'
    },
    value: {
      type: String,
      default: ''
    },
    iconSize: {
      type: [String, Number],
      default: '28'
    },
    iconColor: {
      type: String,
      default: ''
    }
  },
  filters: {
    iconColor(val) {
      return val ? '#ffffff' : 'transparent';
    },
    iconClass(val) {
      return val ? 'checkbox__icon-wrap--checked' : '';
    }
  },
  methods: {
    toggle(e) {
      if (e.checked) {
        e.checked = !e.checked;
      } else {
        if (this.value.length >= this.max) {
          return this.$u.toast(`最多可选${this.max}项`);
        }
        e.checked = !e.checked;
      }
      this.$emit('change', e);
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../assets/css/flex.scss';
.person-list-item-checkbox {
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
  background-color: #ffffff;
  @include vue-flex;
  align-items: center;
  overflow: hidden;
  -webkit-user-select: none;
  user-select: none;
}
.checkbox__icon-wrap {
  color: $uni-text-content-color;
  @include vue-flex;
  flex: none;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 38rpx;
  height: 38rpx;
  color: transparent;
  text-align: center;
  transition-property: color, border-color, background-color;
  font-size: $uni-icon-size-base;
  border: 1px solid $uni-text-color-disable;
  border-radius: 4px;
  transition-duration: 0.2s;
}
.checkbox__icon-wrap--checked {
  border-color: $u-type-primary;
  background-color: $u-type-primary;
}
.checkbox__info-wrap {
  flex: 1;
  margin-left: $uni-spacing-row-base;
}
</style>
