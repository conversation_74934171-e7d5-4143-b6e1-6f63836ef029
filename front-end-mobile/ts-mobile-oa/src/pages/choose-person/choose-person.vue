<template>
  <view class="ts-container">
    <u-navbar title="人员选择" title-bold>
      <text class="navbar-right" slot="right" @click="confirm">确定</text>
    </u-navbar>
    <view class="search-container">
      <u-search
        v-model="keywords"
        :show-action="false"
        placeholder="输入姓名、科室搜索"
        @search="search"
        @clear="clear"
      ></u-search>
      <view class="choose-person-num" @click="changePopopShow">
        已选({{ choosePersonNum }})
      </view>
    </view>
    <view class="choose-person">
      <mescroll
        v-if="mode == 'scoll'"
        :ref="`mescroll`"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <template #default>
          <person-list
            :type="chooseType"
            :list="list"
            :infoProp="personInfoProp"
            @change="choosePerson"
          ></person-list>
        </template>
      </mescroll>
      <person-list
        v-else
        :type="chooseType"
        :list="list"
        @change="choosePerson"
      ></person-list>
    </view>
    <choose-person-popup
      v-model="showPopup"
      :list="choosePersonList"
      :infoProp="personInfoProp"
      @change="choosePersonChange"
    ></choose-person-popup>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import personList from './components/person-list.vue';
import choosePersonPopup from './components/choose-person-popup.vue';
export default {
  name: 'choose-person',
  components: {
    mescroll,
    personList,
    choosePersonPopup
  },
  data() {
    return {
      title: '',
      mode: 'scoll',
      keywords: '',
      list: [],
      choosePersonList: [],
      chooseType: '',
      api: '',
      apiType: '',
      params: {},
      showPopup: false,
      personInfoProp: undefined
    };
  },
  computed: {
    choosePersonNum() {
      return this.choosePersonList.length;
    },
    defaultSort() {
      let sortDatas = this.$store.state?.personalSortData ?? {},
        { sidx = 'create_date', sord = 'desc' } = sortDatas;
      sidx = 'a.' + sidx;
      return { sord, sidx };
    }
  },
  onLoad(opt) {
    this.choosePersonList = JSON.parse(uni.getStorageSync('person_list'));
    this.chooseType = opt.chooseType;
    this.searchType = opt.searchType || 'workSheetPost';
    let pageParams = JSON.parse(uni.getStorageSync('personPageParams'));
    this.personInfoProp = pageParams.personInfoProp || {
      name: 'name',
      describe: 'deptName',
      concatSymbol: '',
      describeConcatSymbol: '-',
      key: 'userId',
      sex: 'sex',
      headImg: 'headImg'
    };
    if (pageParams.api) {
      this.api = pageParams.api;
      this.apiType = pageParams.apiType;
      this.params = pageParams.params;
    }
    this.title = pageParams.title;
  },
  methods: {
    search() {
      if (this.mode == 'scoll') {
        this.datasInit();
        this.$refs['mescroll'].downCallback();
      }
    },
    clear() {
      if (this.mode == 'scoll') {
        this.datasInit();
        this.$refs['mescroll'].downCallback();
      }
    },
    getListData(page, successCallback, errorCallback) {
      switch (this.searchType) {
        case 'fullApiPost':
          this.ajax
            .getPersonListFullPathPost(this.api, this.apiType, {
              ...this.params,
              pageNo: page.num,
              pageSize: page.size,
              searchKey: this.keywords,
              ...this.defaultSort
            })
            .then(res => {
              successCallback(res.rows, res.totalCount);
            })
            .catch(err => {
              errorCallback();
            });
          break;
        case 'workSheetPost':
        default:
          this.ajax
            .getWorksheetPersonList(this.api, {
              ...this.params,
              pageNo: page.num,
              pageSize: page.size,
              searchKey: this.keywords
            })
            .then(res => {
              successCallback(res.rows, res.totalCount);
            })
            .catch(err => {
              errorCallback();
            });
          break;
      }
    },
    setListData(rows, totalCount) {
      const key = (this.personInfoProp && this.personInfoProp.key) || 'userId';
      rows.forEach(item => {
        let listIndex = this.choosePersonList.findIndex(
          one => one[key] === item[key]
        );
        item.checked = listIndex >= 0;
        if (listIndex >= 0) {
          this.choosePersonList.splice(listIndex, 1, item);
        }
      });
      this.list = this.list.concat(rows);
    },
    datasInit() {
      this.list = [];
    },
    choosePerson(val) {
      if (this.chooseType == 'radio') {
        this.choosePersonList = val;
      } else {
        const key =
          (this.personInfoProp && this.personInfoProp.key) || 'userId';
        if (val.checked) {
          this.choosePersonList.push(val);
        } else {
          let index = this.choosePersonList.findIndex(
            item => item[key] == val[key]
          );
          this.choosePersonList.splice(index, 1);
        }
      }
    },
    changePopopShow() {
      this.showPopup = true;
    },
    choosePersonChange(val) {
      const key = (this.personInfoProp && this.personInfoProp.key) || 'userId';
      this.choosePersonList = val;
      this.list.forEach(item => {
        item.checked = this.choosePersonList.some(
          one => one[key] === item[key]
        );
      });
    },
    confirm() {
      uni.$emit('trasenPerson', this.choosePersonList);
      uni.navigateBack({
        delta: 1
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../assets/css/flex.scss';
.ts-container {
  height: 100%;
  position: relative;
  @include vue-flex(column);
}
.navbar-right {
  padding: 0 $uni-spacing-row-lg;
  font-size: $uni-font-size-base;
}
.search-container {
  padding: $uni-spacing-row-sm $uni-spacing-row-lg;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  margin-bottom: $uni-spacing-col-base;
}
.choose-person-num {
  font-size: $uni-font-size-base;
  margin-left: $uni-spacing-row-lg;
}
.choose-person {
  flex: 1;
  position: relative;
}
</style>
