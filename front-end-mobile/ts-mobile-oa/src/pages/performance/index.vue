<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" :title="title"></page-head>
    <scroll-view class="swiper_head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tab, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        :data-current="index"
        @click="ontabtap"
      >
        <view :class="tabIndex == index ? 'uni-tab-item-title-active' : ''">
          <text class="uni-tab-item-title">{{ tab.title }}</text>
          <text class="uni-tab-item-num" v-if="tab.total != null"
            >（{{ tab.total }}）</text
          >
        </view>
      </view>
    </scroll-view>
    <swiper
      :current="tabIndex"
      class="swiper_box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper_item"
        v-for="(item, index) in tabBars"
        :key="index"
      >
        <mescroll
          :ref="'mescroll' + index"
          :mescrollIndex="index"
          :down="item.downOption"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view class="contact_list">
            <view
              class="contact_item"
              v-for="row in item['list']"
              :key="row.wfInstanceId"
              @tap="chooseItem(row, index)"
            >
              <view class="contact_item_row">
                <view class="contact_item_title">
                  <text class="title">{{ row.title }}</text>
                </view>
                <text class="contact_item_time">
                  {{ row.releaseDate }}
                </text>
              </view>
              <view class="contact_item_row">
                <text class="contact_item_node">{{
                  '发起人：' + row.createUserName
                }}</text>
              </view>
            </view>
          </view>
        </mescroll>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      title: '360测评',
      tabIndex: 0, //当前选中的tab索引值，从0计数
      tabBars: [
        {
          title: '待测评',
          releaseStatus: 0, //办理状态
          downOption: false, //初始化时是否下拉加载
          isInit: false, //是否已初始化
          total: null,
          list: []
        },
        {
          title: '已测评',
          releaseStatus: 1,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        }
      ]
    };
  },
  onLoad(opt) {
    if (opt.tabIndex) this.tabIndex = Number(opt.tabIndex);
    this.tabBars.map((item, index) => {
      if (this.tabIndex == index) {
        item.downOption = true;
        item.isInit = true;
      } else {
        item.downOption = false;
        item.isInit = false;
      }
    });
    this.ontabtap(this.tabIndex);
    this.getInfoNum();
  },
  methods: {
    getInfoNum() {
      let _self = this;
      _self.ajax
        .selectReleaseCount({ userCode: _self.$store.state.empcode })
        .then(res => {
          _self.$set(_self.tabBars[0], 'total', res.object.release_wait);
          _self.$set(_self.tabBars[1], 'total', res.object.release_submit);
        });
    },
    //搜索
    search(res) {
      let _self = this;
      _self.tabBars.forEach(item => {
        item.list = [];
        item.isInit = false;
      });
      _self.$nextTick(() => {
        _self.tabBars[_self.tabIndex]['isInit'] = true;
        _self.$refs[`mescroll${_self.tabIndex}`][0].downCallback();
      });
    },
    //tab点解切换
    async ontabtap(e) {
      let _self = this;
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      await _self.switchTab(Number(index));
    },
    //tab滑动切换
    async ontabchange(e) {
      let _self = this,
        index = e.target.current || e.detail.current;
      await _self.switchTab(Number(index));
    },
    async switchTab(index) {
      let _self = this;
      if (_self.tabIndex === index) {
        return;
      } else if (!_self.tabBars[index]['isInit']) {
        _self.tabBars[index]['isInit'] = true;
        await _self.$refs[`mescroll${index}`][0].downCallback();
      }
      _self.tabIndex = index;
    },
    async getListData(page, successCallback, errorCallback) {
      let _self = this;
      await _self.ajax
        .getReleaselist({
          releaseStatus: _self.tabBars[_self.tabIndex].releaseStatus,
          pageSize: page.size,
          pageNo: page.num,
          userCode: _self.$store.state.empcode,
          sidx: 'release_date',
          sord: 'desc'
        })
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      let _self = this;
      _self.tabBars[index]['total'] = totalCount;
      let list = rows.filter(item => item.status != 0);
      _self.tabBars[index]['list'] = _self.tabBars[index]['list'].concat(list);
    },
    datasInit(index) {
      if (index == '') return;
      let _self = this;
      _self.tabBars[index]['list'] = [];
    },
    chooseItem(row, index) {
      let _self = this;
      _self.$nextTick(() => {
        uni.navigateTo({
          url: `/pages/performance/question?masterId=${row.masterId}&releaseId=${row.id}&status=${row.releaseStatus}&tabIndex=${this.tabIndex}`
        });
      });
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  .swiper_head {
    position: relative;
    width: 100%;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    /* #ifndef APP-PLUS */
    white-space: nowrap;
    /* #endif */
    /* flex-wrap: nowrap; */
    /* border-color: #cccccc;
			border-bottom-style: solid;
			border-bottom-width: 1px; */
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
    .uni-tab-item {
      /* #ifndef APP-PLUS */
      display: inline-block;
      /* #endif */
      flex-wrap: nowrap;
      width: 50%;
      padding-left: 34rpx;
      padding-right: 34rpx;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title,
      .uni-tab-item-num {
        color: #666;
        font-size: 30rpx;
        height: 100%;
        line-height: 2.5;
        flex-wrap: nowrap;
        box-sizing: border-box;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
      }
      .uni-tab-item-num {
        font-size: 28rpx;
        color: #fff;
        color: #f59a23;
      }
      .uni-tab-item-title-active {
        color: $theme-color;
        border-bottom: 2px solid $theme-color;
      }
    }
  }
  .swiper_box {
    flex: 1;
    .swiper_item {
      flex: 1;
      flex-direction: row;
    }
  }
  .uni_collapse_cell {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    border-color: #e5e5e5;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    height: 48px;
    .uni_collapse_cell_title {
      padding: 12px 12px;
      position: relative;
      display: flex;
      width: 100%;
      box-sizing: border-box;
      height: 48px;
      line-height: 24px;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .uni_collapse_cell_title_text {
        flex: 1;
        font-size: 14px;
        white-space: nowrap;
        color: inherit;
        overflow: hidden;
        text-overflow: ellipsis;
        color: rgb(51, 51, 51);
        font-weight: bold;
      }
    }
  }
  .contact_list {
    height: 100%;
    overflow: scroll;
  }
  .contact_item {
    padding: 22rpx 30rpx;
    background-color: #ffffff;
    position: relative;
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      height: 1px;
      background-color: #eee;
      left: 30rpx;
      right: 0;
    }
    &:last-child::after {
      height: 0;
    }
    .contact_item_row {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    .contact_item_title {
      font-size: 32rpx;
      color: #333333;
      font-weight: bold;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex: 1;
      .title {
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
      }
    }
    .contact_item_time {
      font-size: 24rpx;
      color: #999;
      overflow: hidden;
      .contact_item_icon {
        color: #f59a23;
        padding-right: 10rpx;
        font-size: 28rpx;
      }
    }
    .contact_item_node {
      font-size: 28rpx;
      color: #666;
    }
    .contact_item_speed {
      color: #dd1f36;
    }
    .contact_item_urge {
      color: #f59a23;
    }
    .contact_item_speed,
    .contact_item_urge {
      font-size: 28rpx;
      font-weight: bold;
    }
    .contact_item_status {
      font-size: 24rpx;
      transform: scale(0.83);
      color: #999;
      background-color: #eee;
      padding: 2rpx 10rpx;
      border-radius: 8rpx;
    }
    .contact_item_text {
      font-size: 28rpx;
      color: #333333;
    }
  }
}
</style>
