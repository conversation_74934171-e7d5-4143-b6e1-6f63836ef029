<template>
  <view class="ts-content">
    <page-head
      @clickLeft="returnBack"
      :title="dataSource.title"
      :rightText="!status ? '提交' : ''"
      @clickRight="!status ? submit() : ''"
    ></page-head>
    <view class="contact_list">
      <view class="remark">说明：{{ dataSource.remark }}</view>
      <uni-collapse>
        <uni-collapse-item
          v-for="(person, perIndex) in personList"
          :key="perIndex"
          :open="true"
        >
          <template #titletext>
            <view
              >{{ person.examUserName
              }}<text
                style="color: rgb(0, 102, 255);"
                @click="downloadFile(person.reportdutyUrl)"
                v-if="
                  person.reportdutyUrl != '' &&
                    person.reportdutyUrl != undefined
                "
                >（述职报告）</text
              ></view
            >
          </template>
          <view
            v-for="(questions, questionsIndex) in dataSource.questionsList"
            :key="questionsIndex"
          >
            <view
              class="contact_item collapse_contact_item"
              v-for="(itemQu, eIndex) in questions.questionList"
              :key="eIndex"
            >
              <view>
                <span style="color: red;" v-if="questions.optionRequired"
                  >*</span
                >
                （{{
                  questionsIndex * questions.questionList.length + eIndex + 1
                }}）{{ itemQu.questionTitle }}
              </view>
              <view>
                <radio-group
                  class="radio-group-list"
                  @change="
                    chagne(
                      $event,
                      itemQu,
                      person,
                      questionsIndex,
                      'resultValue',
                      eIndex
                    )
                  "
                >
                  <label
                    class="radio-item"
                    v-for="(radioLable, lableIndex) in itemQu.selectList"
                    :key="lableIndex"
                  >
                    <view>
                      <radio
                        :value="radioLable.label"
                        :checked="
                          radioLable.label ==
                            person.select[questionsIndex].resultValue[eIndex]
                        "
                        :disabled="status"
                      />
                    </view>
                    <view>{{ radioLable.label }}</view>
                  </label>
                </radio-group>
              </view>
              <view
                class="score"
                v-if="
                  person.select[questionsIndex].resultValue[eIndex] != '' &&
                    itemQu.isArray
                "
              >
                <radio-group
                  class="radio-group-list"
                  @change="
                    chagne(
                      $event,
                      itemQu,
                      person,
                      questionsIndex,
                      'resultValueScroe',
                      eIndex
                    )
                  "
                >
                  <label
                    class="radio-item"
                    v-for="(valueRadio, valueIndex) in itemQu.selectList.filter(
                      e =>
                        e.label.indexOf(
                          person.select[questionsIndex].resultValue[eIndex]
                        ) > -1
                    )[0].value"
                    :key="valueIndex"
                  >
                    <view>
                      <radio
                        :value="valueRadio"
                        :checked="
                          valueRadio ==
                            person.select[questionsIndex].resultValueScroe[
                              eIndex
                            ]
                        "
                        :disabled="status"
                      />
                    </view>
                    <view>{{ valueRadio }}</view>
                  </label>
                </radio-group>
              </view>
            </view>
          </view>
        </uni-collapse-item>
      </uni-collapse>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: '我的问卷',
      collapseTextStyle: {
        color: '#333',
        'font-weight': 'bold'
      },
      openValue: ['0'],
      extraIcon: {
        color: '#4cd964',
        size: '26',
        type: 'image'
      },
      masterId: '',
      releaseId: '',
      personList: [],
      dataSource: {},
      status: false,
      submitLoading: false,
      tabIndex: 0
    };
  },
  onLoad(opt) {
    this.masterId = opt.masterId;
    this.releaseId = opt.releaseId || '';
    this.tabIndex = opt.tabIndex || 0;
    if (opt.status) {
      this.status = opt.status == '1' ? true : false;
    }
    this.getQuestionResouce(opt.masterId);
  },
  methods: {
    async getQuestionResouce(masterId) {
      await this.ajax
        .getPreviewQuestionnaire({ masterId: masterId })
        .then(res => {
          let evaluationMaster = res.object.evaluationMaster,
            evaluationSetlist = res.object.evaluationSetlist,
            personList = [],
            questionsList = [],
            source = {},
            reportdutyUrlList = [];
          source.title = evaluationMaster.title;
          source.examUserName = evaluationMaster.examUserName;
          source.remark = evaluationMaster.remark;
          evaluationMaster.reportdutyUrl &&
            (reportdutyUrlList = evaluationMaster.reportdutyUrl.split(','));
          let examUserNameList = evaluationMaster.examUserName.split(',');
          let examUserList = evaluationMaster.examUser.split(',');
          examUserNameList.forEach((item, index) => {
            let obj = {};
            obj.examUser = examUserList[index];
            obj.examUserName = examUserNameList[index];
            obj.reportdutyUrl =
              reportdutyUrlList.length > 0 ? reportdutyUrlList[index] : '';
            obj.select = [];
            personList.push(obj);
          });
          evaluationSetlist.forEach((item, e) => {
            let obj = {};
            obj.optionTitle = item.optionTitle;
            obj.optionRemark = item.optionRemark;
            obj.optionRequired = item.optionRequired;
            obj.optionType = item.optionType;
            obj.setId = item.id;
            obj.questionList = [];
            personList.forEach(per => {
              per.select.push({
                resultValue: [],
                resultValueScroe: [],
                optionRequired: item.optionRequired,
                optionType: item.optionType,
                optionSetId: item.id
              });
            });
            let optionValueList = item.optionValue.split(';'),
              selectOptionList = item.selectOption.split(';'),
              selectScoreList = item.selectScore.split(';'),
              newList = [],
              newName = [],
              indexs = 0;
            selectOptionList.forEach((item, index) => {
              if (item.indexOf('-') > -1) {
                let newOptions = item.split('-');
                if (newName.indexOf(newOptions[0]) > -1) {
                  newList[newName.indexOf(newOptions[0])].value.push(
                    newOptions[1]
                  );
                  newList[newName.indexOf(newOptions[0])].score.push(
                    selectScoreList[index]
                  );
                } else {
                  let obj = {
                    label: newOptions[0],
                    value: [newOptions[1]],
                    score: [selectScoreList[index]],
                    isArray: true
                  };
                  newName.push(newOptions[0]);
                  newList[indexs] = obj;
                  indexs++;
                }
              } else {
                let obj = {
                  label: item,
                  value: item,
                  score: selectScoreList[index],
                  isArray: false
                };
                newName.push(item);
                newList[indexs] = obj;
                indexs++;
              }
            });
            optionValueList.forEach((optionItem, index) => {
              personList.forEach(per => {
                per.select[e].resultValue.push('');
                per.select[e].resultValueScroe.push('');
                if (!this.status) {
                  per.select[e].resultValue[index] = newList[0].label;
                  per.select[e].resultValueScroe[index] = newList[0].value[0];
                }
              });
              let questionItem = {};
              questionItem.questionTitle = optionItem;
              questionItem.selectList = newList;
              questionItem.isArray = newList.length
                ? newList[0].isArray
                : false;
              obj.questionList.push(questionItem);
            });
            questionsList.push(obj);
          });
          source.questionsList = questionsList;
          if (this.status) {
            this.getEvaluationResult();
          }
          this.$nextTick(() => {
            this.dataSource = source;
            this.personList = personList;
            this.$forceUpdate();
          });
        });
    },
    chagne(e, item, person, questionsIndex, type, eIndex) {
      if (type == 'resultValue') {
        item.select = item.selectList.filter(
          item => item.label.indexOf(e.detail.value) > -1
        )[0].value;
        this.$set(
          person.select[questionsIndex].resultValue,
          eIndex,
          e.detail.value
        );
      } else {
        this.$set(
          person.select[questionsIndex].resultValueScroe,
          eIndex,
          e.detail.value
        );
      }
    },
    async getEvaluationResult() {
      await this.ajax
        .selectEvaluationResult({
          releaseId: this.releaseId
        })
        .then(res => {
          if (!res.success) {
            uni.showToast({
              icon: 'none',
              title: '考评结果获取失败!'
            });
            return;
          }
          let resultList = res.object;
          this.dataSource.questionsList.forEach((item, itemIndex) => {
            this.personList.forEach(copyPer => {
              resultList.forEach(result => {
                if (
                  result.setId === copyPer.select[itemIndex].optionSetId &&
                  result.resultUser === copyPer.examUser &&
                  result.resultUserName === copyPer.examUserName
                ) {
                  let list = result.resultValue.split(';');
                  list.forEach((valueScore, vIndex) => {
                    let Srcore = valueScore.split('-');
                    copyPer.select[itemIndex].resultValue[vIndex] = Srcore[0];
                    copyPer.select[itemIndex].resultValueScroe[vIndex] =
                      Srcore[1];
                  });
                }
              });
            });
          });
          this.$nextTick(() => {
            this.$forceUpdate();
          });
        });
    },
    submit() {
      let _self = this;
      if (this.submitLoading) return;
      let submitData = {
        masterId: this.masterId,
        releaseId: this.releaseId,
        resultUser: '',
        resultUserName: '',
        resultValue: '', //选项值
        resultuTypeStr: '',
        setId: ''
      };
      let resultUser = [],
        resultUserName = [],
        resultValue = [],
        setId = [],
        check = true,
        resultTypeStr = [];
      this.dataSource.questionsList.forEach((item, itemIndex) => {
        this.personList.forEach((copyPer, indexs) => {
          setId.push(item.setId);
          resultUser.push(copyPer.examUser);
          resultUserName.push(copyPer.examUserName);
          resultTypeStr.push(item.optionType);
          let isRequired = item.optionRequired;
          let questionRes = [];
          copyPer.select[itemIndex].resultValue.forEach((e, eIndex) => {
            if (
              isRequired &&
              (e == '' ||
                copyPer.select[itemIndex].resultValueScroe[eIndex] == '')
            ) {
              check = false;
            } else {
              let value =
                e + '-' + copyPer.select[itemIndex].resultValueScroe[eIndex];
              questionRes.push(value);
            }
          });
          resultValue.push(questionRes.join(';'));
        });
      });
      if (!check) {
        uni.showToast({
          icon: 'none',
          title: '请完善问卷信息!'
        });
        return;
      }
      this.submitLoading = !this.submitLoading;
      submitData.resultValue = resultValue.join('*');
      submitData.resultUser = resultUser.join('*');
      submitData.resultUserName = resultUserName.join('*');
      submitData.resultuTypeStr = resultTypeStr.join('*');
      submitData.setId = setId.join('*');
      this.ajax.saveEvaluationResult({ ...submitData }).then(res => {
        if (!res.success) {
          uni.showToast({
            icon: 'none',
            title: '提交失败'
          });
          this.submitLoading = !this.submitLoading;
          return;
        }
        uni.showToast({
          icon: 'none',
          title: '提交成功',
          duration: 1500,
          success: function() {
            _self.submitLoading = !_self.submitLoading;
            _self.returnBack();
          }
        });
      });
    },
    async downloadFile(fileId) {
      let filePath = `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/downloadFile/${fileId}`;
      this.$downloadFile.downloadFile(filePath);
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/ts-mobile-oa/pages/performance/index?tabIndex=${this.tabIndex}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .remark {
    background-color: #ffffff;
    padding: 20rpx;
    color: #666;
    font-size: 28rpx;
    border-bottom: 1rpx solid #ccc;
  }
  .contact_item {
    padding: 22rpx 30rpx;
    background-color: #ffffff;
    position: relative;
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      height: 1px;
      background-color: #eee;
      left: 30rpx;
      right: 0;
    }
    &:last-child::after {
      height: 0;
    }
  }
  .score {
    margin: 10px 10px 0px 10px;
    padding: 5px 5px;
    background-color: #afeeee;
    border-radius: 5px;
  }
  .radio-group-list {
    display: flex;
    flex-wrap: wrap;
    .radio-item {
      display: flex;
    }
    /deep/ uni-radio .uni-radio-input {
      width: 16px;
      height: 16px;
    }
  }
  .uni_collapse_cell {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    border-color: #e5e5e5;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    height: 48px;
    .uni_collapse_cell_title {
      padding: 12px 12px;
      position: relative;
      display: flex;
      width: 100%;
      box-sizing: border-box;
      height: 48px;
      line-height: 24px;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .uni_collapse_cell_title_text {
        flex: 1;
        font-size: 14px;
        white-space: nowrap;
        color: inherit;
        overflow: hidden;
        text-overflow: ellipsis;
        color: rgb(51, 51, 51);
        font-weight: bold;
      }
    }
  }
}
</style>
