<template>
  <view class="ts-content">
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :searchInput="true"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <!-- 标签列表-->
        <view
          v-show="showContent"
          class="nav_list"
          style="margin-bottom: 20rpx;background-color: #FFFFFF;"
        >
          <view
            class="nav_list_cell"
            v-for="(item, index) in navList"
            :key="index"
          >
            <view
              class="uni-list-cell-navigate uni-navigate-right"
              @click="goDetailPage(item.path)"
            >
              <view class="item_icon" v-if="item.icon">
                <text
                  :style="{ color: item.color }"
                  class="uni-icons oa-icon"
                  :class="item.icon"
                ></text>
              </view>
              <text class="item_title">{{ item.text }}</text>
            </view>
          </view>
        </view>
        <!-- 标签列表  end-->
        <!-- 数据列表-->
        <view
          class="contact-item"
          v-for="item in dataList"
          :key="item.empCode"
          @tap="chooseItem(item.empCode)"
        >
          <image
            class="iconImg"
            v-if="item.empHeadImg ? true : false"
            :src="`${$config.BASE_HOST}${item.empHeadImg}`"
            mode="aspectFill"
          ></image>
          <view
            v-else
            class="iconImg"
            :class="item.empSex == 0 ? 'sexMan' : 'sexWoman'"
          >
            {{ item.empName.substring(item.empName.length - 2) }}
          </view>
          <view class="userInfo">
            <text>{{ item.empName }}</text>
            <text class="description"
              >{{
                item.empDeptName != 'null' && item.empDeptName
                  ? item.empDeptName
                  : ''
              }}&nbsp;&nbsp;{{
                item.empDutyName && item.empDutyName != 'null'
                  ? item.empDutyName
                  : ''
              }}</text
            >
          </view>
        </view>
        <!-- 数据列表  end-->
      </mescroll>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      showContent: false,
      navList: [
        {
          text: '我的群组',
          color: '#ffffff',
          icon: 'oa-icon-qunzu',
          path: 'collection'
        }
      ],
      dataList: [] //列表数据
    };
  },
  methods: {
    async getListData(page, successCallback, errorCallback, keywords) {
      await this.ajax
        .getEmployeeList({
          pageSize: page.size,
          pageNo: page.num,
          searchKey: keywords,
          sidx: 'a.CREATE_DATE'
        })
        .then(res => {
          let rows = res.rows;
          this.showNavList(keywords);
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(row) {
      this.dataList = this.dataList.concat(row);
    },
    datasInit(keywords) {
      this.showNavList(keywords);
      this.dataList = [];
    },
    showNavList(keywords) {
      if (keywords == '') {
        this.showContent = true;
      } else {
        this.showContent = false;
      }
    },
    chooseItem(empcode) {
      uni.navigateTo({
        url: `/pages/addressBook/personal-info?usercode=${empcode}`
      });
    },
    goDetailPage() {
      uni.navigateTo({
        url: '/pages/addressBook/groups'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  .mescroll-content {
    position: absolute;
    width: 100%;
    bottom: 0;
    top: 0;
    .nav_list {
      margin-bottom: 20rpx;
      .uni-list-cell-navigate {
        justify-content: start;
        padding: 22rpx 30rpx;
      }
      .item_icon {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        margin-right: 20rpx;
        border-radius: 100%;
        background-color: $theme-color;
        .oa-icon {
          font-size: 44rpx;
        }
      }
      .item_title {
        color: #333333;
        font-size: 28rpx;
      }
      &::before,
      &::after {
        height: 0;
      }
    }
    .contact-item {
      padding: 22rpx 30rpx;
      display: flex;
      align-items: center;
      background-color: #ffffff;
      .userInfo {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 1;
        color: #333333;
        .description {
          color: #999999;
          font-weight: normal;
          font-size: 28rpx;
        }
      }
      .iconImg {
        width: 80rpx;
        height: 80rpx;
        margin-right: 20rpx;
        border-radius: 100%;
        color: #ffffff;
        text-align: center;
        line-height: 80rpx;
        font-size: 28rpx;
      }
      .sexMan {
        background-color: $sexman-color;
      }
      .sexWoman {
        background-color: $sexwoman-color;
      }
    }
  }
}
</style>
