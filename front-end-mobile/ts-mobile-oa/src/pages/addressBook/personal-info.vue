<template>
  <view class="ts-content" v-if="showContent">
    <page-head title="个人信息" @clickLeft="returnBack"></page-head>
    <view class="person-info-top">
      <image
        class="iconImg"
        :src="
          info.empHeadImg
            ? `${$config.BASE_HOST}${info.empHeadImg}`
            : '../../static/img/headImg.png'
        "
        mode="aspectFill"
      >
      </image>
      <view class="person-info-top-text">
        <view>
          <text class="name">{{ info.empName }}</text>
          <text
            class="oa-icon sex-icon"
            :class="
              info.empSex == 0 ? 'sex-man oa-icon-nan' : 'sex-woman oa-icon-nv'
            "
          ></text>
        </view>
        <view class="description">
          {{
            info.empDeptName != 'null' && info.empDeptName
              ? info.empDeptName
              : ''
          }}
          &nbsp;&nbsp;
          {{
            info.empDutyName && info.empDutyName != 'null'
              ? info.empDutyName
              : ''
          }}
        </view>
      </view>
      <view class="top-right-icon">
        <text class="oa-icon oa-icon-fasong" @tap="sendInsideEmail"></text>
      </view>
    </view>
    <view class="person-info-content">
      <view class="info-list">
        <view class="info-item">
          <view class="info-item-lable">邮箱</view>
          <view class="info-item-val">
            <text class="val-text">{{
              info.empEmail ? info.empEmail : '未填写邮箱信息'
            }}</text>
            <text
              class="oa-icon oa-icon-weiduyoujian right-icon"
              v-if="info.empEmail"
              @tap="sendOutsideEmail(info.empEmail)"
            ></text>
          </view>
        </view>
        <view class="info-item">
          <view class="info-item-lable">手机号码</view>
          <view class="info-item-val" v-if="info.empPhone">
            <text class="val-text">{{ info.empPhone }}</text>
            <text
              v-if="info.empPhone && info.empPhone.indexOf('*') != -1"
              class="privacy-tips"
              >已开启隐私保护</text
            >
            <text
              v-else
              class="oa-icon oa-icon-dianhua1 right-icon"
              @tap="call(info.empPhone)"
            ></text>
          </view>
          <view class="info-item-val" v-if="info.empPhoneSecond">
            <text class="val-text">{{ info.empPhoneSecond }}</text>
            <text
              v-if="
                info.empPhoneSecond && info.empPhoneSecond.indexOf('*') != -1
              "
              class="privacy-tips"
              >已开启隐私保护</text
            >
            <text
              v-else
              class="oa-icon oa-icon-dianhua1 right-icon"
              @tap="call(info.empPhoneSecond)"
            ></text>
          </view>
        </view>
        <view
          class="info-item"
          v-if="
            info.empBusinessPhone ||
              info.empUnicomBusinessPhone ||
              info.empTelecomBusinessPhone
          "
        >
          <view class="info-item-lable">手机短号</view>
          <view class="info-item-val" v-if="info.empBusinessPhone">
            <image
              class="phone-icon"
              src="../../static/img/chinaMobile.png"
              mode="aspectFit"
            ></image>
            <text class="val-text">{{ info.empBusinessPhone }}</text>
            <text
              class="oa-icon oa-icon-dianhua1 right-icon"
              @tap="call(info.empBusinessPhone)"
            ></text>
          </view>
          <view class="info-item-val" v-if="info.empUnicomBusinessPhone">
            <image
              class="phone-icon"
              src="../../static/img/chinaUnicom.png"
              mode="aspectFit"
            ></image>
            <text class="val-text">{{ info.empUnicomBusinessPhone }}</text>
            <text
              class="oa-icon oa-icon-dianhua1 right-icon"
              @tap="call(info.empUnicomBusinessPhone)"
            ></text>
          </view>
          <view class="info-item-val" v-if="info.empTelecomBusinessPhone">
            <image
              class="phone-icon"
              src="../../static/img/chinaTelecom.png"
              mode="aspectFit"
            ></image>
            <text class="val-text">{{ info.empTelecomBusinessPhone }}</text>
            <text
              class="oa-icon oa-icon-dianhua1 right-icon"
              @tap="call(info.empTelecomBusinessPhone)"
            ></text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showContent: false,
      info: {},
      collectId: ''
    };
  },
  onLoad(prama) {
    this.getUserInfo(prama.usercode);
  },
  methods: {
    //获取联系人信息
    getUserInfo(empcode) {
      this.ajax
        .getPersonalInformationSettings({
          userCode: empcode,
          isHidden: 1
        })
        .then(res => {
          let data = res.object;
          this.info = data;
          this.showContent = true;
        });
    },
    sendInsideEmail() {
      //发邮件
      let personList = [
        {
          id: this.info.empCode,
          name: this.info.empName,
          empFirstName: this.info.empName.substring(
            this.info.empName.length - 2
          ),
          empHeadImg: this.info.empHeadImg,
          empDeptName: this.info.empDeptName,
          empDutyName: this.info.empDutyName,
          empSex: this.info.empSex,
          choose: true
        }
      ];
      uni.setStorageSync('person_list', JSON.stringify(personList));
      uni.navigateTo({
        url: `/pages/email/email-send?index=5&usercode=${this.info.empCode}&username=${this.info.empName}&fromPage=addressBook`
      });
    },
    call(phoneNumber) {
      uni.makePhoneCall({
        phoneNumber: phoneNumber
      });
    },
    sendOutsideEmail(mail) {
      uni.navigateTo({
        url: `/pages/email/out-email-send?fromPage=address&index=5&mail=${mail}&usercode=${this.info.empCode}`
      });
    },
    returnBack() {
      // uni.switchTab({
      //   url: 'address-book'
      // });
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/address-book'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  overflow: hidden;
  .person-info-top {
    padding: 20rpx 30rpx;
    display: flex;
    align-items: center;
    background-color: #ffffff;
    margin-bottom: 20rpx;
    .person-info-top-text {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      color: #333333;
      font-size: 36rpx;
      .name {
        margin-right: 20rpx;
      }
      .sex-icon {
        font-size: 32rpx;
      }
      .sex-man {
        color: $sexman-color;
      }
      .sex-woman {
        color: $sexwoman-color;
      }
      .description {
        font-size: 28rpx;
        color: #999;
      }
    }
    .top-right-icon {
      .oa-icon {
        color: #999;
        font-size: 40rpx;
      }
    }
    .iconImg {
      width: 120rpx;
      height: 120rpx;
      margin-right: 20rpx;
      border-radius: 100%;
    }
  }
  .person-info-content {
    .info-list {
      background-color: #ffffff;
      margin-bottom: 20rpx;
      .info-item {
        padding: 20rpx 30rpx;
        position: relative;
        &::after {
          position: absolute;
          z-index: 10;
          right: 0;
          bottom: 0;
          left: 0;
          height: 1px;
          content: '';
          -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
          background-color: #eeeeee;
        }
        &:last-child {
          &::after {
            height: 0;
          }
        }
        .info-item-lable {
          color: #333333;
          font-size: 32rpx;
          margin-bottom: 20rpx;
        }
        .info-item-val {
          color: #666666;
          font-size: 32rpx;
          word-break: break-all;
          margin-bottom: 20rpx;
          display: flex;
          align-items: center;
        }
        .val-text {
          flex: 1;
        }
        .privacy-tips {
          color: #999999;
          font-size: 24rpx;
        }
        .phone-icon {
          width: 44rpx;
          height: 44rpx;
          margin-right: 20rpx;
        }
        .right-icon {
          font-size: 44rpx;
          color: #999999;
        }
      }
    }
  }
}
</style>
