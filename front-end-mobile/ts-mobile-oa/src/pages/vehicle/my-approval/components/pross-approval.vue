<template>
  <view class="ts-content">
    <page-head :title="title" @clickLeft="returnBack"></page-head>

    <base-form
      class="base-form"
      ref="baseForm"
      :form-list="formList"
      :form-data.sync="form"
      :rules="rules"
      :showSubmitButton="false"
    />

    <view class="action-content">
      <view class="action-item" @click="close">取消</view>

      <view class="action-item submit-btn" @click="submit">
        提交
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      id: '',
      taskId: '',
      type: '',

      formList: [
        {
          title: '备注',
          prop: 'approvalRemark',
          type: 'textarea',
          placeholder: '请输入',
          maxlength: 500
        }
      ],
      form: {},
      rules: {}
    };
  },
  async onLoad({ id, taskId, type = '' }) {
    this.id = id;
    this.taskId = taskId;
    this.type = type;

    this.form = {};

    switch (type) {
      case 'Pass':
        this.title = '同意';
        break;
      case 'Back':
        this.title = '退回';
        break;
    }
  },
  methods: {
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        this.$parentTypeFun({
          type: 'jumpPage',
          path: '/workbench'
        });
      } else {
        uni.navigateBack();
      }
    },
    submit() {
      let data = Object.assign(
        {
          id: this.id,
          taskId: this.taskId
        },
        this.form
      );

      this.ajax[
        this.title == '同意'
          ? 'handleVehicleApprovalPass'
          : 'handleVehicleApprovalReject'
      ](data).then(res => {
        if (!res.success) {
          uni.showToast({
            title: res.message || '审批失败!',
            duration: 2000,
            icon: 'none'
          });
          return;
        }
        uni.showToast({
          title: res.message || '审批成功!',
          duration: 2000,
          icon: 'none'
        });
        setTimeout(() => {
          uni.reLaunch({
            //关闭当前页面，跳转到应用内的某个页面。
            url: '/pages/vehicle/my-approval/index?fromPage=workBench&index=0'
          });
        }, 2000);
      });
    },
    close() {
      let pages = getCurrentPages();
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #fff;
  position: relative;
  padding-bottom: 40px;
  .action-content {
    position: absolute;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    box-shadow: 0 -1px 6px #ccc;
    width: 100%;
    height: 40px;
    .action-item {
      flex: 1;
      text-align: center;
      color: $uni-text-color-grey;
      position: relative;
      &:not(:last-child)::after {
        content: ' ';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 15px;
        border-right: 1px solid #eee;
      }
      &.submit-btn {
        color: $theme-color;
      }
    }
  }
}
</style>
