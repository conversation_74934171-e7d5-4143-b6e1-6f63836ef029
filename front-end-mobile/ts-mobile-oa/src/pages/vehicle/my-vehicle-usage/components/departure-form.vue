<template>
  <view class="ts-content">
    <view class="form-content">
      <page-head title="出车" @clickLeft="returnBack"></page-head>
      <base-form
        class="base-form"
        ref="baseForm"
        :form-list="formList"
        :form-data.sync="form"
        :rules="rules"
        :showSubmitButton="false"
      />
    </view>

    <view class="action-content">
      <view class="action-item submit-btn" @click="submit">
        确定
      </view>
      <view class="action-item" @click="close">取消</view>
    </view>
  </view>
</template>

<script>
import BaseForm from '@/components/base-form/base-form.vue';
export default {
  components: {
    BaseForm
  },
  data() {
    return {
      formList: [
        {
          title: '是否出车',
          prop: 'outType',
          type: 'radio',
          radioList: [
            {
              label: '出车',
              value: '1'
            },
            {
              label: '不出车',
              value: '2'
            }
          ],
          placeholder: '请选择',
          required: true
        },
        {
          title: '备注',
          prop: 'outRemark',
          type: 'textarea',
          placeholder: '请输入',
          required: false,
          maxlength: 500
        }
      ],
      form: {
        outType: '',
        outRemark: '',
        id: ''
      },
      rules: {
        outType: [
          {
            required: true,
            message: '请选择是否出车',
            trigger: ''
          }
        ],
        outRemark: [
          {
            required: false,
            message: '请输入备注',
            trigger: ''
          }
        ]
      }
    };
  },
  onLoad({ id }) {
    this.form.outType = '1';
    this.form.id = id;
  },
  watch: {
    'form.outType'(val) {
      this.formList.find(item => item.prop === 'outRemark').required = val == 2;
      this.rules.outRemark[0].required = val == 2;
    }
  },
  methods: {
    returnBack() {
      let pages = getCurrentPages();
      if (pages.length === 1) {
        this.$parentTypeFun
          ? this.$parentTypeFun({
              type: 'jumpPage',
              path: '/workbench'
            })
          : history.back();
      } else {
        uni.navigateBack();
      }
    },
    async submit() {
      const result = await this.$refs.baseForm.validate();
      if (!result) {
        return;
      }

      const data = Object.assign({}, this.form);
      const res = await this.ajax.handleOutVehicle(data);
      if (!res.success) {
        uni.showToast({
          title: res.message || '出车失败!',
          duration: 2000,
          icon: 'none'
        });
        return;
      }
      uni.showToast({
        title: '出车成功',
        duration: 2000,
        icon: 'none'
      });
      setTimeout(() => {
        uni.reLaunch({
          url:
            '/pages/vehicle/my-vehicle-usage/index?fromPage=workBench&index=0'
        });
      }, 2000);
    },
    close() {
      let pages = getCurrentPages();
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100vh;
  background: #fff;
  position: relative;
  .form-content {
    height: calc(100% - 42px);
    overflow: auto;
    .item-title {
      padding: 8px;
      padding-left: 14px;
      color: #333;
      position: relative;
      &::before {
        position: absolute;
        left: 6px;
        top: 14px;
        content: '';
        width: 4px;
        height: 14px;
        background-color: $theme-color;
      }
      /deep/ .u-checkbox {
        margin-left: 8px;
      }
    }
    /deep/ .base-form {
      height: auto;
      .u-form-item--right__content {
        width: 100%;
        height: 100%;
        .u-form-item--right__content__icon {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .action-content {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    box-shadow: 0 -1px 6px #ccc;
    width: 100%;
    height: 40px;
    .action-item {
      flex: 1;
      text-align: center;
      color: $uni-text-color-grey;
      position: relative;
      &:not(:last-child)::after {
        content: ' ';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 15px;
        border-right: 1px solid #eee;
      }
      &.submit-btn {
        color: $theme-color;
      }
    }
  }
}
</style>
