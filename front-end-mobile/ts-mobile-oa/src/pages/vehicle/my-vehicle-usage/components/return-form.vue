<template>
  <view class="ts-content">
    <view class="form-content">
      <page-head title="还车" @clickLeft="returnBack"></page-head>
      <base-form
        class="base-form"
        ref="baseForm"
        :form-list="formList"
        :form-data.sync="form"
        :rules="rules"
        :showSubmitButton="false"
      >
        <template v-slot:totalPrice>
          <view style="text-align: right;color: #333;">
            <u-input v-model="totalPrice" input-align="right"></u-input>
          </view>
        </template>
        <template v-slot:currentDistance>
          <view style="text-align: right;color: #333;">
            <u-input
              v-model="form.currentDistance"
              input-align="right"
            ></u-input>
          </view>
        </template>
      </base-form>

      <view class="item-title">
        加油记录
        <u-checkbox v-model="form.isRefuel" />
      </view>

      <view v-show="form.isRefuel">
        <base-form
          class="base-form"
          ref="refuelForm"
          :form-list="refuelList"
          :form-data.sync="refuelForm"
          :rules="refuelRules"
          :showSubmitButton="false"
          @select-oilCard-callback="handleSelectOilCardCallback"
        />
      </view>

      <view class="item-title">
        维修记录
        <u-checkbox v-model="form.isMaintenance" />
      </view>
      <view v-show="form.isMaintenance">
        <base-form
          class="base-form"
          ref="maintenanceForm"
          :form-list="maintenanceList"
          :form-data.sync="maintenanceForm"
          :rules="maintenanceRules"
          :showSubmitButton="false"
        />
      </view>
    </view>

    <view class="action-content">
      <view class="action-item submit-btn" @click="submit">
        确定
      </view>
      <view class="action-item" @click="close">取消</view>
    </view>
    <search-select ref="SearchSelect" />
  </view>
</template>

<script>
import indexJs from './index';
import BaseForm from '@/components/base-form/base-form.vue';
import SearchSelect from '@/components/search-select/search-select.vue';

export default {
  mixins: [indexJs],
  components: {
    BaseForm,
    SearchSelect
  },
  data() {
    return {
      personlist: []
    };
  },
  methods: {
    returnBack() {
      let pages = getCurrentPages();
      if (pages.length === 1) {
        this.$parentTypeFun
          ? this.$parentTypeFun({
              type: 'jumpPage',
              path: '/workbench'
            })
          : history.back();
      } else {
        uni.navigateBack();
      }
    },
    async handleSelectOilCardCallback(item) {
      let optionList = [];
      switch (item.propVal) {
        case 'oilCard':
          let res = await this.ajax.getVehicleOilList({});
          if (res.success == false) {
            uni.showToast({
              title: res.message || '油卡数据获取失败',
              icon: 'none'
            });
            return false;
          }

          optionList = res.map(item => {
            let price = Number(item.oilPrice);
            isNaN(price) && (price = 0);
            return {
              id: item.id,
              oilNo: item.oilNo,
              price,
              name: `${item.oilNo} - (余额:${item.oilPrice}-${item.oilOrgName})`
            };
          });
          this.oilCardList = optionList;
          break;
      }

      this.$refs.SearchSelect.open({
        optionList,
        defaultVal: this.refuelForm[item.propVal],
        callback: select => {
          this.$set(this.refuelForm, item.prop, select.name.split('-')[0]);
          this.$set(this.refuelForm, item.propVal, select.id);
          this.$forceUpdate();
        }
      });
    },
    async submit() {
      let validateForms = [this.$refs.baseForm.validate()];
      this.form.isRefuel &&
        validateForms.push(this.$refs.refuelForm.validate());
      this.form.isMaintenance &&
        validateForms.push(this.$refs.maintenanceForm.validate());

      let validateList = await Promise.all(validateForms),
        validateRes = validateList.every(item => item == true);
      if (validateRes) {
        this.$set(this.form, 'vehicleApplyRefuel', this.refuelForm);
        this.$set(this.form, 'vehicleApplyMaintenance', this.maintenanceForm);
        this.$set(this.form, 'totalPrice', this.totalPrice);
      }

      const data = Object.assign({}, this.form);
      data.id = this.id;
      data.isRefuel = data.isRefuel ? 1 : 0;
      data.isMaintenance = data.isMaintenance ? 1 : 0;
      delete data.returnFiles;
      delete data.vehicleApplyRefuel.refuelFilesList;
      delete data.vehicleApplyMaintenance.maintenanceFilesList;
      this.ajax.handleReturnVehicle(data).then(res => {
        if (!res.success) {
          uni.showToast({
            title: res.message || '还车失败!',
            duration: 2000,
            icon: 'none'
          });
          return;
        }
        uni.showToast({
          title: '还车成功',
          duration: 2000,
          icon: 'none'
        });
        setTimeout(() => {
          uni.reLaunch({
            url:
              '/pages/vehicle/my-vehicle-usage/index?fromPage=workBench&index=0'
          });
        }, 2000);
      });
    },
    close() {
      let pages = getCurrentPages();
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100vh;
  background: #fff;
  position: relative;
  .form-content {
    height: calc(100% - 42px);
    overflow: auto;
    .item-title {
      padding: 8px;
      padding-left: 14px;
      color: #333;
      position: relative;
      &::before {
        position: absolute;
        left: 6px;
        top: 14px;
        content: '';
        width: 4px;
        height: 14px;
        background-color: $theme-color;
      }
      /deep/ .u-checkbox {
        margin-left: 8px;
      }
    }
    /deep/ .base-form {
      height: auto;
      .u-form-item--right__content {
        width: 100%;
        height: 100%;
        .u-form-item--right__content__icon {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .action-content {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    box-shadow: 0 -1px 6px #ccc;
    width: 100%;
    height: 40px;
    .action-item {
      flex: 1;
      text-align: center;
      color: $uni-text-color-grey;
      position: relative;
      &:not(:last-child)::after {
        content: ' ';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 15px;
        border-right: 1px solid #eee;
      }
      &.submit-btn {
        color: $theme-color;
      }
    }
  }
}
</style>
