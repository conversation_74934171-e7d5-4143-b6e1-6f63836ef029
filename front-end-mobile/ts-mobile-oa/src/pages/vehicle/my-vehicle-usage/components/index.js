import common from '@/common/js/common.js';
export default {
  data() {
    return {
      oilCardList: [],
      oilTypeList: [],
      totalMileage: undefined,
      id: undefined,

      fromPage: '',
      formList: [
        {
          title: '还车时里程数',
          prop: 'distance',
          type: 'number',
          mode: 'input',
          placeholder: '请输入还车时里程数（KM）',
          labelWidth: 220,
          required: true,
          blurCallback: this.handleDistanceBlur,
          callback: (e, item) => {
            return this.handleInput(e, item);
          }
        },
        {
          title: '本次行驶里程',
          prop: 'currentDistance',
          labelWidth: 220,
          rightSlot: 'currentDistance',
          rightSlotStyle: {
            width: '100%',
            height: '100%',
            background: '#eee'
          }
        },
        {
          title: '高速费用',
          prop: 'highSpeedPrice',
          type: 'number',
          mode: 'input',
          placeholder: '请输入高速费用（元）',
          callback: (e, item) => {
            return this.handleInput(e, item);
          }
        },
        {
          title: '停车费用',
          prop: 'stopPrice',
          type: 'number',
          mode: 'input',
          placeholder: '请输入停车费用（元）',
          callback: (e, item) => {
            return this.handleInput(e, item);
          }
        },
        {
          title: '过桥费用',
          prop: 'bridgePrice',
          type: 'number',
          mode: 'input',
          placeholder: '请输入过桥费用（元）',
          callback: (e, item) => {
            return this.handleInput(e, item);
          }
        },
        {
          title: '其他费用',
          prop: 'otherPrice',
          type: 'number',
          mode: 'input',
          placeholder: '请输入其他费用（元）',
          callback: (e, item) => {
            return this.handleInput(e, item);
          }
        },
        {
          title: '总费用',
          prop: 'totalPrice',
          labelWidth: 220,
          rightSlot: 'totalPrice',
          rightSlotStyle: {
            width: '100%',
            height: '100%',
            background: '#eee'
          }
        },
        {
          title: '备注',
          prop: 'returnRemark',
          type: 'textarea',
          placeholder: '请输入',
          maxlength: 500
        },
        {
          title: '附件',
          prop: 'returnFiles',
          propVal: 'returnFilesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件'
        }
      ],
      form: {
        distance: '',
        currentDistance: '',
        highSpeedPrice: '',
        stopPrice: '',
        bridgePrice: '',
        otherPrice: '',
        totalPrice: '',
        returnRemark: '',

        returnFiles: common.guid(),
        returnFilesList: [],

        isRefuel: false,
        isMaintenance: false
      },
      rules: {
        distance: [
          {
            required: true,
            message: '请输入还车时里程数',
            trigger: ''
          }
        ],
        currentDistance: [
          {
            required: true,
            message: '请输入本次行驶里程',
            trigger: ''
          }
        ]
      },
      refuelList: [
        {
          title: '加油日期',
          prop: 'refuelDate',
          type: 'select',
          mode: 'time',
          placeholder: '请选择加油日期',
          required: true
        },
        {
          title: '加油升数',
          prop: 'refuelLitre',
          type: 'number',
          mode: 'input',
          placeholder: '请输入加油升数(升)',
          callback: (e, item) => {
            return this.handleInput(e, item);
          },
          required: true
        },
        {
          title: '支付方式',
          prop: 'refuelPayName',
          propVal: 'refuelPay',
          type: 'select',
          mode: 'select',
          optionList: [],
          placeholder: '请选择支付方式',
          required: true
        },
        {
          title: '加油类型',
          prop: 'refuelTypeName',
          propVal: 'refuelType',
          type: 'select',
          mode: 'select',
          optionList: [
            {
              label: '#92',
              value: '#92'
            },
            {
              label: '#95',
              value: '#95'
            },
            {
              label: '柴油',
              value: '柴油'
            },
            {
              label: '其他',
              value: '其他'
            }
          ],
          placeholder: '请选择加油类型',
          required: true
        },
        {
          title: '现金费用',
          prop: 'cashPrice',
          type: 'number',
          mode: 'input',
          placeholder: '请输入费用（元）',
          callback: (e, item) => {
            return this.handleInput(e, item);
          }
        },
        {
          title: '附件',
          prop: 'refuelFiles',
          propVal: 'refuelFilesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件'
        }
      ],
      refuelForm: {
        refuelDate: '',
        refuelLitre: '',
        refuelPay: '',
        refuelType: '',
        oilCard: '',
        refuelPrice: '',
        cashPrice: '',
        refuelFiles: common.guid(),
        refuelFilesList: []
      },
      refuelRules: {
        refuelDate: [
          {
            required: true,
            message: '请选择加油日期',
            trigger: ''
          }
        ],
        refuelLitre: [
          {
            required: true,
            message: '请输入加油升数',
            trigger: ''
          }
        ],
        refuelPayName: [
          {
            required: true,
            message: '请选择支付方式',
            trigger: ''
          }
        ],
        refuelTypeName: [
          {
            required: true,
            message: '请选择加油类型',
            trigger: ''
          }
        ],
        oilCardName: [
          {
            required: true,
            message: '请选择油卡卡号',
            trigger: ''
          }
        ],
        refuelPrice: [
          {
            required: true,
            message: '请输入油卡费用',
            trigger: ''
          }
        ]
      },
      maintenanceList: [
        {
          title: '维修日期',
          prop: 'maintenanceDate',
          type: 'select',
          mode: 'time',
          placeholder: '请选择维修日期',
          required: true
        },
        {
          title: '费用',
          prop: 'maintenancePrice',
          type: 'number',
          mode: 'input',
          required: true,
          placeholder: '请输入费用（元）',
          callback: (e, item) => {
            return this.handleInput(e, item);
          }
        },
        {
          title: '维修情况',
          prop: 'maintenanceRemark',
          type: 'textarea',
          placeholder: '请输入',
          required: true,
          maxlength: 500
        },
        {
          title: '附件',
          prop: 'maintenanceFiles',
          propVal: 'maintenanceFilesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件'
        }
      ],
      maintenanceForm: {
        maintenanceDate: '',
        maintenancePrice: '',
        maintenanceRemark: '',
        maintenanceFiles: common.guid(),
        maintenanceFilesList: []
      },
      maintenanceRules: {
        maintenanceDate: [
          {
            required: true,
            message: '请选择加油日期',
            trigger: ''
          }
        ],
        maintenancePrice: [
          {
            required: true,
            message: '请输入加油升数',
            trigger: ''
          }
        ],
        maintenanceRemark: [
          {
            required: true,
            message: '请输入维修情况',
            trigger: ''
          }
        ]
      }
    };
  },
  onLoad({ id, totalMileage }) {
    this.id = id;
    this.totalMileage = totalMileage;
    // 获取加油类型
    this.ajax.getDataByDataLibrary('VEHICLE_OIL_PTY_TYPE').then(res => {
      if (!res.success) {
        return;
      }
      let row = res.object || [];
      this.oilTypeList = row.map(item => {
        return {
          label: item.itemName,
          value: item.itemNameValue
        };
      });

      this.refuelList.find(
        item => item.propVal === 'refuelPay'
      ).optionList = this.oilTypeList;
    });
  },
  watch: {
    'refuelForm.oilCard'(val) {
      let item = this.oilCardList.find(card => card.id == val) || {};
      this.maxOilPrice = item.price;
      this.$set(this.refuelForm, 'refuelId', item.id || '');
      this.$set(this.refuelForm, 'refuelNo', item.oilNo || '');

      if (this.refuelForm.refuelPrice > item.price) {
        this.$set(this.refuelForm, 'refuelPrice', item.price || '');
      }
    },
    'refuelForm.refuelPay'(val) {
      let isOilSelectFormItem = [
        {
          title: '油卡卡号',
          prop: 'oilCardName',
          propVal: 'oilCard',
          type: 'select',
          mode: 'callBack',
          callBackName: 'select-oilCard-callback',
          placeholder: '请选择',
          required: true
        },
        {
          title: '油卡费用',
          prop: 'refuelPrice',
          type: 'number',
          mode: 'input',
          placeholder: '请输入油卡费用（元）',
          required: true,
          callback: (e, item) => {
            return this.handleInput(e, item, this.maxOilPrice);
          }
        }
      ];

      let ishasOilItem = this.refuelList.filter(
        item => item.propVal === 'oilCard' || item.prop === 'refuelPrice'
      );

      if ([2, 4].includes(Number(val))) {
        if (ishasOilItem && ishasOilItem.length === 0) {
          this.refuelList.splice(3, 0, ...isOilSelectFormItem);
        }
      } else {
        delete this.refuelForm.oilCard;
        delete this.refuelForm.refuelId;
        delete this.refuelForm.refuelNo;
        delete this.refuelForm.refuelPrice;

        if (ishasOilItem && ishasOilItem.length) {
          this.refuelList.splice(3, 2);
        }
      }
    }
  },
  computed: {
    totalPrice() {
      let {
          highSpeedPrice = 0,
          stopPrice = 0,
          bridgePrice = 0,
          otherPrice = 0
        } = this.form,
        dataList = [highSpeedPrice, stopPrice, bridgePrice, otherPrice];

      return (
        dataList.reduce((prev, next) => {
          let num = Number(next);
          if (!isNaN(num)) {
            prev += num * 100;
          }
          return prev;
        }, 0) / 100
      ).toFixed(2);
    }
  },
  methods: {
    handleDistanceBlur() {
      const totalMileage = isNaN(this.totalMileage)
        ? 0
        : Number(this.totalMileage);

      if (this.form.distance < totalMileage) {
        uni.showToast({
          title: `不得小于车辆总里程数${totalMileage}KM`,
          duration: 2000,
          icon: 'none'
        });
        this.$set(this.form, 'distance', '');
        return;
      }

      const currentDistance = (
        (this.form.distance * 100 - totalMileage * 100) /
        100
      ).toFixed(2);

      this.$set(this.form, 'currentDistance', currentDistance);
    },
    handleInput(e, item, maxNumber) {
      let newVal = common.inputTowDecimalPlaces(e);
      if (maxNumber && newVal > maxNumber) {
        newVal = maxNumber;
      }
      return newVal;
    }
  }
};
