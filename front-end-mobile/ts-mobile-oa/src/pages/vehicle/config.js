export const applyResult = {
  1: '预约中',
  2: '同意',
  3: '退回',
  4: '已撤销',
  // 5: '已过期',
  6: '补录用车',
  7: '紧急派车',
  8: '不派车',
  9: '重新派车'
};

export let applyResultColor = {
  1: '#5260FF',
  2: '#333',
  3: '#FF0000',
  4: '#999999',
  5: '#999999',
  6: '#333',
  7: '#333',
  8: '#FF0000',
  9: '#5260FF'
};

/**@desc 车辆预约结果 */
export const vehicleApprovalResultStatus = Object.keys(applyResult).map(key => {
  return {
    label: applyResult[key],
    value: key,
    element: 'ts-option'
  };
});

export const applyStatus = {
  1: '待审批',
  2: '待派车',
  3: '待出车',
  4: '待还车',
  5: '已完结'
};

/**@desc 车辆预约状态 */
export const vehicleApprovalStatus = Object.keys(applyStatus).map(key => {
  return {
    label: applyStatus[key],
    value: key,
    element: 'ts-option'
  };
});

export const driverStatus = {
  1: '空闲',
  2: '出车',
  3: '休息'
};

export let driverStatusColor = {
  1: '#62B1C9',
  2: '#F4BA40',
  3: '#eee'
};

export const driverStatusList = [];
for (const key in vehicleStatus) {
  const item = vehicleStatus[key];

  driverStatusList.push({
    label: item,
    value: key,
    element: 'ts-option'
  });
}

export const vehicleStatus = {
  1: '在库',
  2: '预约',
  3: '在用',
  4: '借用',
  5: '报废'
};

export let vehicleStatusColor = {
  1: '#62B1C9',
  2: '#5260FF',
  3: '#F4BA40',
  4: '#eee',
  5: '#333'
};

export const vehicleStateList = [];
for (const key in vehicleStatus) {
  const item = vehicleStatus[key];

  vehicleStateList.push({
    label: item,
    value: key,
    element: 'ts-option'
  });
}
