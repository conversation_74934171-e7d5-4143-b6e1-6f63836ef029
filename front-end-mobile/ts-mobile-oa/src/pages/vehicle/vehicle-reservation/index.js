import common from '@/common/js/common.js';
export default {
  data() {
    return {
      fromPage: '',
      formList: [
        {
          title: '预约车辆',
          prop: 'vehicleNo',
          propVal: 'vehicleId',
          type: 'select',
          mode: 'callBack',
          callBackName: 'select-callback',
          placeholder: '请选择',
          required: true
        },
        {
          title: '用车原因',
          prop: 'applyReason',
          type: 'textarea',
          placeholder: '请输入',
          maxlength: 50,
          required: true
        },
        {
          title: '原因是否公开',
          prop: 'applyReasonPublic',
          type: 'switch',
          placeholder: '',
          switchLabel: '公开'
        },
        {
          title: '是否需要司机',
          prop: 'applyDriver',
          type: 'switch',
          placeholder: '',
          switchLabel: '需要'
        },
        {
          title: '用车人',
          prop: 'applyUserName',
          propVal: 'applyUser',
          type: 'select',
          mode: 'callBack',
          callBackName: 'select-person-callback',
          placeholder: '请选择用车人',
          required: true
        },
        {
          title: '用车时间',
          prop: 'applyTime',
          propVal: 'applyTimeVal',
          type: 'select',
          mode: 'range-picker',
          format: 'YYYY-MM-DD HH:mm',
          placeholder: '请选择',
          required: true
        },
        {
          title: '用车线路',
          prop: 'applyLine',
          propVal: 'applyLine',
          type: 'text',
          placeholder: '请输入用车线路',
          maxlength: 100,
          required: true
        },
        {
          title: '用车联系人',
          prop: 'applyContact',
          propVal: 'applyContact',
          type: 'text',
          placeholder: '请输入用车联系人',
          maxlength: 10,
          required: true
        },
        {
          title: '联系人手机',
          prop: 'applyContactPhone',
          propVal: 'applyContactPhone',
          type: 'text',
          placeholder: '请输入联系人手机',
          maxlength: 11,
          required: true
        },
        {
          title: '联系人部门',
          prop: 'applyContactDept',
          propVal: 'applyContactDept',
          type: 'text',
          placeholder: '请输入联系人部门',
          maxlength: 10
        },
        {
          title: '备注',
          prop: 'applyRemark',
          type: 'textarea',
          placeholder: '请输入',
          maxlength: 500
        },
        {
          title: '附件',
          prop: 'applyFiles',
          propVal: 'applyFilesList',
          form: {},
          action: '',
          type: 'file',
          mode: 'file',
          placeholder: '上传附件'
        }
      ],
      form: {
        vehicleNo: '',
        vehicleId: '',

        applyReason: '',
        applyReasonPublic: true,
        applyDriver: true,
        applyUserName: '',
        applyUser: '',

        applyTime: '',
        applyTimeVal: [],

        applyLine: '',
        applyContact: '',
        applyContactDept: '',
        applyContactPhone: '',
        applyRemark: '',
        applyFiles: common.guid(),
        applyFilesList: []
      },
      rules: {
        vehicleNo: [
          {
            required: true,
            message: '请选择预约车辆',
            trigger: ''
          }
        ],
        applyReason: [
          {
            required: true,
            message: '请输入用车原因',
            trigger: ''
          }
        ],
        applyUserName: [
          {
            required: true,
            message: '请选择用车人',
            trigger: ''
          }
        ],
        applyTime: [
          {
            required: true,
            message: '请选择用车时间',
            trigger: ''
          }
        ],
        applyLine: [
          {
            required: true,
            message: '请输入用车线路',
            trigger: ''
          }
        ],
        applyContact: [
          {
            required: true,
            message: '请输入用车联系人',
            trigger: ''
          }
        ],
        applyContactPhone: [
          {
            required: true,
            message: '请输入用车联系人手机',
            trigger: ''
          },
          {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
              // 上面有说，返回true表示校验通过，返回false表示不通过
              // this.$u.test.mobile()就是返回true或者false的
              return this.$u.test.mobile(value);
            },
            message: '用车联系人手机号码不正确',
            trigger: ''
          }
        ]
      }
    };
  },
  async onLoad(opt) {
    const userInfo = this.$store.state.userInfo;
    this.form.applyUser = userInfo.employeeNo;
    this.form.applyUserName = userInfo.employeeName;

    this.personlist = [
      {
        id: userInfo.employeeNo,
        name: userInfo.employeeName
      }
    ];

    this.form.applyContact = userInfo.employeeName;
    this.form.applyContactDept = userInfo.orgName;
    this.form.applyContactPhone = userInfo.phoneNumber;
    const findItem = this.formList.find(item => item.prop === 'applyFiles');
    findItem.form = {
      businessId: this.form.applyFiles
    };
  }
};
