<template>
  <view class="ts-content">
    <page-head title="车辆预约" @clickLeft="returnBack"></page-head>
    <base-form
      class="base-form"
      ref="baseForm"
      :form-list="formList"
      :form-data.sync="form"
      :rules="rules"
      :showSubmitButton="false"
      @open-range-picker-before="handleOpenRangePickerBefore"
      @select-callback="handleSelectCallback"
      @select-person-callback="handleSelectPersonCallback"
      @handld-rang-date="handldRangDate"
    ></base-form>

    <view class="action-content">
      <view class="action-item" @click="close">取消</view>

      <view class="action-item submit-btn" @click="submit">
        提交
      </view>
    </view>

    <search-select ref="SearchSelect" />
  </view>
</template>

<script>
import indexJs from './index';
import BaseForm from '@/components/base-form/base-form.vue';
import SearchSelect from '@/components/search-select/search-select.vue';

export default {
  mixins: [indexJs],
  components: {
    BaseForm,
    SearchSelect
  },
  data() {
    return {
      personlist: []
    };
  },
  methods: {
    returnBack() {
      let pages = getCurrentPages();
      if (pages.length === 1) {
        this.$parentTypeFun
          ? this.$parentTypeFun({
              type: 'jumpPage',
              path: '/workbench'
            })
          : history.back();
      } else {
        uni.navigateBack();
      }
    },
    handleOpenRangePickerBefore() {
      // 新增预约时间 开始时间默认提前5分钟， 结束时间默认提前一小时
      this.$set(this.form, 'applyTimeVal', [
        this.$dayjs()
          .add(5, 'm')
          .format('YYYY-MM-DD HH:mm'),
        this.$dayjs()
          .add(1, 'h')
          .format('YYYY-MM-DD HH:mm')
      ]);
    },
    async handleSelectCallback(item) {
      let optionList = [];
      switch (item.propVal) {
        case 'vehicleId':
          const res = await this.ajax.vehicleInfoList({
            pageSize: 999,
            status: 1,
            sidx: 'create_date',
            sord: 'desc'
          });
          let row = res.rows || [];

          optionList = row.map(item => {
            return {
              name: `${item.vehicleNo}-${item.seats}座`,
              id: item.id
            };
          });

          break;
      }

      this.$refs.SearchSelect.open({
        optionList,
        defaultVal: this.form[item.propVal],
        callback: select => {
          this.form[item.prop] = select.name.split('-')[0];
          this.form[item.propVal] = select.id;
          this.$forceUpdate();
        }
      });
    },
    handleSelectPersonCallback() {
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        this.personlist = data;
        uni.removeStorageSync('person_list');
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
        this.form.applyUser = '';
        this.form.applyUserName = '';
        this.form.applyUser = data.map(item => item.id).join(',');
        this.form.applyUserName = data.map(item => item.name).join(',');
      });
      uni.setStorageSync('person_list', JSON.stringify(this.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    async handldRangDate(e) {
      const reset = title => {
        uni.showToast({
          title,
          icon: 'none'
        });
        this.$set(this.form, 'applyTime', '');
        this.$set(this.form, 'applyTimeVal', []);
      };

      if (!this.form.vehicleId) {
        reset('请选择预约车辆!');
        return false;
      }

      const [applyStartTime, applyEndTime] = e;
      const start = this.$dayjs(applyStartTime, 'YYYY-MM-DD HH:mm');
      const end = this.$dayjs(applyEndTime, 'YYYY-MM-DD HH:mm');
      if (start.isAfter(end)) {
        reset('预约开始时间不能大于结束时间!');
        return false;
      }

      let params = {
        applyStartTime,
        applyEndTime,
        vehicleId: this.form.vehicleId
      };
      const res = await this.ajax.getIsOccupy(params);
      if (res.success == false) {
        reset('查询用车时间是否被占用失败!');
        return false;
      }
      if (!res.object) {
        reset(res.message);
        return false;
      }
    },
    async submit() {
      const result = await this.$refs.baseForm.validate();

      const data = Object.assign({}, this.form);

      const {
        applyTimeVal: [applyStartTime, applyEndTime]
      } = data;

      data.applyStartTime = applyStartTime;
      data.applyEndTime = applyEndTime;

      delete data.applyTime;
      delete data.applyTimeVal;
      delete data.applyFilesList;
      data.applyReasonPublic = Number(data.applyReasonPublic);
      data.applyDriver = Number(data.applyReasonPublic);

      const res = await this.ajax.vehicleApplySave(data);
      if (!res.success) {
        uni.showToast({
          title: res.message || '预约失败!',
          duration: 2000,
          icon: 'none'
        });
        return;
      }

      uni.showToast({
        title: '预约成功',
        duration: 2000,
        icon: 'none'
      });
      setTimeout(() => {
        this.$set(this, 'form', {});
        this.close();
      }, 2000);
    },
    close() {
      let pages = getCurrentPages();
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100vh;
  background: #fff;
  position: relative;
  /deep/ .base-form {
    height: calc(100% - 84px);
    overflow: auto;

    .u-switch {
      &.u-switch--on {
        background: $theme-color !important;
      }
    }
  }
  .action-content {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    box-shadow: 0 -1px 6px #ccc;
    width: 100%;
    height: 40px;
    .action-item {
      flex: 1;
      text-align: center;
      color: $uni-text-color-grey;
      position: relative;
      &:not(:last-child)::after {
        content: ' ';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 15px;
        border-right: 1px solid #eee;
      }
      &.submit-btn {
        color: $theme-color;
      }
    }
  }
}
</style>
