<template>
  <view class="preview-form-content">
    <view v-for="col of columns" :key="col.prop" class="preview-form-item">
      <view class="form-item-name">{{ col.label }}</view>
      <view class="form-item-content">
        <view v-if="col.prop === 'image' && col.key">
          <item-image v-if="data[col.key]" :businessId="data[col.key]" />
        </view>
        <slot v-else :name="col.prop">
          {{
            col.formatter
              ? col.formatter(data, col, data[col.prop])
              : data[col.prop] || '-'
          }}
        </slot>
      </view>
    </view>
  </view>
</template>

<script>
import ItemImage from './item-image.vue';
export default {
  props: {
    columns: Array,
    data: Object
  },
  components: { ItemImage }
};
</script>

<style lang="scss" scoped>
.preview-form-content {
  padding-left: 8px;
  background-color: #fff;
  .preview-form-item {
    display: flex;
    padding: 8px 0;
    &:not(:last-child) {
      border-bottom: 1px solid $u-form-item-border-color;
    }
    .form-item-name {
      flex-shrink: 0;
      margin-right: 8px;
      font-size: 14px;
      color: $u-tips-color;
    }
    .form-item-content {
      flex: 1;
      text-align: right;
      padding-right: 16px;
      font-size: 14px;
    }
  }
}
</style>
