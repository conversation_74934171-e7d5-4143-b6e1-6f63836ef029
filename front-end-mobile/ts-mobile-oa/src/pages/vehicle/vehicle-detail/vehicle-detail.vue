<template>
  <view class="ts-content">
    <page-head :title="title" @clickLeft="returnBack"></page-head>

    <scroll-view class="swiper-head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tabItem, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        @click="handleTabClick(tabItem, index)"
      >
        <text
          class="uni-tab-item-title"
          :class="tabIndex == index ? 'uni-tab-item-title-active' : ''"
          >{{ tabItem.label }}</text
        >
      </view>
    </scroll-view>

    <!-- 流程基础信息 -->
    <div class="detail-content">
      <view class="detail-title-content">
        <view class="name">{{ detail.createUserName }}的预约</view>
        <view class="appointment-time">{{ detail.createDate }}</view>
        <view class="flow-info">
          <text>当前节点：{{ detail.wfStepName }}</text>
          <text
            class="status-text"
            :style="`color: ${detail.applyStatus != 5 ? '#5260FF' : '#333'}`"
          >
            · {{ applyStatus[detail.applyStatus] }}
          </text>
        </view>
      </view>

      <scroll-view
        :scroll-into-view="toView"
        :scroll-y="true"
        scroll-with-animation="true"
        class="scroll-content"
        ref="scroll"
      >
        <view class="vehicle-inform-container">
          <image :src="vehicleInfo.picture" class="vehicle-image"> </image>
          <view class="vehicle-info">
            <view class="vehicle-title">
              <text>{{ vehicleInfo.vehicleNo }}</text>
              <text
                class="vehicle-status"
                :style="
                  `color: ${vehicleStatusColor[vehicleInfo.vehicleStatus]}`
                "
              >
                · {{ vehicleStatus[vehicleInfo.vehicleStatus] }}
              </text>
              <text>{{ vehicleInfo.seats }}座</text>
              <view class="oil-card-icon" v-if="vehicleInfo.oilCard"></view>
            </view>
            <view class="vehicle-name">
              {{ vehicleInfo.vehicleName }} ·
              {{ vehicleInfo.vehicleColor }}
            </view>
          </view>
        </view>

        <view class="section-title">预约信息</view>
        <form-preview
          ref="BasicDetail"
          id="BasicDetail"
          :columns="basicColumns"
          :data="detail"
        />

        <view
          v-if="taskHistoryList && taskHistoryList.length > 0"
          class="section-title"
          >流程信息</view
        >
        <view class="process-box">
          <data-process-history
            v-if="taskHistoryList && taskHistoryList.length > 0"
            ref="FlowDetail"
            id="FlowDetail"
            :processHistoryList="taskHistoryList"
          />
        </view>
        <template v-if="detail.applyStatus >= 3">
          <view class="section-title">派车信息</view>
          <dispatch-detail
            ref="DispatchDetail"
            id="DispatchDetail"
            :data="detail"
          />
        </template>
        <template v-if="detail.applyStatus >= 4 && this.detail.isDispatch == 1">
          <view class="section-title">出车信息</view>
          <departure-info
            ref="DepartureInfo"
            id="DepartureInfo"
            :data="detail"
          />
        </template>
        <template v-if="detail.applyStatus >= 5 && this.detail.isDispatch == 1">
          <view class="section-title">还车信息</view>
          <return-info
            ref="ReturnInfo"
            id="ReturnInfo"
            :data="detail"
            :payTypeOptions="payTypeOptions"
          />
        </template>
      </scroll-view>
    </div>

    <view class="operate">
      <!-- 审批 -->
      <template v-if="renderType == 1">
        <view @click="handleBack" class="operate-item">退回</view>
        <view @click="handlePass" class="operate-item">同意</view>
      </template>
      <!-- 派车 -->
      <template v-if="renderType == 2">
        <view @click="handleDelivery" class="operate-item">派车</view>
      </template>
      <!-- 出还车 -->
      <template v-if="renderType == 3">
        <view
          v-if="detail.applyStatus == 3"
          @click="handleDeparture"
          class="operate-item"
          >出车</view
        >
        <view
          v-if="detail.applyStatus == 4"
          @click="handleReturn"
          class="operate-item"
          >还车</view
        >
      </template>
    </view>
  </view>
</template>

<script>
import {
  applyStatus,
  vehicleStatus,
  vehicleStatusColor
} from '@/pages/vehicle/config.js';
import formPreview from './components/form-preview.vue';
import DispatchDetail from './components/dispatch-detail.vue';
import DepartureInfo from './components/departure-info.vue';
import ReturnInfo from './components/return-info.vue';

export default {
  components: {
    formPreview,
    DispatchDetail,
    DepartureInfo,
    ReturnInfo
  },
  data() {
    return {
      title: '',
      applyStatus,
      vehicleStatus,
      vehicleStatusColor,
      tabIndex: 0,
      toView: '',
      vehicleInfo: {},
      detail: {},
      taskHistoryList: null,

      basicColumns: [
        {
          label: '用车原因',
          prop: 'applyReason'
        },
        {
          label: '原因是否公开',
          prop: 'applyReasonPublic',
          formatter: (row, prop, cell) => {
            return cell == 1 ? '公开' : '不公开';
          }
        },
        {
          label: '用车人',
          prop: 'applyUserName'
        },
        {
          label: '用车时间',
          prop: 'applyStartTime',
          formatter: row => {
            let { applyStartTime, applyEndTime } = row;
            return [applyStartTime, applyEndTime].join(' - ');
          }
        },
        {
          label: '用车路线',
          prop: 'applyLine'
        },
        {
          label: '用车联系人',
          prop: 'applyContact'
        },
        {
          label: '联系人部门',
          prop: 'applyContactDept'
        },
        {
          label: '联系人手机',
          prop: 'applyContactPhone'
        },
        {
          label: '是否需要司机',
          prop: 'applyDriver',
          formatter: (row, prop, cell) => {
            if (String(cell)) {
              return cell == 0 ? '不需要' : '需要';
            }
          }
        },
        {
          label: '备注',
          prop: 'applyRemark'
        },
        {
          label: '附件',
          prop: 'image',
          key: 'applyFiles'
        }
      ],

      dispatchColumns: [],
      payTypeOptions: [],
      id: '',
      renderType: '',
      taskId: ''
    };
  },
  async onLoad({ id, type, taskId = '', title = '预约详情' }) {
    this.id = id;
    this.title = title;
    this.renderType = type;
    if (taskId) this.taskId = taskId;
    this.handleGetOilType();

    const appointmentRes = await this.ajax.getVehicleAppointmentDetail(id);
    const vehicleRes = await this.ajax.vehicleInfoSeeDetails(
      appointmentRes.object.vehicleId
    );
    if (!vehicleRes.success || !appointmentRes.success) {
      uni.showToast({
        title: '车辆预约详情获取失败!',
        icon: 'none'
      });
      return;
    }
    this.vehicleInfo = vehicleRes.object;
    this.detail = appointmentRes.object;

    // 获取车辆图片信息
    this.ajax
      .getFileAttachmentByBusinessId({
        businessId: this.vehicleInfo.picture
      })
      .then(res => {
        if (!res.success) {
          uni.showToast({
            title: '车辆图片获取失败!',
            icon: 'none'
          });
          return;
        }
        this.vehicleInfo.picture = location.origin + res.object[0].realPath;
      });

    if (this.detail.wfInstanceId) {
      //获取流程信息
      await this.ajax
        .getTaskHisList({
          wfInstId: this.detail.wfInstanceId,
          pageNo: 1,
          pageSize: 10000,
          sidx: 'finished_date',
          sord: 'desc'
        })
        .then(res => {
          this.taskHistoryList = this.$oaModule.taskHisList(res.rows);
        });
    }
  },
  methods: {
    handleGetOilType() {
      this.ajax.getDataByDataLibrary('VEHICLE_OIL_PTY_TYPE').then(res => {
        if (!res.success) {
          return;
        }
        this.payTypeOptions = res.object;
      });
    },
    returnBack() {
      let pages = getCurrentPages();
      if (pages.length === 1) {
        uni.redirectTo({
          url: '/pages/vehicle/my-reservation/index'
        });
      } else {
        uni.navigateBack();
      }
    },
    handleTabClick(item, index) {
      this.tabIndex = index;
      let name = item.name;
      let dom = this.$refs[name];
      dom = dom && dom.$el;

      let domScrollTop = dom.offsetTop,
        scrollDom = this.$refs.scroll;
      document.documentElement.scrollTop = domScrollTop;
      this.toView = name;
    },
    // 审批同意
    handlePass() {
      let { id, taskId } = this;
      uni.navigateTo({
        url: `/pages/vehicle/my-approval/components/pross-approval?id=${id}&taskId=${taskId}&type=Pass`
      });
    },
    // 审批退回
    handleBack() {
      let { id, taskId } = this;
      uni.navigateTo({
        url: `/pages/vehicle/my-approval/components/pross-approval?id=${id}&taskId=${taskId}&type=Back`
      });
    },
    // 派车
    handleDelivery() {
      let { id } = this;
      uni.navigateTo({
        url: `/pages/vehicle/my-delivery/components/add-delivery?id=${id}`
      });
    },
    // 出车
    handleDeparture() {
      let { id } = this;
      uni.navigateTo({
        url: `/pages/vehicle/my-vehicle-usage/components/departure-form?id=${id}`
      });
    },
    // 还车
    handleReturn() {
      let { id } = this,
        totalMileage = this.vehicleInfo.totalMileage;
      uni.navigateTo({
        url: `/pages/vehicle/my-vehicle-usage/components/return-form?id=${id}&totalMileage=${totalMileage}`
      });
    }
  },
  computed: {
    tabBars() {
      let sections = [
        {
          label: '预约信息',
          name: 'BasicDetail'
        }
      ];

      if (this.detail.wfInstanceId) {
        sections.push({
          label: '流程信息',
          name: 'FlowDetail'
        });
      }

      if (this.detail.applyStatus >= 3) {
        sections.push({
          label: '派车信息',
          name: 'DispatchDetail'
        });
      }
      if (this.detail.applyStatus >= 4 && this.detail.isDispatch == 1) {
        sections.push({
          label: '出车信息',
          name: 'DepartureInfo'
        });
      }
      if (this.detail.applyStatus >= 5 && this.detail.isDispatch == 1) {
        sections.push({
          label: '还车信息',
          name: 'ReturnInfo'
        });
      }

      return sections;
    }
  }
};
</script>

<style lang="scss" scoped>
.swiper-head {
  position: relative;
  width: 100%;
  height: 40px;
  background-color: #ffffff;
  flex-direction: row;
  box-sizing: border-box;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
  &::before,
  &::after {
    position: absolute;
    z-index: 10;
    right: 0;
    left: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #eeeeee;
  }
  &::before {
    top: 0;
  }
  &::after {
    bottom: 0;
  }
}
.uni-tab-item {
  /* #ifndef APP-PLUS */
  display: inline-block;
  /* #endif */
  flex-wrap: nowrap;
  min-width: 30%;
  padding: 0 16px;
  box-sizing: border-box;
  text-align: center;
}
.uni-tab-item-title {
  color: #555;
  font-size: 16px;
  height: 40px;
  line-height: 38px;
  flex-wrap: nowrap;
  display: block;
  box-sizing: border-box;
  /* #ifndef APP-PLUS */
  white-space: nowrap;
  /* #endif */
}
.uni-tab-item-title-active {
  color: $theme-color;
  border-bottom: 2px solid $theme-color;
}
.ts-content {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #fff;
  position: relative;
  padding-bottom: 40px;
  .operate {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px;
    display: flex;
    background: #fff;
    z-index: 999;
    .operate-item {
      border-top: 1px solid #eee;
      display: flex;
      flex: 1;
      justify-content: center;
      align-items: center;
      text-align: center;
      color: $theme-color;
      &:first-child {
        border-right: 1px solid #eee;
      }
    }
  }
}
.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .vehicle-inform-container {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background: mix(#5260ff, #fff, 8%);
    border-radius: 4px;
    .vehicle-image {
      height: 56px;
      width: 56px;
      margin-right: 8px;
      border-radius: 4px;
    }
    .vehicle-info {
      flex: 1;

      .vehicle-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        > * {
          margin-right: 16px;
        }
        .vehicle-status {
          color: #5260ff;
          display: inline-flex;
          align-items: center;
        }
        .oil-card-icon {
          height: 22px;
          width: 22px;
          background-color: #5260ff;
          border-radius: 50%;
          position: relative;
          &::before {
            content: '绑';
            font-size: 12px;
            line-height: 0;
            color: #fff;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
      .vehicle-name {
        font-size: 12px;
        color: $u-tips-color;
      }
    }
  }
  .scroll-content {
    flex: 1;
    overflow-y: auto;
  }
}
.detail-title-content {
  position: sticky;
  background-color: #fff;
  padding: 8px;
  .name {
    font-weight: 700;
  }
  .appointment-time {
    color: #999;
    font-size: 12px;
  }
  .flow-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #666;
    .status-text {
      display: flex;
      align-items: center;
      color: #5260ff;
    }
  }
}
.section-title {
  background: #fff;
  height: 30px;
  line-height: 30px;
  padding-left: 8px;
  font-weight: 700;
}
.process-box {
  padding: 0 16px;
  background: #fff;
}
</style>
