<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="我的收文"></page-head>
    <uni-search-bar
      radius="100"
      bgColor="#FFFFFF"
      searchBgColor="#eeeeee"
      borderColor="transparent"
      cancelButton="none"
      @confirm="search"
      placeholder="请输入收文标题"
    ></uni-search-bar>
    <scroll-view class="swiper_head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tab, index) in tabBars"
        class="uni-tab-item"
        :key="index"
        :data-current="index"
        @click="ontabtap"
      >
        <view :class="tabIndex === index ? 'uni-tab-item-title-active' : ''">
          <text class="uni-tab-item-title">{{ tab.name }}</text>
          <text
            class="uni-tab-item-num"
            v-if="tab.total != null && tab.total != 0 && index != 1"
            >{{ tab.total >= 100 ? '99+' : tab.total }}</text
          >
        </view>
      </view>
    </scroll-view>
    <swiper
      :current="tabIndex"
      class="swiper_box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper_item"
        v-for="(item, index) in tabBars"
        :key="index"
      >
        <mescroll
          :ref="'mescroll' + index"
          :mescrollIndex="index"
          :down="item.downOption"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view class="contact_list">
            <view
              class="contact_item"
              v-for="row in item['list']"
              :key="row.id"
              @tap="chooseItem(row)"
            >
              <view class="contact_item_row">
                <view class="contact_item_title">
                  <text class="title">{{ row.fileTitle }}</text>
                </view>
              </view>
              <view class="contact_item_row">
                <text class="contact_item_node">{{ row.fileNumber }}</text>
                <text class="contact_item_time">{{
                  row.createDate | formatTime
                }}</text>
              </view>
            </view>
          </view>
        </mescroll>
      </swiper-item>
    </swiper>
    <uni-popup type="bottom" ref="popup">
      <view class="popup-content-box">
        <view class="popup-content-title">{{ checkFileTitle }}</view>
        <view
          class="file-item"
          v-for="item in checkFileList"
          :key="item.fileId"
          @click="openFile(item)"
        >
          <text
            class="oa-icon"
            :class="'oa-icon-' + $oaModule.formatFileType(item.fileExtension)"
          ></text>
          <text>{{ item.originalName || '未知' }}</text>
          <text
            class="oa-icon oa-icon-xiazai down_load"
            @click.stop="downloadFile(item)"
          >
          </text>
        </view>
      </view>
    </uni-popup>
    <file-operation-selection
      ref="actionPopup"
      @preview="handleActionedFile"
      @download="handleActionedFile"
    ></file-operation-selection>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import Base64 from '@/common/js/base64.min.js';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      keywords: '',
      tabIndex: 0, //当前选中的tab索引值，从0计数
      tabBars: [
        {
          name: '未读',
          isRead: 0,
          downOption: true,
          isInit: true,
          total: null,
          list: []
        },
        {
          name: '已读',
          isRead: 1,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        }
      ],
      checkFileTitle: '',
      checkFileList: []
    };
  },
  methods: {
    //搜索
    search(res) {
      this.keywords = res ? res.value : '';
      this.tabBars.forEach(item => {
        item.list = [];
        item.isInit = false;
        item.total = 0;
      });
      this.$nextTick(() => {
        this.tabBars[this.tabIndex]['isInit'] = true;
        this.$refs[`mescroll${this.tabIndex}`][0].downCallback();
      });
    },
    //tab点解切换
    ontabtap(e) {
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      this.switchTab(Number(index));
    },
    //tab滑动切换
    ontabchange(e) {
      let index = e.target.current || e.detail.current;
      this.switchTab(Number(index));
    },
    switchTab(index) {
      if (this.tabIndex === index) {
        return;
      } else if (!this.tabBars[index]['isInit']) {
        this.tabBars[index]['isInit'] = true;
        this.$nextTick(() => {
          this.$refs[`mescroll${index}`][0].downCallback();
        });
      }
      this.tabIndex = index;
    },
    //根据类型获取数据
    getListData(page, successCallback, errorCallback, keywords, index) {
      this.ajax
        .getMySendfileList({
          isRead: this.tabBars[index]['isRead'],
          pageSize: page.size,
          pageNo: page.num,
          fileTitle: this.keywords,
          sidx: 'CREATE_DATE',
          sord: 'desc'
        })
        .then(res => {
          successCallback(res.rows, res.totalCount);
        })
        .catch(e => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      this.tabBars[index]['total'] = totalCount;
      this.tabBars[index]['list'] = this.tabBars[index]['list'].concat(rows);
    },
    datasInit(keywords, index) {
      this.tabBars[index]['list'] = [];
    },
    chooseItem(row) {
      if (row.fileId && row.fileId.indexOf(',') !== -1) {
        this.showPopup(row);
      } else {
        this.$refs.actionPopup.open({
          id: row.fileId,
          fileName: row.fileName,
          rowId: row.id
        });
      }
    },
    async handleActionedFile(row) {
      if (this.tabIndex == 0) await this.changFileStatus(row.rowId);
    },
    async openFile(row) {
      let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${row.fileId}?fullfilename=${row.fileName}&source=mobile`;
      if (this.tabIndex == 0) await this.changFileStatus(row.id);
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}${
            this.tabIndex == 0
              ? '&path=/pages/officialDocument/document-list'
              : ''
          }`
        });
      }
    },
    async downloadFile(row) {
      let filePath = `${this.$config.BASE_HOST}/ts-document/attachment/downloadFile/${row.fileId}?fullfilename=${row.fileName}&source=mobile`;
      if (this.tabIndex == 0) await this.changFileStatus(row.id);
      this.$downloadFile.downloadFile(filePath);
    },
    showPopup(row) {
      this.checkFileTitle = row.fileTitle;
      let fileIdList = row.fileId.split(','),
        originalNameList = row.originalName.split(','),
        fileNameList = row.fileName.split(','),
        fileExtensionList = row.fileExtension.split(',');
      this.checkFileList = fileIdList.map((item, index) => {
        return {
          id: row.id,
          fileId: item,
          fileName: fileNameList[index],
          originalName: originalNameList[index],
          fileExtension: fileExtensionList[index]
        };
      });
      this.$refs['popup'].open();
    },
    async changFileStatus(id) {
      await this.ajax.confirmMySendFile({
        isRead: 1,
        id: id
      });
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  .swiper_head {
    position: relative;
    width: 750rpx;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    /* #ifndef APP-PLUS */
    white-space: nowrap;
    /* #endif */
    /* flex-wrap: nowrap; */
    /* border-color: #cccccc;
			border-bottom-style: solid;
			border-bottom-width: 1px; */
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    .uni-tab-item-title-active {
      color: $theme-color;
      border-bottom: 2px solid $theme-color;
    }
    .uni-tab-item {
      /* #ifndef APP-PLUS */
      display: inline-block;
      /* #endif */
      flex-wrap: nowrap;
      width: 50%;
      padding-left: 34rpx;
      padding-right: 34rpx;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title,
      .uni-tab-item-num {
        color: #666;
        font-size: 30rpx;
        height: 100%;
        line-height: 2.5;
        flex-wrap: nowrap;
        box-sizing: border-box;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
      }
      .uni-tab-item-num {
        font-size: 28rpx;
        color: #fff;
        background-color: #f59a23;
        border-radius: 40rpx;
        padding: 0 10rpx;
        margin: 0 10rpx;
      }
    }
  }
  .swiper_box {
    flex: 1;
    .swiper_item {
      flex: 1;
      flex-direction: row;
    }
  }
  .empty-wrap {
    text-align: center;
    .empty-img {
      width: 300rpx;
      margin-top: 200rpx;
    }
    .empty-text {
      text-align: center;
      clear: both;
      color: #d6d6d6;
      font-size: 14px;
      margin-top: -95px;
    }
  }
  .contact_item {
    text-decoration: none;
    padding: 22rpx 30rpx;
    background-color: #ffffff;
    position: relative;
    display: block;
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      height: 1px;
      background-color: #eee;
      left: 30rpx;
      right: 0;
    }
    &:last-child::after {
      height: 0;
    }
    .contact_item_row {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    .contact_item_title {
      font-size: 32rpx;
      color: #333333;
      font-weight: bold;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex: 1;
      .title {
        width: 100%;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    .contact_item_time {
      font-size: 24rpx;
      color: #999;
      overflow: hidden;
      .contact_item_icon {
        color: #f59a23;
        padding-right: 10rpx;
        font-size: 28rpx;
      }
    }
    .contact_item_node {
      font-size: 28rpx;
      color: #666;
      flex: 1;
    }
    .contact_item_status {
      font-size: 24rpx;
      transform: scale(0.83);
      color: #999;
      background-color: #eee;
      padding: 2rpx 10rpx;
      border-radius: 8rpx;
    }
  }
  .popup-content-box {
    background-color: #fff;
    .popup-content-title {
      padding: 20rpx 30rpx;
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
      border-bottom: 1px solid #ddd;
    }
  }
  .file-item {
    text-decoration: none;
    color: #333;
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 16rpx 30rpx;
    font-size: 28rpx;
    position: relative;
    // display: flex;
    // align-items: center;
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 30rpx;
      bottom: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &:last-child::after {
      height: 0;
    }
    text:nth-child(2) {
      flex: 1;
    }
    text:not(:nth-child(2)) {
      flex-shrink: 0;
    }
    .down_load {
      color: $theme-color;
    }
    .oa-icon {
      font-size: 40rpx;
      margin-right: 10rpx;
    }
  }
}
</style>
