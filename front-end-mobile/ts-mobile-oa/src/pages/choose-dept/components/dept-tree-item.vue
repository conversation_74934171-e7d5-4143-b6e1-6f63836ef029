<template>
  <view
    ref="deptTreeItem"
    class="dept-tree-item"
    v-show="!isHidden"
    :class="{
      'is-checked': isChecked,
      'is-expanded': isExpanded,
      'is-current': isCurrent
    }"
  >
    <view
      class="dept-tree-item-content"
      :style="{ paddingLeft: 16 * (level - 1) + 'px' }"
    >
      <view class="dept-tree-item-label-content">
        <view class="dept-tree-item-icon" @click="handleIconClick">
          <slot name="header" :data="data" :lazyLoading="lazyLoading">
            <view
              v-if="
                (data[propotySet.children] &&
                  data[propotySet.children].length) ||
                  lazyLoading
              "
              :class="
                level === 1 ? 'oa-icon-down-copy' : 'oa-icon-paixujiantouxia2'
              "
              class="oa-icon"
            ></view>
          </slot>
        </view>

        <view
          v-if="propotySet.formatter"
          class="dept-tree-item-label"
          v-html="propotySet.formatter(data, $refs.deptTreeItem)"
          @click="handleLabelClick"
        ></view>
        <view v-else class="dept-tree-item-label" @click="handleLabelClick">
          {{ data[propotySet.label] }}
        </view>
      </view>

      <view class="dept-tree-item-checked">
        <slot name="footer">
          <u-icon name="checkmark" size="32"></u-icon>
        </slot>
      </view>
    </view>

    <view class="loading-col" v-show="isLoading">
      <u-loading></u-loading>
      加载中...
    </view>
    <view
      v-if="data[propotySet.children] && data[propotySet.children].length"
      class="dept-tree-item-children-content"
    >
      <dept-tree-item
        v-for="(item, index) of data[propotySet.children]"
        :key="index"
        :data="item"
        :level="level + 1"
        :propotySet="propotySet"
      >
      </dept-tree-item>
    </view>
  </view>
</template>

<script>
export default {
  name: 'dept-tree-item',
  props: {
    /**@desc 树数据
     * @param {String}  label     显示的名称      可通过 propotySet 属性配置
     * @param {any}     value     点击选中的值    可通过 propotySet 属性配置
     * @param {Array}   children  子数据列表      可通过 propotySet 属性配置
     */
    data: {
      type: Object,
      default: () => {}
    },
    /**@desc 树显示数据配置
     * @param {String}    label     显示的名称的属性名
     * @param {String}    value     选中值的属性名
     * @param {String}    children  子数据列表的属性名
     * @param {Function}  formatter 名称显示内容
     */
    propotySet: {
      type: Object,
      default: () => {
        return {
          label: 'name',
          value: 'id',
          children: 'children'
        };
      }
    },
    /**@desc 树层级 */
    level: {
      type: Number
    }
  },
  data() {
    return {
      tree: null,
      isExpanded: false,
      isChecked: false,
      isCurrent: false,
      isHidden: false,
      isLoading: false
    };
  },
  methods: {
    handleLabelClick() {
      this.tree.onlyExpandInClickIcon
        ? null
        : this.tree.setExpandedNode(this, true);
      this.tree.setCurrentNode(this, true);
      this.tree.setSelectedNode(this, true);
    },
    handleIconClick() {
      if (
        this.lazyLoading &&
        !this.isExpanded &&
        (!this.data.children || !this.data.children.length)
      ) {
        this.isLoading = true;
        this.tree
          .loadAsyncSourceData(this, this.sourceLevel + 1)
          .then(() => {
            this.lazyLoading = false;
            this.isLoading = false;
          })
          .catch(() => {
            this.lazyLoading = false;
            this.isLoading = false;
          });
      }
      this.tree.setExpandedNode(this, true);
    }
  },
  created() {
    const parent = this.$parent;
    if (this.level == 1) {
      this.tree = parent.$parent;
      this.parent = parent.$parent;
    } else {
      this.tree = parent.$parent.$parent.tree;
      this.parent = parent.$parent.$parent;
    }

    this.tree.createdNode(this.level, this.data, this);
  }
};
</script>

<style lang="scss" scoped>
.dept-tree-item.is-checked {
  > .dept-tree-item-content .dept-tree-item-checked {
    color: $u-type-primary;
  }
}
.dept-tree-item.is-current {
  > .dept-tree-item-content {
    background-color: $u-type-primary-light;
  }
}
.dept-tree-item.is-expanded {
  > .dept-tree-item-children-content {
    display: block;
  }

  > .dept-tree-item-content .dept-tree-item-icon {
    transform: rotate(0);
    .oa-icon-paixujiantouxia2 {
      transform: rotate(0) scale(0.75);
    }
  }
}
.dept-tree-item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  // padding-right: 16px;
}
.dept-tree-item-label-content {
  display: flex;
  align-items: center;
  flex: 1;
}
.dept-tree-item-icon {
  height: 100%;
  font-size: 16px;
  text-align: center;
  transform: rotate(-90deg);
  transition: all 0.3s;
  .oa-icon-down-copy {
    font-size: 12px;
  }
  .oa-icon-paixujiantouxia2 {
    font-size: 12px;
    transform: scale(0.75);
    color: #333333b3;
  }
}
.dept-tree-item-checked {
  color: #e4e4e4;
  position: absolute;
  right: 8px;
}
.dept-tree-item-label {
  padding-left: 8px;
  flex: 1;
}
.dept-tree-item-children-content {
  overflow: hidden;
  transition: all 0.3s;
  display: none;
}
.loading-col {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  color: #666;
  .u-loading {
    margin-right: 8px;
  }
}
</style>
