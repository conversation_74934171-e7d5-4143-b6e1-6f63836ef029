<template>
  <u-popup mode="bottom" v-model="value" height="100%">
    <u-navbar title="已选科室" title-bold :customBack="closePopup"></u-navbar>
    <view class="search-container">
      <u-search
        v-model="keywords"
        :show-action="false"
        placeholder="输入科室名称搜索"
        @search="search"
        @clear="clear"
      ></u-search>
    </view>
    <view class="content-list">
      <u-swipe-action
        v-for="(item, index) in filterDepts"
        :key="item.userId"
        :show="item.show"
        :index="index"
        :options="swipeActionOptions"
        @click="click"
        @open="open"
      >
        <view class="u-border-bottom">
          <view class="title-wrap">
            <dept-list-item-info :dept="item"></dept-list-item-info>
          </view>
        </view>
      </u-swipe-action>
    </view>
  </u-popup>
</template>

<script>
import deptListItemInfo from './dept-list-item-info.vue';
export default {
  name: 'choose-dept-popup',
  components: {
    deptListItemInfo
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      keywords: '',
      swipeActionOptions: [
        {
          text: '删除',
          style: {
            backgroundColor: '#dd524d'
          }
        }
      ]
    };
  },
  computed: {
    filterDepts() {
      return this.list.filter(p => {
        p.show = false;
        return p.name.indexOf(this.keywords) !== -1;
      });
    }
  },
  methods: {
    closePopup() {
      this.$emit('input', false);
    },
    open(index) {
      this.filterDepts[index].show = true;
      this.filterDepts.map((val, idx) => {
        if (index != idx) this.list[idx].show = false;
      });
    },
    close(index) {
      this.filterDepts[index].show = false;
    },
    click(index) {
      let clickItem = this.filterDepts[index];
      let chooseDepts = this.list.filter(i => i.id != clickItem.id);
      this.$emit('change', chooseDepts);
    }
  }
};
</script>

<style lang="scss" scoped>
.search-container {
  padding: $uni-spacing-col-sm $uni-spacing-row-lg;
  margin-bottom: $uni-spacing-col-base;
}
.title-wrap {
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
}
</style>
