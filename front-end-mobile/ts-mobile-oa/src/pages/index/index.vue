<template>
  <view class="ts-content">
    <view class="user-info">
      <image
        class="user-img"
        :src="
          headimg ? $config.BASE_HOST + headimg : '../../static/img/headImg.png'
        "
      ></image>
      <text class="use-name">{{ deptname }}-{{ username }}</text>
    </view>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :down="mescrollDown"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <!-- 数据列表 -->
        <view
          class="contact-item"
          v-for="(item, index) in dataList"
          :key="index"
          @click="chooseItem(item.urlStr)"
        >
          <view
            class="contact-item-left"
            :style="{ 'background-color': item.backgroundcolor }"
          >
            <text class="oa-icon" :class="item.oa - icon"></text>
          </view>
          <view class="contact-item-right">
            <view class="contact-item-title">
              {{ item.title }}
              <view
                class="contact-item-status status-wait"
                v-if="item.noread > 0"
              >
                <text>{{ item.noread >= 100 ? '99+' : item.noread }}</text>
              </view>
            </view>
            <view class="contact-item-info">
              <text class="contact-item-new">{{ item.subject }}</text>
              <text class="contact-item-item">{{
                item.createDate | indexTimeFilter
              }}</text>
            </view>
          </view>
        </view>
        <!-- 数据列表 end-->
      </mescroll>
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import loginModule from '@/common/js/loginModule.js';
export default {
  components: {
    mescroll
  },
  computed: {
    ...mapState(['username', 'headimg', 'deptname'])
  },
  data() {
    return {
      dataList: [], //列表数据
      mescrollDown: false
    };
  },
  watch: {
    $route: {
      handler: function() {
        this.dataList = [];
        this.$refs['mescroll'].downCallback();
      },
      // 深度观察监听
      deep: true
    }
  },
  async onLoad(opt) {
    if (opt && opt.token && !this.$config.ENABLE_ACCOUNT_LOGIN) {
      let userInfo = await loginModule.loginInfo(opt);
      if (JSON.stringify(userInfo) != '{}') this.changeState(userInfo);
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs['mescroll'].downCallback();
    });
  },
  methods: {
    ...mapMutations(['changeState']),
    async getListData(page, successCallback, errorCallback, keywords) {
      await _self.ajax
        .getIndexMessageRemindList()
        .then(res => {
          let rows = res.object;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(row) {
      row.map(item => {
        switch (item.title) {
          case '信息发布':
            item.urlStr = '/pages/index/info-unread-list';
            break;
          case '审批助手':
            item.urlStr = '/pages/index/work-unread-list';
            break;
          case '邮箱助手':
            item.urlStr = '/pages/index/email-unread-list';
            break;
          case '会议助手':
            item.urlStr = '/pages/index/boardroom-signin-list';
            break;
          case '收文助手':
            item.urlStr = '/pages/index/govfile-unread-list';
            break;
        }
      });
      this.dataList = this.dataList.concat(row);
    },
    datasInit() {
      this.dataList = [];
    },
    chooseItem(urlStr) {
      uni.navigateTo({
        url: urlStr
      });
    },
    jumpWorkflowPage() {
      uni.navigateTo({
        url: '/pages/workflow/workflow-list'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  .user-info {
    padding: 20rpx 30rpx;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    height: 88rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;
    .user-img {
      width: 48rpx;
      height: 48rpx;
      margin-right: 20rpx;
    }
    .use-name {
      font-size: 28rpx;
      color: #333333;
    }
  }
  .mescroll-content {
    position: absolute;
    width: 100%;
    top: 108rpx;
    bottom: 0;
    .contact-item {
      display: flex;
      padding: 22rpx 30rpx;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        height: 1px;
        background-color: #eee;
        left: 30rpx;
        right: 0;
        transform: scaleY(0.5);
      }
      &:last-child::after {
        height: 0;
      }
      .contact-item-left {
        width: 80rpx;
        height: 80rpx;
        border-radius: 100%;
        margin-right: 20rpx;
        color: #ffffff;
        text-align: center;
        line-height: 80rpx;
        font-size: 28rpx;
      }
      .contact-item-right {
        flex: 1;
        .contact-item-title {
          font-size: 32rpx;
          color: #333333;
          overflow: hidden;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .contact-item-status {
            font-size: 24rpx;
          }
          .status-wait {
            color: #fff;
            background-color: #f59a23;
            border-radius: 200rpx;
            line-height: 40rpx;
            padding: 0 16rpx;
            text-align: center;
            font-size: 24rpx;
          }
        }
        .contact-item-info {
          font-size: 28rpx;
          color: #999;
          display: flex;
          justify-content: flex-start;
          .contact-item-new {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
          }
          .contact-item-item {
            font-size: 24rpx;
          }
        }
      }
    }
  }
}
</style>
