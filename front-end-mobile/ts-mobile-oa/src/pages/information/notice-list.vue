<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack">
      <view class="head_info_type">
        <view
          class="type_tab"
          data-popup-type="bottom"
          data-popup-name="popup"
          @click="showPopup"
        >
          <text class="info_type_name">{{ selectedType.channelName }}</text>
          <text class="oa-icon oa-icon-paixujiantouxia2"></text>
        </view>
      </view>
    </page-head>
    <uni-search-bar
      radius="100"
      bgColor="#FFFFFF"
      cancelButton="none"
      searchBgColor="#eeeeee"
      borderColor="transparent"
      @confirm="search"
    ></uni-search-bar>
    <view class="tab_list">
      <view class="tab_item allread" @click="allread">
        <text class="tab_item_icon oa-icon oa-icon-xuanzhong"></text>
        全标已读
      </view>
      <view class="tab_item editEmail" @click="noreadList">
        <text
          class="tab_item_icon oa-icon oa-icon-huiyishishiyongqingkuang"
        ></text>
        所有未读
      </view>
    </view>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :down="false"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view class="contact_list">
          <view
            class="contact_item"
            v-for="(item, index) in dataList"
            :key="item.id"
            @tap="chooseItem(item.id, index)"
          >
            <view
              class="contact_item_img"
              :data-c="item.initColor"
              :style="{ 'background-color': item.initColor }"
              >{{ item.channelName.substring(item.channelName.length - 2) }}
            </view>
            <view class="contact_item_info">
              <view class="contact_item_row">
                <text
                  class="contact_item_unread"
                  v-if="item.bid == '' || item.bid == null"
                ></text>
                <text class="contact_item_top" v-if="item.showSign === '1'"
                  >[顶]</text
                >
                <text
                  class="contact_item_quintessence"
                  v-if="item.isMarrow === '1'"
                  >[精]</text
                >
                <text
                  class="contact_item_title"
                  :class="[item.titleColor === '1' ? 'titleRed' : '']"
                >
                  {{
                    item.informationTitle
                      ? item.informationTitle.replace(
                          /&lt;\/?[^&gt;]*&gt;/g,
                          ''
                        )
                      : '无主题'
                  }}
                </text>
                <text class="contact_item_read">
                  浏览 {{ item.informationKits }}
                </text>
              </view>
              <!-- <view class="contact_item_row">
                <rich-text
                  class="contact_item_content"
                  :nodes="
                    item.informationContent
                      ? item.informationContent.replace(/<\/?[^>]*>/g, '')
                      : '此信息没有文字内容'
                  "
                ></rich-text>
              </view> -->
              <view class="contact_item_row">
                <text class="contact_item_user">{{
                  item.createDeptName + ' ' + item.createUserName
                }}</text>
                <text class="contact_item_time">{{
                  item.createDate.substring(0, 16)
                }}</text>
              </view>
            </view>
          </view>
        </view>
      </mescroll>
    </view>
    <uni-popup :type="popupType" top-height="44px" ref="showpopup">
      <view class="contact_list scroll_list">
        <view
          class="contact_item"
          v-for="item in infoTypeList"
          :key="item.channelId"
          :data-column-id="item.channelId"
          :data-column-name="item.channelName"
          data-popup-name="popup"
          @tap="selectColumn"
        >
          <text class="contact_item_text">{{ item.channelName }}</text>
          <view class="contact_item_icon">
            <uni-icons
              v-if="selectedType.channelId == item.channelId"
              type="checkmarkempty"
              color="#005BAC"
              size="44"
            />
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
export default {
  components: {
    mescroll,
    uniPopup
  },
  data() {
    return {
      selectedType: {
        channelName: '全部类型',
        channelId: ''
      },
      infoTypeList: [
        {
          channelName: '全部类型',
          channelId: ''
        }
      ],
      dataList: [],
      keywords: '',
      itemColor: [
        '#005BAC',
        '#8080ff',
        '#1ab785',
        '#ff7f65',
        '#da70d6',
        '#639ef6',
        '#2cb8c4'
      ],
      popupType: ''
    };
  },
  onLoad() {
    this.getInfoCountChannel();
  },
  watch: {
    $route: {
      handler: function() {
        this.dataList = [];
        this.$refs['mescroll'].downCallback();
      },
      // 深度观察监听
      deep: true
    }
  },
  methods: {
    //获取信息类型
    getInfoCountChannel() {
      this.ajax
        .getInfoCountByChannel({
          index: 5,
          informationStatus: 1
        })
        .then(res => {
          this.infoTypeList = this.infoTypeList.concat(res.object);
          let arr = res.object.filter(item => {
            return item.channelName === '通知公告';
          });
          this.$set(this.selectedType, 'channelName', arr[0].channelName);
          this.$set(this.selectedType, 'channelId', arr[0].channelId);
          this.$refs['mescroll'].downCallback();
        });
    },
    //搜索
    search(res) {
      this.keywords = res.value;
      this.datasInit();
      this.$nextTick(() => {
        this.$refs['mescroll'].downCallback();
      });
    },
    //获取数据
    getListData(page, successCallback, errorCallback) {
      this.ajax
        .getInformationList({
          index: 5,
          sidx: 'createDate',
          sord: 'desc',
          informationStatus: 1,
          pageSize: page.size,
          pageNo: page.num,
          informationTitle: this.keywords,
          channelId: this.selectedType.channelId,
          isRead: ''
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(e => {
          errorCallback();
        });
    },
    setListData(rows) {
      rows.forEach(item => {
        this.infoTypeList.forEach((one, index) => {
          if (one.channelName == item.channelName) {
            item.initColor = this.itemColor[index - 1];
          }
        });
      });
      this.dataList = this.dataList.concat(rows);
    },
    datasInit() {
      this.dataList = [];
    },
    chooseItem(id, index) {
      this.$set(
        this.dataList[index],
        'bid',
        `bid_${Math.random()
          .toString(36)
          .substr(18)}`
      );
      uni.navigateTo({
        url: `/pages/information/information-details?informationId=${id}&fromPage=noticeList`
      });
    },
    //全标已读
    allread() {
      uni.showModal({
        title: '提示',
        content: '您确定将全部信息标记为已读？',
        confirmColor: '#005BAC',
        success: res => {
          if (res.confirm) {
            this.ajax.setInformationAllRead().then(res => {
              if (res.success) {
                uni.showToast({
                  title: '操作成功',
                  icon: 'none'
                });
                this.dataList.forEach(item => {
                  item.bid = 'readed';
                });
              }
            });
          }
        }
      });
    },
    //查看未读列表
    noreadList() {
      uni.navigateTo({
        url: '/pages/information/unread-list?fromPage=noticeList'
      });
    },
    //显示弹出层
    showPopup(e) {
      let data = e.currentTarget.dataset;
      this.popupType = data.popupType;
      this.$nextTick(() => {
        this.$refs[`show${data.popupName}`].open();
      });
    },
    //选择信息类型
    selectColumn(e) {
      let data = e.currentTarget.dataset;
      this.$set(this.selectedType, 'channelName', data.columnName);
      this.$set(this.selectedType, 'channelId', data.columnId);
      this.datasInit();
      this.$nextTick(() => {
        this.$refs[`show${data.popupName}`].close();
        this.$refs['mescroll'].downCallback();
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .head_info_type {
    text-align: center;
    vertical-align: middle;
    flex: 1;
    .type_tab {
      display: inline-block;
    }
    .info_type_name {
      /* #ifdef APP-PLUS */
      font-size: 34rpx;
      /* #endif */
      /* #ifndef APP-PLUS */
      font-size: 32rpx;
      /* #endif */
      font-weight: bold;
    }
    .oa-icon {
      font-size: 24rpx;
      padding: 0 3px;
    }
  }
  .tab_list {
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    position: relative;
    &::after {
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      content: '';
      position: absolute;
      background-color: #eee;
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
    }
    .tab_item {
      flex: 1;
      text-align: center;
      position: relative;
      height: 80rpx;
      line-height: 80rpx;
      color: #333;
      &::after {
        top: 20rpx;
        bottom: 20rpx;
        left: 0;
        width: 1px;
        content: '';
        position: absolute;
        background-color: #eee;
        -webkit-transform: scaleX(0.5);
        transform: scaleX(0.5);
      }
      &:first-child::after {
        width: 0;
      }
      .tab_item_icon {
        font-size: 32rpx;
        margin-right: 10rpx;
        color: #999999;
      }
    }
  }
  .mescroll-content {
    flex: 1;
    position: relative;
  }
  .scroll_list {
    max-height: 800rpx;
    overflow: auto;
  }
  .contact_item {
    padding: 22rpx 30rpx;
    background-color: #ffffff;
    position: relative;
    display: flex;
    align-items: center;
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      height: 1px;
      background-color: #eee;
      left: 30rpx;
      right: 0;
    }
    &:last-child::after {
      height: 0;
    }
    .contact_item_img {
      width: 80rpx;
      height: 80rpx;
      margin-right: 20rpx;
      border-radius: 100%;
      text-align: center;
      line-height: 80rpx;
      font-size: 28rpx;
      background-color: #005bac;
      color: #fff;
    }
    .contact_item_info {
      flex: 1;
      .contact_item_row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }
    }
    .contact_item_unread {
      width: 20rpx;
      height: 20rpx;
      border-radius: 100%;
      background-color: #f59a23;
      margin-right: 10rpx;
    }
    .contact_item_top {
      color: #f59a23;
      font-size: 28rpx;
    }
    .contact_item_quintessence {
      color: #3aad73;
      font-size: 28rpx;
    }
    .contact_item_title {
      flex: 1;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      padding-right: 20rpx;
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
    .titleRed {
      color: #dd1f36;
    }
    .contact_item_read {
      font-size: 24rpx;
      color: #999;
    }
    .contact_item_content {
      font-size: 28rpx;
      color: #666;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    .contact_item_user {
      flex: 1;
    }
    .contact_item_user,
    .contact_item_time {
      font-size: 24rpx;
      color: #999;
    }
    .contact_item_text {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
    }
    .contact_item_icon {
      line-height: 1;
    }
  }
}
</style>
