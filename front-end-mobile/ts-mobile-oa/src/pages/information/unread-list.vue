<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="所有未读"></page-head>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :searchInput="true"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view class="contact_list">
          <view
            class="contact_item"
            v-for="(item, index) in dataList"
            :key="item.id"
            @tap="chooseItem(item.id, index)"
          >
            <view
              class="contact_item_img"
              :data-c="item.initColor"
              :style="{ 'background-color': item.initColor }"
              >{{ item.channelName.substring(item.channelName.length - 2) }}
            </view>
            <view class="contact_item_info">
              <view class="contact_item_row">
                <text
                  class="contact_item_unread"
                  v-if="item.bid == '' || item.bid == null"
                ></text>
                <text class="contact_item_title">
                  {{
                    item.informationTitle
                      ? item.informationTitle.replace(/&lt;\/?[^>]*&gt;/g, '')
                      : '无主题'
                  }}
                </text>
                <text class="contact_item_read">
                  浏览 {{ item.informationKits }}
                </text>
              </view>
              <!-- <view class="contact_item_row">
                <rich-text
                  class="contact_item_content"
                  :nodes="
                    item.informationContent
                      ? item.informationContent.replace(/<\/?[^>]*>/g, '')
                      : '此信息没有文字内容'
                  "
                ></rich-text>
              </view> -->
              <view class="contact_item_row">
                <text class="contact_item_user">{{
                  item.createDeptName + ' ' + item.createUserName
                }}</text>
                <text class="contact_item_time">{{
                  item.createDate.substring(0, 16)
                }}</text>
              </view>
            </view>
          </view>
        </view>
      </mescroll>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      preIndex: '',
      selectedType: {
        channelName: '全部类型',
        channelId: ''
      },
      infoTypeList: [
        {
          channelName: '全部类型',
          channelId: ''
        }
      ],
      dataList: [],
      itemColor: [
        '#005BAC',
        '#8080ff',
        '#1ab785',
        '#ff7f65',
        '#da70d6',
        '#639ef6',
        '#2cb8c4'
      ]
    };
  },
  onLoad(opt) {
    this.preIndex = opt.fromPage;
    this.getInfoCountChannel();
  },
  methods: {
    //获取信息类型
    getInfoCountChannel() {
      this.ajax
        .getInfoCountByChannel({
          index: 5,
          informationStatus: 1
        })
        .then(res => {
          this.infoTypeList = this.infoTypeList.concat(res.object);
        });
    },
    //获取数据
    getListData(page, successCallback, errorCallback, keywords) {
      this.ajax
        .getInformationList({
          index: 5,
          sidx: 'createDate',
          sord: 'desc',
          informationStatus: 1,
          pageSize: page.size,
          pageNo: page.num,
          informationTitle: keywords,
          channelId: this.selectedType.channelId,
          isRead: 'no'
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(e => {
          errorCallback();
        });
    },
    setListData(rows) {
      rows.forEach(item => {
        this.infoTypeList.forEach((one, index) => {
          if (one.channelName == item.channelName) {
            item.initColor = this.itemColor[index - 1];
          }
        });
      });
      this.dataList = this.dataList.concat(rows);
    },
    datasInit() {
      this.dataList = [];
    },
    chooseItem(id, index) {
      this.dataList.splice(index, 1);
      uni.navigateTo({
        url: `/pages/information/information-details?informationId=${id}&fromPage=unreadList&preIndex=${this.preIndex}`
      });
    },
    returnBack() {
      if (this.preIndex == 'noticeList') {
        uni.redirectTo({
          url: '/pages/information/notice-list'
        });
      } else {
        uni.redirectTo({
          url: '/pages/information/information-access'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .mescroll-content {
    flex: 1;
    position: relative;
  }
  .contact_item {
    padding: 22rpx 30rpx;
    background-color: #ffffff;
    position: relative;
    display: flex;
    align-items: center;
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      height: 1px;
      background-color: #eee;
      left: 30rpx;
      right: 0;
    }
    &:last-child::after {
      height: 0;
    }
    .contact_item_img {
      width: 80rpx;
      height: 80rpx;
      margin-right: 20rpx;
      border-radius: 100%;
      text-align: center;
      line-height: 80rpx;
      font-size: 28rpx;
      background-color: #005bac;
      color: #fff;
    }
    .contact_item_info {
      flex: 1;
      .contact_item_row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        .contact_item_unread {
          width: 20rpx;
          height: 20rpx;
          border-radius: 100%;
          background-color: #f59a23;
          margin-right: 10rpx;
        }
        .contact_item_title {
          flex: 1;
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          padding-right: 20rpx;
          box-sizing: border-box;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
        .contact_item_read {
          font-size: 24rpx;
          color: #999;
        }
        .contact_item_content {
          font-size: 28rpx;
          color: #666;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .contact_item_user {
          flex: 1;
        }
        .contact_item_user,
        .contact_item_time {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
}
</style>
