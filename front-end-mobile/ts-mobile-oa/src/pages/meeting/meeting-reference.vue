<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" :title="formTitle"></page-head>
    <view class="content_top">
      <uni-segmented-control
        :current="current"
        :values="items"
        :style-type="styleType"
        :active-color="activeColor"
        @clickItem="onClickItem"
      />
    </view>
    <view class="content" v-if="showContent">
      <view v-if="current === 0" class="content_tab_item">
        <view class="row-group">
          <view class="row dis_flex">
            <view class="row_lable">
              <text class="row_lable_text">申请人</text>
            </view>
            <view class="row_value">
              <text class="row_value_text">{{
                applicationInfo.applyEmpname
              }}</text>
            </view>
          </view>
          <view class="row dis_flex">
            <view class="row_lable">
              <text class="row_lable_text">申请科室</text>
            </view>
            <view class="row_value">
              <text class="row_value_text">{{
                applicationInfo.applyOrgname
              }}</text>
            </view>
          </view>
          <view class="row dis_flex">
            <view class="row_lable">
              <text class="row_lable_text">联系人</text>
            </view>
            <view class="row_value">
              <text class="row_value_text">{{
                applicationInfo.linktelePerson
              }}</text>
            </view>
          </view>
          <view class="row dis_flex">
            <view class="row_lable">
              <text class="row_lable_text">联系方式</text>
            </view>
            <view class="row_value">
              <text class="row_value_text">{{
                applicationInfo.linktelePhone
              }}</text>
            </view>
          </view>
        </view>
        <view class="row-group">
          <view class="row dis_flex">
            <view class="row_lable">
              <text class="row_lable_text">会议类型</text>
            </view>
            <view class="row_value">
              <text class="row_value_text">{{
                applicationInfo.apptypeid
              }}</text>
            </view>
          </view>
          <view class="row dis_flex">
            <view class="row_lable">
              <text class="row_lable_text">会议主题</text>
            </view>
            <view class="row_value">
              <text class="row_value_text">{{
                applicationInfo.linktelePhone
              }}</text>
            </view>
          </view>
          <view class="row dis_flex">
            <view class="row_lable">
              <text class="row_lable_text">参与人</text>
            </view>
            <view class="row_value" style="color: #005BAC;">
              <text class="personNameStr">{{
                applicationInfo.attendeeName
              }}</text>
            </view>
          </view>
          <view class="row dis_flex">
            <view class="row_lable">
              <text class="row_lable_text">主持人</text>
            </view>
            <view class="row_value" style="color: #005BAC;">
              <text class="personNameStr">{{ applicationInfo.emceeName }}</text>
            </view>
          </view>
          <view class="row dis_flex">
            <view class="row_lable">
              <text class="row_lable_text">会议内容</text>
            </view>
            <view class="row_value">
              <text class="row_value_text">{{ applicationInfo.content }}</text>
            </view>
          </view>
        </view>
        <view class="row-group">
          <view class="row dis_flex">
            <view class="row_lable">
              <text class="row_lable_text">开始时间</text>
            </view>
            <view class="row_value">
              <text class="row_value_text">{{
                applicationInfo.startTime
              }}</text>
            </view>
          </view>
          <view class="row dis_flex">
            <view class="row_lable">
              <text class="row_lable_text">结束时间</text>
            </view>
            <view class="row_value">
              <text class="row_value_text">{{ applicationInfo.endTime }}</text>
            </view>
          </view>
          <view class="row dis_flex">
            <view class="row_lable">
              <text class="row_lable_text">会议地点</text>
            </view>
            <view class="row_value">
              <text class="row_value_text">{{
                applicationInfo.location + '-' + applicationInfo.name
              }}</text>
            </view>
          </view>
        </view>
      </view>
      <view v-if="current === 1" class="content_tab_item">
        <view class="fiel_list" v-if="applicationInfo.accessoryName.length > 0">
          <view
            class="file_item"
            v-for="(item, index) in applicationInfo.accessoryName"
            :key="index"
            @tap="downloadFile(applicationInfo.accessoryId[index])"
          >
            <view class="">
              <text
                class="oa-icon"
                :class="'oa-icon-' + item.split('.')[1]"
              ></text>
            </view>
            <view class="">
              <text>{{ item }}</text>
            </view>
          </view>
        </view>
        <view v-else class="nothing">
          <view class="img_content">
            <image
              class="nothing_img"
              src="../../static/img/nothing.png"
              mode="aspectFit"
            ></image>
          </view>
          <view class="tips_text">
            <text>暂无数据</text>
          </view>
        </view>
      </view>
      <view v-if="current === 2" class="content_tab_item">
        <view
          class="agenda_list"
          v-if="applicationInfo.boardroomAgenda.length > 0"
        >
          <view
            class="agenda_item"
            v-for="(item, index) in applicationInfo.boardroomAgenda"
            :key="index"
          >
            <view class="agenda_item_subject">{{
              index + 1 + '、' + item.agenda
            }}</view>
            <view class="agenda_item_content">{{ item.content }}</view>
            <view class="agenda_item_person"
              >负责人：{{ item.functionary }}</view
            >
          </view>
        </view>
        <view v-else class="nothing">
          <view class="img_content">
            <image
              class="nothing_img"
              src="../../static/img/nothing.png"
              mode="aspectFit"
            ></image>
          </view>
          <view class="tips_text">
            <text>暂无数据</text>
          </view>
        </view>
      </view>
    </view>
    <view class="bottomBtn" v-if="showContent">
      <button
        class="btn_item btn_item_finished"
        v-if="applicationInfo.meetingStatus == -1"
      >
        会议已结束
      </button>
      <button
        class="btn_item btn_item_ongoing"
        v-else
        :data-meeting-time-id="applicationInfo.meetingTimeId"
        @tap="stopMeeting"
      >
        <text class="oa-icon oa-icon-guanbi1 btn_item_icon"></text>
        <text>结束会议</text>
      </button>
    </view>
  </view>
</template>

<script>
import uniSegmentedControl from '@/components/uni-segmented-control/uni-segmented-control.vue';
import Base64 from '@/common/js/base64.min.js';
export default {
  components: {
    uniSegmentedControl
  },
  data() {
    return {
      showContent: false,
      formTitle: '',
      items: ['基本信息', '文件', '议程'],
      current: 0,
      activeColor: '#005BAC',
      styleType: 'text',
      applicationInfo: {}
    };
  },
  onLoad(opt) {
    this.formTitle = opt.subject;
    this.getData(opt.id);
  },
  methods: {
    //获取数据
    getData(id) {
      this.ajax.getMeetingDetail(id).then(res => {
        let data = res.object;
        let fileName = data.accessoryName
          ? data.accessoryName.split('#,#')
          : [];
        let fileId = data.accessoryId ? data.accessoryId.split('#,#') : [];
        data.accessoryName = fileName;
        data.accessoryId = fileId;
        this.applicationInfo = data;
        this.showContent = true;
      });
    },
    //tab切换事件
    onClickItem(e) {
      if (this.current !== e.currentIndex) {
        this.current = e.currentIndex;
      }
    },
    //查看附件详情
    downloadFile(id) {
      this.ajax
        .getFiles({
          idsStr: id
        })
        .then(res => {
          let filePath = `${
            this.$config.ENABLE_FILE_PREVIEW
              ? this.$config.DOCUMENT_BASE_HOST
              : this.$config.BASE_HOST
          }/ts-document/attachment/downloadFile/${id}?fullfilename=${
            res.object[0].fileName
          }&source=mobile`;
          if (this.$config.ENABLE_FILE_PREVIEW) {
            uni.navigateTo({
              url: `/pages/webview/webview?url=${
                this.$config.BASE_HOST
              }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
            });
          } else {
            this.$downloadFile.downloadFile(filePath);
          }
        });
    },
    //结束会议
    stopMeeting(e) {
      let data = e.currentTarget.dataset;
      this.ajax
        .updateMeeting({
          id: data.meetingTimeId
        })
        .then(res => {
          this.applicationInfo.meetingStatus = '-1';
        });
    },
    //返回上一层
    returnBack() {
      uni.redirectTo({
        url: '/pages/meeting/meeting-list?type=meeting-reference'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  .dis_flex {
    display: flex;
    justify-content: center;
  }
  .content_top {
    padding: 0 30rpx;
    background-color: #fff;
    position: relative;
    &::after {
      position: absolute;
      content: '';
      bottom: 1px;
      left: 0;
      right: 0;
      transform: scaleY(-0.5);
      height: 1px;
      background-color: #eee;
    }
  }
  .content {
    flex: 1;
    overflow: hidden;
    margin-bottom: 100rpx;
    .content_tab_item {
      height: 100%;
      width: 100%;
      overflow-y: auto;
    }
    .row-group {
      margin-top: 20rpx;
      &:first-child {
        margin-top: 0;
      }
      .row {
        width: 100%;
        background-color: #ffffff;
        position: relative;
        &::after {
          position: absolute;
          content: '';
          bottom: 0;
          left: 30rpx;
          right: 0;
          transform: scaleY(-0.5);
          height: 1px;
          background-color: #eee;
        }
        &:last-child::after {
          height: 0;
        }
        .row_lable {
          width: 200rpx;
          font-size: 30rpx;
          color: #999;
          padding: 22rpx 30rpx;
          box-sizing: border-box;
          position: relative;
          text-align: right;
          .row_lable_text {
            box-sizing: border-box;
          }
        }
        .row_lable ~ .row_value {
          flex: 1;
          font-size: 30rpx;
          color: #333;
          padding: 22rpx 30rpx;
          padding-left: 0;
          box-sizing: border-box;
          text-align: left;
        }
      }
    }
    .fiel_list {
      .file_item {
        text-decoration: none;
        background-color: #fff;
        padding: 22rpx 30rpx;
        display: flex;
        align-items: center;
        position: relative;
        color: #333;
        font-size: 28rpx;
        &::after {
          position: absolute;
          content: '';
          bottom: 0;
          left: 30rpx;
          right: 0;
          transform: scaleY(-0.5);
          height: 1px;
          background-color: #eee;
        }
        .oa-icon {
          font-size: 40rpx;
          margin-right: 10rpx;
        }
      }
    }
  }
  .agenda_list {
    .agenda_item {
      position: relative;
      width: 100%;
      padding: 22rpx 30rpx;
      background-color: #fff;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 30rpx;
        right: 0;
        transform: scaleY(-0.5);
        height: 1px;
        background-color: #eee;
      }
      &:last-child::after {
        height: 0;
      }
      .agenda_item_subject {
        color: #333333;
        font-size: 32rpx;
      }
      .agenda_item_content,
      .agenda_item_person {
        font-size: 28rpx;
        color: #666;
      }
    }
  }
  .bottomBtn {
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    background-color: #ffffff;
    uni-button:after {
      border-radius: 0;
      border: 0;
      border-top: 1px solid rgba(0, 0, 0, 0.2);
    }
    .btn_item {
      height: 100%;
      padding: 10rpx 30rpx;
      box-sizing: border-box;
      text-align: center;
      font-size: 32rpx;
      position: relative;
      border-radius: 0;
      .btn_item_icon {
        margin-right: 10rpx;
      }
      &.btn_item_finished {
        background-color: $uni-bg-color-grey;
        color: $uni-text-color-grey;
      }
      &.btn_item_ongoing {
        background-color: $uni-bg-color;
        color: $theme-color;
      }
    }
  }
  .nothing {
    position: absolute;
    top: 40%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .img_content {
      width: 300rpx;
      height: 300rpx;
      .nothing_img {
        width: 100%;
        height: 100%;
      }
    }
    .tips_text {
      color: #666666;
    }
  }
}
</style>
