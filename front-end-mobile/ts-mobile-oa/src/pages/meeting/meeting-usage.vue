<template>
  <view class="ts-content" v-if="showContent">
    <page-head @clickLeft="returnBack" title="会议室使用情况"></page-head>
    <view class="calendar_wrap">
      <!-- 插入模式 -->
      <lxCalendar @change="calendarChange"></lxCalendar>
    </view>
    <view class="cal_view_tab">
      <view class="cal_item">
        <text class="legend" style="background-color: #FFEEEF;"></text>
        占用
      </view>
      <view class="cal_item">
        <text
          class="legend"
          style="background-color: rgba(0, 191, 208, 0.1);"
        ></text>
        预约中
      </view>
      <view class="cal_item">
        <text class="legend"></text>
        空
      </view>
      <view
        class="cal_item"
        style="flex: 1; justify-content: flex-end;margin: 0;"
      >
        <text
          class="oa-icon oa-icon-zhuye change_item"
          :class="listType == 'tableTab' ? 'active' : ''"
          data-list-type="tableTab"
          @click="changeListType"
        ></text>
        <text
          class="oa-icon oa-icon-liebiao change_item"
          :class="listType == 'listTab' ? 'active' : ''"
          data-list-type="listTab"
          @click="changeListType"
        ></text>
      </view>
    </view>
    <view class="meeting_room_list">
      <view v-show="listType == 'tableTab'" class="table_tab">
        <view
          class="meeting_room_item"
          v-for="item in tableDataList"
          :key="item.id"
        >
          <view class="item_title">{{ item.name }}</view>
          <view class="item_describe">
            <view class="describe_item">
              <uni-icons type="person" color="#005BAC" size="30" />
              <text>{{ item.capacitance }}</text>
            </view>
            <view class="describe_item">
              <uni-icons type="location" color="#005BAC" size="30" />
              <text>{{ item.location }}</text>
            </view>
          </view>
          <view class="time_table">
            <uni-grid
              :column="24"
              :square="false"
              :highlight="false"
              @change="clickChange"
              tWidth="70rpx"
              tHeight="40rpx"
              flexWrap="nowrap"
            >
              <view
                class="column_item"
                v-for="(e, i) in item.apply_hour"
                :key="i"
              >
                <uni-grid-item :index="i">
                  <view class="grid-item-box" :class="e ? e : ''"></view>
                </uni-grid-item>
                <view class="time_num">
                  {{ i % 2 == 0 ? i : '' }}
                </view>
              </view>
            </uni-grid>
          </view>
        </view>
      </view>
      <view v-show="listType == 'listTab'" class="list_tab">
        <view class="mescroll-content">
          <mescroll
            ref="mescroll"
            @getDatas="getListData"
            @setDatas="setListData"
            @datasInit="datasInit"
          >
            <view
              class="meeting_room_item"
              v-for="item in listDataList"
              :key="item.id"
            >
              <view class="item_title">{{ item.motif }}</view>
              <view class="item_describe">
                <view v-if="item.location" class="describe_item">
                  <uni-icons type="location" color="#005BAC" size="30" />
                  <text>{{ item.location + '-' + item.name }}</text>
                </view>
                <view
                  v-if="item.status == '0'"
                  class="describe_status"
                  style="color: #F59A23;"
                >
                  待审核
                </view>
                <view
                  v-else-if="item.status == '1'"
                  class="describe_status"
                  style="color: #3aad73;"
                >
                  预定成功
                </view>
                <view
                  v-else-if="item.status == '-1'"
                  class="describe_status"
                  style="color: #dd1f36;"
                >
                  未通过
                </view>
              </view>
              <view class="item_personal">
                <text>{{
                  item.startTime.substring(0, 10) +
                    ' ' +
                    item.startTime.substring(11, 16) +
                    '-' +
                    item.endTime.substring(11, 16)
                }}</text>
                <text>{{ item.applyEmpname }}</text>
              </view>
            </view>
          </mescroll>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import lxCalendar from '@/components/lx-calendar/lx-calendar.vue';
import uniGrid from '@/components/uni-grid/uni-grid.vue';
import uniGridItem from '@/components/uni-grid-item/uni-grid-item.vue';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    lxCalendar,
    uniGrid,
    uniGridItem,
    mescroll
  },
  data() {
    return {
      fromPage: '',
      showContent: false,
      tableDataList: [],
      listDataList: [],
      listType: 'tableTab',
      selectedDate: ''
    };
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage ? opt.fromPage : '';
    let current = this.$common.getDate('date').timeStr;
    this.selectedDate = `${current} 00:00:00`;
    this.getTableData(`${current} 00:00:00`);
  },
  methods: {
    getTableData(date) {
      this.ajax
        .getBoardroomApplyBylist({
          pattern: 0,
          searchTime: date
        })
        .then(res => {
          let data = res.object;
          data.forEach(e => {
            let occupy_time = {},
              reserve_time = {};
            e.boardroomApply.forEach(v => {
              let s_time = this.$common.getDate('date', v.startTime);
              let e_time = this.$common.getDate('date', v.endTime);
              if (s_time.timeStr == e_time.timeStr && v.isArtificial != 2) {
                let s_hour = s_time.hour;
                let e_hour = e_time.hour;
                if (e_time.minute > 0 || e_time.seconds > 0) e_hour += 2;
                for (let d = s_hour; d < e_hour; d++) {
                  if (v.status == '0') {
                    reserve_time[d] = 'reserve';
                  } else if (v.status == '1') {
                    occupy_time[d] = 'occupy';
                  }
                }
              }
            });
            let apply_hour = { ...occupy_time, ...reserve_time };
            let all_hour = [];
            for (let k = 0; k < 24; k++) {
              let state = false;
              if (apply_hour[k] != undefined) {
                state = apply_hour[k];
              }
              all_hour.push(state);
            }
            this.tableDataList.push({
              id: e.id,
              name: e.name,
              capacitance: e.capacitance,
              location: e.location,
              apply_hour: all_hour
            });
          });
          this.showContent = true;
        });
    },
    async getListData(page, successCallback, errorCallback, keywords) {
      await this.ajax
        .getBoardroomApplyList({
          pageSize: page.size,
          pageNo: page.num,
          searchTime: this.selectedDate,
          pattern: 0,
          sidx: 'START_TIME',
          sord: 'asc'
        })
        .then(res => {
          let rows = res.rows;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(row) {
      this.listDataList = this.listDataList.concat(row);
    },
    datasInit() {
      this.listDataList = [];
    },
    //日历切换事件
    calendarChange(e) {
      this.tableDataList = [];
      this.selectedDate = `${e.fulldate} 00:00:00`;
      this.getTableData(`${e.fulldate} 00:00:00`);
      this.$nextTick(() => {
        this.$refs['mescroll'].downCallback();
      });
    },
    //点击 grid 触发
    clickChange(e) {},
    //表格和列表的切换
    changeListType(e) {
      let data = e.currentTarget.dataset;
      this.listType = data.listType;
    },
    //返回上一层
    returnBack() {
      if (this.fromPage === 'workBench') {
        this.$parentTypeFun({
          type: 'jumpPage',
          path: '/workbench'
        });
      } else {
        const pages = getCurrentPages(); //获取页面栈
        if (pages.length === 1) {
          //如果只有一个调用原生js
          history.back();
        } else {
          uni.navigateBack();
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  .calendar_wrap {
    z-index: 10;
  }
  .cal_view_tab {
    padding: 22rpx 30rpx;
    background-color: #fff;
    display: flex;
    justify-content: flex-start;
    .cal_item {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      margin-right: 20rpx;
      .legend {
        width: 40rpx;
        height: 40rpx;
        display: inline-block;
        border: 1px solid #dddddd;
        box-sizing: border-box;
        margin-right: 8rpx;
      }
      .change_item {
        color: #bbb;
        border: 1px solid #ccc;
        padding: 0 8rpx;
        border-radius: 2rpx;
        &.active {
          background-color: #005bac;
          border: 1px solid #005bac;
          color: #ffffff;
        }
      }
    }
  }
  .meeting_room_list {
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table_tab,
    .list_tab {
      overflow-y: auto;
      width: 100%;
      height: 100%;
      .mescroll-content {
        position: relative;
        height: 100%;
      }
    }
    .meeting_room_item {
      padding: 10rpx 30rpx;
      width: 100%;
      margin-top: 20rpx;
      background-color: #ffffff;
      box-sizing: border-box;
      .item_title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
      }
      .item_describe {
        display: flex;
        align-items: center;
        .describe_item {
          font-size: 28rpx;
          padding: 0 10rpx;
          color: #666;
          &:first-child {
            padding-left: 0;
          }
        }
        .describe_status {
          font-size: 28rpx;
          text-align: right;
          flex: 1;
        }
      }
      .item_personal {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        font-size: 28rpx;
        color: #666;
      }
      .time_table {
        width: 100%;
        overflow-x: auto;
        .column_item {
          .occupy {
            background-color: #ffeeef;
            height: 100%;
          }
          .reserve {
            background-color: rgba(0, 191, 208, 0.1);
            height: 100%;
          }
          .time_num {
            font-size: 24rpx;
            padding: 2px 0;
            text-align: center;
          }
        }
      }
    }
  }
}
</style>
