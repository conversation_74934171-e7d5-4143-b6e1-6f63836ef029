export default {
  data() {
    return {
      proportionOutpatientInpatientIncomeMzData: {
        series: [
          {
            data: []
          }
        ]
      },
      proportionOutpatientInpatientIncomeMzList: [],
      proportionOutpatientInpatientIncomeMzOpts: {
        fontSize: 12,
        touchMoveLimit: 60,
        enableScroll: true,
        dataLabel: false,
        dataPointShape: false,
        dataPointShapeType: 'hollow',
        legend: {
          show: false,
          position: 'right',
          formatter: '1'
        },
        extra: {
          ring: {
            ringWidth: 25,
            border: false
          }
        },
        title: {
          name: '门诊',
          color: '#929292',
          fontSize: 14,
          fontWeight: 600
        },
        subtitle: {
          name: ''
        }
      },

      proportionOutpatientInpatientIncomeZyData: {
        series: [
          {
            data: []
          }
        ]
      },
      proportionOutpatientInpatientIncomeZyList: [],
      proportionOutpatientInpatientIncomeZyOpts: {
        fontSize: 12,
        touchMoveLimit: 60,
        enableScroll: true,
        dataLabel: false,
        dataPointShape: false,
        dataPointShapeType: 'hollow',
        legend: {
          show: false,
          position: 'right',
          formatter: '1'
        },
        extra: {
          ring: {
            ringWidth: 25,
            border: false
          }
        },
        title: {
          name: '住院',
          color: '#929292',
          fontSize: 14,
          fontWeight: 600
        },
        subtitle: {
          name: ''
        }
      },

      weekIncomeChartsData: {
        categories: [],
        series: [
          {
            name: '去年同期',
            data: [],
            barWidth: 10,
            itemStyle: {
              color: '#79CFB9'
            },
            label: {
              show: false,
              color: '#79CFB9',
              fontSize: 10,
              rotate: 25
            }
          },
          {
            name: '本期',
            data: [],
            barWidth: 10,
            itemStyle: {
              color: '#336BBB'
            },
            label: {
              show: false,
              color: '#366DBC',
              fontSize: 10,
              rotate: 25
            }
          }
        ]
      },
      weekIncomeChartsOpts: {
        grid: {
          left: '2%',
          right: '2%',
          bottom: '0%',
          top: '10%',
          containLabel: true
        },
        legend: {
          show: true,

          top: '0%', // 距离顶部的距离
          right: '0%' // 距离右侧的距离
        },
        xAxis: {
          trigger: 'axis',
          axisTick: {
            alignWithLabel: true
          },
          axisPointer: {
            type: 'shadow',
            snap: true
          },
          axisLabel: {
            fontSize: 11
          }
        },
        yAxis: {
          type: 'value'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          triggerOn: 'mousemove|click',
          formatter: '{b}\n{a0}: {c0} 万元\n{a1}: {c1} 万元',
          textStyle: {
            whiteSpace: 'normal'
          }
        }
      }
    };
  }
};
