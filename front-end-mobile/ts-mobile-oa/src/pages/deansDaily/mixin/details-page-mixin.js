export default {
  data() {
    return {
      personChartsData: {},
      personuChartsOpts: {
        fontSize: 12,
        enableScroll: true,
        touchMoveLimit: 60,
        dataLabel: true,
        dataPointShape: true,
        dataPointShapeType: 'hollow',
        legend: {
          show: true,
          position: 'right'
        },
        extra: {
          ring: {
            ringWidth: 20,
            border: false
          }
        },
        title: {
          name: '人员去向',
          color: '#929292',
          fontSize: 14,
          fontWeight: 600
        },
        subtitle: {
          name: '',
          color: '#333',
          fontSize: 14,
          offsetY: 4
        }
      },

      incomeChartsData: {
        series: [
          {
            data: [],
            barWidth: 15,
            itemStyle: {
              color: '#336BBB'
            },
            label: {
              show: true,
              color: '#366DBC',
              fontSize: 12,
              formatter: '{c} 万'
            }
          }
        ]
      },
      incomeChartsOpts: {
        backgroundColor: '#eff4fc', // 设置整个图表的背景颜色
        grid: {
          left: '5%',
          right: '5%',
          bottom: '5%',
          top: '5%',
          containLabel: true
        },
        legend: {
          show: false
        },
        xAxis: {
          trigger: 'axis',
          axisTick: {
            alignWithLabel: true
          },
          axisPointer: {
            type: 'shadow',
            snap: true
          }
        },
        yAxis: {
          name: '收入（万元）',
          type: 'value'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          triggerOn: 'mousemove|click',
          formatter: '{b} {a}: {c}万元'
        }
      }
    };
  }
};
