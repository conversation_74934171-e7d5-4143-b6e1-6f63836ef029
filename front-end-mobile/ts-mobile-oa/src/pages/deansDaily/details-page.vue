<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" leftText="日报" />

    <view class="content-container">
      <component-head
        pageSubjectTitle="领导日报"
        :dailyBeginDate="dailyBeginDate"
      />

      <view class="module-title">人员动态</view>
      <component-person-total-module
        :index="0"
        :paramsDate="paramsDate"
        :onTheJobTotal="onTheJobTotal"
        :onThePostTotal="onThePostTotal"
        :personnelDirectionTotal="personnelDirectionTotal"
        :personnelDirectionList="personnelDirectionList"
        :abnormalPersonTotal="abnormalPersonTotal"
        :abnormalPersonList="abnormalPersonList"
      />

      <view class="module-title">收入情况</view>
      <view class="income-situation">
        <view class="income-number">
          <view
            class="income-number-item"
            v-for="item in IncomeSituationConfig"
            :key="item.key"
          >
            <view class="label">{{ item.label }}</view>
            <view :class="{ [item.className]: true }">
              {{ IncomeSituationObject[item.key] | formatNumber }}
            </view>
          </view>
        </view>

        <view class="echarts-label">近七天收入趋势(万元)</view>
        <view class="income-charts-box">
          <qiun-data-charts
            type="column"
            :chartData="incomeChartsData"
            :loadingType="1"
            :disableScroll="true"
            canvasId="income-charts"
            :canvas2d="true"
            background="none"
            :animation="false"
            :ontouch="true"
            :echartsH5="true"
            :echartsApp="false"
            :eopts="incomeChartsOpts"
            :opts="incomeChartsOpts"
          />
        </view>
      </view>

      <view class="module-title">门诊与住院情况</view>
      <view class="outpatient-inpatient-situations">
        <view class="container-head-title">
          门诊情况
        </view>
        <view class="outpatient-situation-number">
          <view
            class="outpatient-situation-item"
            v-for="item in OutpatientSituationConfig"
            :key="item.key"
          >
            <view :class="{ [item.labelClass]: true, label: true }">
              {{ formatLabel(item.label, item.lineBreak) }}
            </view>
            <view :class="{ [item.valClass]: true }">
              {{ OutpatientSituationObject[item.key] }}
            </view>
          </view>
        </view>

        <view class="container-head-title">
          住院情况
        </view>
        <view class="hospitalization-status-number">
          <view
            class="hospitalization-status-item"
            v-for="item in HospitalizationConfig"
            :key="item.key"
          >
            <view :class="{ [item.labelClass]: true, label: true }">
              {{ formatLabel(item.label, item.lineBreak) }}
            </view>
            <view :class="{ [item.valClass]: true }">
              {{ HospitalizationObject[item.key] }}
            </view>
          </view>
        </view>
      </view>

      <view class="module-title">住院科室业务数据</view>
      <view class="inpatient-department-business-data">
        <fixed-table-dept
          :data="deptListData"
          :header="columns"
          :fixed="true"
          :fixedFirstAndSecond="true"
          :border="true"
          :stripe="true"
          :showActions="true"
        />
      </view>
    </view>
  </view>
</template>

<script>
import common from '@/common/js/common.js';
import detailsPageMixin from './mixin/details-page-mixin.js';
import ComponentHead from './components/component-head.vue';
import ComponentPersonTotalModule from './components/component-person-total-module.vue';
import QiunDataCharts from '@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue';
import FixedTableDept from './components/fixed-table-dept.vue';
export default {
  components: {
    ComponentHead,
    ComponentPersonTotalModule,
    FixedTableDept,
    QiunDataCharts
  },
  mixins: [detailsPageMixin],
  data() {
    return {
      tabIndex: undefined,
      dailyBeginDate: undefined,
      logoImgPath: undefined,

      id: undefined,
      onTheJobTotal: 0,
      onThePostTotal: 0,
      personnelDirectionTotal: 0,
      personnelDirectionList: [],
      abnormalPersonTotal: 0,
      abnormalPersonList: [],

      IncomeSituationObject: {},
      IncomeSituationConfig: [
        {
          label: '总收入',
          key: 'hj_zsr',
          className: 'blue'
        },
        {
          label: '门急诊收入',
          key: 'mz_zsr',
          className: 'org'
        },
        {
          label: '住院收入',
          key: 'zy_zsr',
          className: 'green'
        },
        {
          label: '体检收入',
          key: 'hj_tjsr',
          className: 'blue'
        },
        {
          label: '门诊均次费用',
          key: 'mz_jcfy',
          className: 'org'
        },
        {
          label: '住院均次费用',
          key: 'zy_jcfy',
          className: 'green'
        }
      ],

      OutpatientSituationObject: {},
      OutpatientSituationConfig: [
        {
          label: '门急诊人次',
          key: 'mz_mjzrc',
          labelClass: 's09',
          valClass: 'green',
          lineBreak: 3
        },
        {
          label: '门诊人次',
          key: 'mz_mzrc',
          labelClass: '',
          valClass: 'blue',
          lineBreak: 2
        },
        {
          label: '急诊人次',
          key: 'mz_jzrc',
          labelClass: '',
          valClass: 'blue',
          lineBreak: 2
        },
        {
          label: '预约就诊人次',
          key: 'mz_yyrc',
          labelClass: 's09',
          valClass: 'blue',
          lineBreak: 2
        },
        {
          label: '预约就诊率',
          key: 'mz_yyjzl',
          labelClass: 's09',
          valClass: 'blue',
          lineBreak: 2
        }
      ],

      HospitalizationObject: {},
      HospitalizationConfig: [
        {
          label: '在院人次',
          key: 'zy_zyrc',
          labelClass: '',
          valClass: 'green',
          lineBreak: 2
        },
        {
          label: '入院人次',
          key: 'zy_ryrc',
          labelClass: '',
          valClass: 'blue',
          lineBreak: 2
        },
        {
          label: '出院人次',
          key: 'zy_cyrc',
          labelClass: '',
          valClass: 'blue',
          lineBreak: 2
        },
        {
          label: '手术人次',
          key: 'zy_ssrc',
          labelClass: '',
          valClass: 'blue',
          lineBreak: 2
        },
        {
          label: '留观人次',
          key: 'zy_lgrc',
          labelClass: '',
          valClass: 'blue',
          lineBreak: 2
        },
        {
          label: '病危人次',
          key: 'zy_bwrc',
          labelClass: '',
          valClass: 'blue',
          lineBreak: 2
        },
        {
          label: '病重人次',
          key: 'zy_bzrc',
          labelClass: '',
          valClass: 'blue',
          lineBreak: 2
        },
        {
          label: '死亡人次',
          key: 'zy_swrc',
          labelClass: '',
          valClass: 'blue',
          lineBreak: 2
        },
        {
          label: '分娩人次',
          key: 'zy_fmrc',
          labelClass: '',
          valClass: 'blue',
          lineBreak: 2
        },
        {
          label: '平均住院日',
          key: 'zy_pjzyr',
          labelClass: 's09',
          valClass: 'blue',
          lineBreak: 2
        }
      ],
      paramsDate: {},
      detailsObject: {},

      columns: [
        {
          prop: 'index',
          align: 'center',
          title: '序'
        },
        {
          prop: 'dept',
          title: '科室',
          align: 'left',
          overflow: true
        },
        {
          prop: 'zy_ryrc',
          title: '入院',
          align: 'center',
          width: 100
        },
        {
          prop: 'zy_cyrc',
          title: '出院',
          align: 'center',
          width: 100
        },
        {
          prop: 'zy_zyrc',
          title: '在院',
          align: 'center',
          width: 100
        },
        {
          prop: 'zy_bzrc',
          title: '病重',
          align: 'center',
          width: 100
        },
        {
          prop: 'zy_bwrc',
          title: '病危',
          align: 'center',
          width: 100
        },
        {
          prop: 'kcs',
          title: '余床数',
          align: 'center',
          width: 120,
          formatter: (val, row) => {
            let formatterVal = (row.bzcwsyl * 100).toFixed(2);
            const numericVal = parseFloat(formatterVal);
            if (numericVal >= 100) {
              return `<text style="color: red">${val}</text>`;
            }
            if (numericVal == 0) {
              return val;
            }
            if (numericVal < 50) {
              return `<text style="color: #53CBB5">${val}</text>`;
            }
            return val;
          }
        },
        {
          prop: 'bzcwsyl',
          title: '床位使用率',
          align: 'center',
          width: 200,
          formatter: (val, row) => {
            let formatterVal = (row.bzcwsyl * 100).toFixed(2);
            const numericVal = parseFloat(formatterVal);

            if (numericVal >= 100) {
              return `<text style="color: red">${formatterVal}%</text>`;
            }
            if (numericVal == 0) {
              return `${formatterVal}%`;
            }
            if (numericVal < 50) {
              return `<text style="color: #53CBB5">${formatterVal}%</text>`;
            }
            return `${formatterVal}%`;
          }
        },
        {
          prop: 'cws',
          align: 'center',
          title: '床位数',
          width: 120
        },
        {
          prop: 'zy_ssrc',
          title: '手术人次',
          align: 'center',
          width: 150
        },
        {
          prop: 'zy_yjssrc',
          title: '一级',
          align: 'center',
          width: 100
        },
        {
          prop: 'zy_ejssrc',
          title: '二级',
          align: 'center',
          width: 100
        },
        {
          prop: 'zy_sanjssrc',
          title: '三级',
          align: 'center',
          width: 100
        },
        {
          prop: 'zy_sijssrc',
          title: '四级',
          align: 'center',
          width: 100
        }
      ],
      deptListData: []
    };
  },

  async onLoad(opt) {
    let _self = this;
    _self.tabIndex = opt.index;
    _self.id = opt.id;
    let details = await _self.handleGetLeaderDailyDetails(_self.id);
    if (!details) {
      return;
    }
    _self.ajax.LeaderDailyReadClickSaveUpdate({ dailyId: opt.id });

    let { dailyBeginDate, dailyEndDate } = details;
    _self.dailyBeginDate = common.formatDateText(dailyBeginDate);
    _self.paramsDate = {
      start_date: dailyBeginDate,
      end_date: dailyEndDate
    };

    await _self.handleGetHrindexGetDayDyna();
    _self.handleGetHrindexGetPsnTrns();
    _self.handleGetHrindexGetPsnDstn();
    _self.handleGgatewayGetLeaderDailyAllSingleZb();
  },
  filters: {
    formatNumber(value) {
      if (!value) return '0';
      const number = parseFloat(value);
      if (isNaN(number)) return value;
      return number.toLocaleString('en-US');
    }
  },
  methods: {
    async handleGetLeaderDailyDetails(id) {
      let _self = this;
      const res = await _self.ajax.LeaderDailyDetails(id);
      if (!res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message || '获取详情失败,请联系管理员'
        });
        return false;
      }
      return res.object;
    },

    async handleGetHrindexGetDayDyna() {
      let _self = this;
      const res = await _self.ajax.hrindexGetDayDyna({
        type: 5,
        ..._self.paramsDate
      });
      if (!res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message || '获取数据失败,请联系管理员'
        });
        return false;
      }

      _self.onTheJobTotal = Number(res.object?.total_cnt || 0);
    },

    async handleGetHrindexGetPsnDstn() {
      let _self = this;
      const res = await _self.ajax.hrindexGetPsnDstn({
        type: 5,
        ..._self.paramsDate
      });
      if (!res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message || '获取数据失败,请联系管理员'
        });
        return false;
      }

      let list = res.object.dataList || [];
      let data = list.map(m => {
        return {
          name: m.leave_type,
          type: m.type,
          value: Number(m.cnt)
        };
      });
      data.sort((a, b) => b.value - a.value);
      // 人员去向
      _self.personnelDirectionTotal = data.reduce(
        (sum, item) => sum + item.value,
        0
      );
      // 在岗人员
      _self.onThePostTotal =
        _self.onTheJobTotal - _self.personnelDirectionTotal;
      data = data.filter(f => f.value > 0);
      _self.personnelDirectionList = data;
    },

    async handleGetHrindexGetPsnTrns() {
      let _self = this;
      const res = await _self.ajax.hrindexGetPsnTrns({
        type: 5,
        ..._self.paramsDate
      });
      if (!res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message || '获取数据失败,请联系管理员'
        });
        return false;
      }
      let list = res.object.dataList || [];
      let data = list.map(m => {
        return {
          name: m.cause,
          value: Number(m.cnt)
        };
      });
      data.sort((a, b) => b.value - a.value);
      // 人员去向
      _self.abnormalPersonTotal = data.reduce(
        (sum, item) => sum + item.value,
        0
      );
      data = data.filter(f => f.value > 0);
      _self.abnormalPersonList = data;
    },

    async handleGgatewayGetLeaderDailyAllSingleZb() {
      let _self = this;
      const res = await _self.ajax.gatewayGetLeaderDailyAllSingleZb({
        ..._self.paramsDate
      });
      if (!res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message || '获取数据失败,请联系管理员'
        });
        return false;
      }
      _self.detailsObject = res.object;
      let sevenIncomeChartList = res.object.sevenIncomeChartList || [];
      let categories = [];
      let thisIssue = [];
      sevenIncomeChartList.forEach(item => {
        categories.push(`${item.pdate.substring(item.pdate.length - 2)}日`);
        thisIssue.push(item.sr);
      });

      _self.incomeChartsData.categories = categories;
      _self.incomeChartsData.series[0]['data'] = thisIssue;

      let {
        hj_zsr = 0,
        mz_zsr = 0,
        zy_zsr = 0,
        hj_tjsr = 0,
        mz_jcfy = 0,
        zy_jcfy = 0
      } = res.object?.AllSingleZbData || {};
      _self.IncomeSituationObject = {
        hj_zsr,
        mz_zsr,
        zy_zsr,
        hj_tjsr,
        mz_jcfy,
        zy_jcfy
      };

      let {
        mz_mjzrc = 0,
        mz_mzrc = 0,
        mz_jzrc = 0,
        mz_yyrc = 0,
        mz_yyjzl = 0
      } = res.object?.AllSingleZbData || {};
      _self.OutpatientSituationObject = {
        mz_mjzrc,
        mz_mzrc,
        mz_jzrc,
        mz_yyrc,
        mz_yyjzl: (mz_yyjzl * 100).toFixed(2) + '%'
      };

      let {
        zy_zyrc = 0,
        zy_ryrc = 0,
        zy_cyrc = 0,
        zy_ssrc = 0,
        zy_lgrc = 0,
        zy_bwrc = 0,
        zy_bzrc = 0,
        zy_swrc = 0,
        zy_fmrc = 0,
        zy_pjzyr = 0
      } = res.object?.AllSingleZbData || {};
      _self.HospitalizationObject = {
        zy_zyrc,
        zy_ryrc,
        zy_cyrc,
        zy_ssrc,
        zy_lgrc,
        zy_bwrc,
        zy_bzrc,
        zy_swrc,
        zy_fmrc,
        zy_pjzyr
      };
      ['zy_bwrc', 'zy_bzrc', 'zy_swrc'].forEach(key => {
        if (_self.HospitalizationObject[key] > 0) {
          const config = _self.HospitalizationConfig.find(f => f.key === key);
          if (config) config.valClass = 'red';
        }
      });

      let hospDeptZbDataList = res.object.hospDeptZbDataList || [];
      _self.deptListData = hospDeptZbDataList.map((m, index) => {
        return {
          ...m,
          index: index + 1
        };
      });
      _self.$forceUpdate();
    },
    formatLabel(label, lineBreak) {
      if (!lineBreak || lineBreak <= 0) return label;
      const firstLine = label.slice(0, lineBreak); // 第一行的字符
      const secondLine = label.slice(lineBreak); // 第二行的字符
      return secondLine ? `${firstLine}\n${secondLine}` : firstLine;
    },
    returnBack() {
      uni.redirectTo({
        url: '/pages/deansDaily/index?index=0'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  background: #fff;

  ::v-deep {
    .blue {
      color: #4775c0 !important;
    }
    .org {
      color: #e99d42 !important;
    }
    .green {
      color: #79cfb9 !important;
    }
    .red {
      color: red !important;
    }
    .abnormal {
      color: #52cbb4 !important;
    }
    .destination {
      color: #e97582 !important;
    }
    .s09 {
      transform: scale(0.95) !important;
    }
  }

  .content-container {
    padding: 0 16rpx;

    .module-title {
      font-weight: bold;
      font-size: 36rpx;
      margin-bottom: 16rpx;
    }

    //收入情况
    .income-situation {
      width: 100%;
      border: 1px solid #e7e7e7;
      border-radius: 16rpx;
      margin-bottom: 16rpx;
      padding: 0 16rpx;
      padding-bottom: 16rpx;
      background: #eff4fc;

      .income-number {
        display: flex;
        flex-wrap: wrap;

        .income-number-item {
          width: 33%;
          border-right: 2rpx dashed #d9dde3;
          border-bottom: 2rpx dashed #d9dde3;
          padding: 16rpx 0;
          > view {
            text-align: center;
            font-size: 32rpx;
          }
          .label {
            margin-bottom: 16rpx;
          }
          &:nth-child(3n) {
            border-right: 0rpx;
          }
        }
      }

      .echarts-label {
        font-size: 36rpx;
        margin: 16rpx 0;
        font-weight: bold;
      }

      .income-charts-box {
        background-color: #ffffff;
        height: 500rpx;
        width: 100%;
      }
    }

    //门诊与住院情况
    .outpatient-inpatient-situations {
      width: 100%;
      border: 1px solid #e7e7e7;
      border-radius: 16rpx;
      margin-bottom: 16rpx;
      padding: 0 16rpx;
      .container-head-title {
        padding-left: 16rpx;
        padding-top: 16rpx;
        padding-bottom: 16rpx;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }

      //门诊情况
      .outpatient-situation-number {
        display: flex;
        flex-wrap: wrap;

        .outpatient-situation-item {
          flex: 1;
          border-right: 2rpx dashed #d9dde3;
          border-top: 2rpx dashed #d9dde3;
          border-bottom: 2rpx dashed #d9dde3;
          padding: 16rpx 0;
          > view {
            white-space: nowrap;
            font-size: 32rpx;
            text-align: center;
          }
          .label {
            white-space: pre-wrap; /* 保证换行符生效 */
            text-align: center; /* 居中对齐 */
            display: inline-block; /* 保证多行文本居中 */
            width: 100%; /* 根据需要调整宽度 */
            line-height: 1.5; /* 设置行高 */
            margin-bottom: 16rpx;
          }
          &:last-child {
            border-right: 0rpx;
          }
        }
      }

      // 住院情况
      .hospitalization-status-number {
        display: flex;
        flex-wrap: wrap;

        .hospitalization-status-item {
          width: 20%;
          border-right: 2rpx dashed #d9dde3;
          border-top: 2rpx dashed #d9dde3;
          padding: 16rpx 0;
          > view {
            white-space: nowrap;
            font-size: 32rpx;
            text-align: center;
          }
          .label {
            white-space: pre-wrap; /* 保证换行符生效 */
            text-align: center; /* 居中对齐 */
            display: inline-block; /* 保证多行文本居中 */
            width: 100%; /* 根据需要调整宽度 */
            line-height: 1.5; /* 设置行高 */
            margin-bottom: 16rpx;
          }
          &:nth-child(5n) {
            border-right: 0rpx;
          }
        }
      }
    }

    //住院科室业务数据
    .inpatient-department-business-data {
      width: 100%;
      border: 1px solid #e7e7e7;
      border-radius: 16rpx;
      padding: 16rpx;
    }
  }

  ::v-deep .uni-navbar {
    .uni-navbar__header-btns {
      > .uni-navbar-btn-text {
        text {
          font-size: 32rpx !important;
          transform: translateY(-2rpx);
        }
      }
    }

    .uni-nav-bar-right-text {
      span {
        color: #333 !important;
      }
    }
  }
}
</style>
