<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="领导日报" />
    <scroll-view class="swiper_head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tab, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        :data-current="index"
        @click="ontabtap"
      >
        <view :class="tabIndex == index ? 'uni-tab-item-title-active' : ''">
          <text class="uni-tab-item-title">{{ tab.title }}</text>
        </view>
      </view>
    </scroll-view>
    <view class="select-month-container">
      <view class="date-container-left" @tap="handleSwitchSameMonth">
        <text
          :class="{
            the_same_month: true,
            active: dateIsAcitve
          }"
        >
          {{ dateTypeTitle }}
        </text>
      </view>
      <view class="select-month" @tap="handleSelectTap">
        {{ selectMonth | formatter }}
        <text></text>
        <img class="select-date" src="@/static/img/select-date.svg" alt="" />
      </view>
    </view>
    <swiper
      :current="tabIndex"
      class="swiper_box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper_item"
        v-for="(item, index) in tabBars"
        :key="index"
      >
        <mescroll
          :ref="'mescroll' + index"
          :mescrollIndex="index"
          :down="item.downOption"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view class="contact_list">
            <view
              class="contact_item"
              v-for="row in item['list']"
              :key="row.id"
              @tap="chooseItem(row, index)"
            >
              <view class="contact_item_row">
                <view class="contact_item_title">
                  <text class="title">{{ row.title }}</text>
                </view>
                <view
                  :class="{
                    contact_item_time: true,
                    active: row.readStatus == '0'
                  }"
                >
                  {{ row.readTitle }}
                </view>
              </view>
              <view class="contact_item_row">
                <view class="contact_item_title c999"> </view>
                <view class="contact_item_time">
                  {{ row.createDate | formatTime }}
                </view>
              </view>
            </view>
          </view>
        </mescroll>
      </swiper-item>
    </swiper>

    <date-picker
      ref="date"
      mode="date"
      startDate="2000-01-01"
      endDate="2100-12-31"
      :disabled-after="true"
      :value="picker.value"
      :fields="picker.fields"
      @confirm="onConfirm"
    />
  </view>
</template>

<script>
import moment from 'moment';

import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import datePicker from '@/components/picker/date-picker.vue';
import common from '@/common/js/common.js';

export default {
  components: {
    mescroll,
    datePicker
  },
  data() {
    return {
      selectMonth: undefined,
      sameMonth: moment().format('YYYY-MM'),
      sameYear: moment().format('YYYY'),
      picker: {},

      tabIndex: 0, //当前选中的tab索引值，从0计数
      tabBars: [
        {
          title: '日报',
          downOption: false, //初始化时是否下拉加载
          isInit: false, //是否已初始化
          list: []
        },
        {
          title: '周报',
          downOption: false,
          isInit: false,
          list: []
        },
        {
          title: '月报',
          downOption: false,
          isInit: false,
          list: []
        }
      ]
    };
  },
  onLoad(opt) {
    if (opt.index) this.tabIndex = Number(opt.index);
    this.selectMonth =
      this.tabIndex == 2 ? moment().format('YYYY') : moment().format('YYYY-MM');

    this.tabBars.map((item, index) => {
      if (this.tabIndex == index) {
        item.downOption = true;
        item.isInit = true;
      } else {
        item.downOption = false;
        item.isInit = false;
      }
    });
  },
  computed: {
    dateIsAcitve() {
      return (
        ([0, 1].includes(this.tabIndex) &&
          this.selectMonth == this.sameMonth) ||
        (this.tabIndex === 2 && this.selectMonth == this.sameYear)
      );
    },
    dateTypeTitle() {
      return this.tabIndex == 2 ? '今年' : '当月';
    }
  },

  filters: {
    formatter(val) {
      if (val.includes('-')) {
        let [year, month] = val.split('-');
        return `${year}年${month}月`;
      } else {
        return val + '年';
      }
    }
  },
  methods: {
    //tab点解切换
    async ontabtap(e) {
      let _self = this;
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      await _self.switchTab(Number(index));
    },
    //tab滑动切换
    async ontabchange(e) {
      let _self = this,
        index = e.target.current || e.detail.current;
      await _self.switchTab(Number(index));
    },
    async switchTab(index) {
      let _self = this;
      if (_self.tabIndex === index) {
        return;
      } else if (!_self.tabBars[index]['isInit']) {
        _self.tabBars[index]['isInit'] = true;
        await _self.$refs[`mescroll${index}`][0].downCallback();
      }
      _self.tabIndex = index;
      _self.handleSwitchSameMonth();
    },
    async getListData(page, successCallback, errorCallback, keywords, index) {
      let _self = this;
      await _self.ajax
        .LeaderDailyPageList({
          isMobile: true,
          type: _self.tabIndex,
          dailyBeginDate: _self.selectMonth,
          pageSize: page.size,
          pageNo: page.num,
          sidx: 'daily_end_date',
          sord: 'desc'
        })
        .then(async res => {
          let rows = res.rows || [];
          let data = rows.map(m => {
            let title;
            switch (_self.tabIndex) {
              case 0:
                title = common.formatDateText(m.dailyBeginDate);
                break;
              case 1:
                let endF = moment(m.dailyEndDate).format('MM-DD');
                endF = endF.replace('-', '月');
                endF = endF + '日';
                title = `${common.formatDateText(m.dailyBeginDate)}-${endF}`;
                break;
              case 2:
                let startF = moment(m.dailyBeginDate).format('YYYY-MM');
                startF = startF.replace('-', '年');
                startF = startF + '月';
                title = startF;
                break;
            }
            let readTitle = m.readStatus == '0' ? '未读' : '已读';
            return {
              ...m,
              title,
              readTitle
            };
          });
          await successCallback(data, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      let _self = this;
      _self.tabBars[index]['total'] = totalCount;
      let list = rows.filter(item => item.status != 0);
      _self.tabBars[index]['list'] = _self.tabBars[index]['list'].concat(list);
    },
    datasInit(keywords, index) {
      let _self = this;
      _self.tabBars[index]['list'] = [];
    },
    chooseItem(row, index) {
      let _self = this;
      let pagePath = '/pages/deansDaily/details-page';

      if (_self.tabIndex == 1) {
        pagePath = '/pages/deansDaily/details-page-week';
      }
      if (_self.tabIndex == 2) {
        pagePath = '/pages/deansDaily/details-page-month';
      }
      row.readStatus = '1';
      row.readTitle = '已读';
      _self.$nextTick(() => {
        let pagePramas = {
          index,
          id: row.id
        };
        let url = `${pagePath}?${_self.$common.convertObj(pagePramas)}`;
        uni.navigateTo({
          url
        });
      });
    },
    //显示时间选择弹出层
    handleSelectTap(e) {
      let _self = this;
      _self.picker = {
        fields: this.tabIndex == 2 ? 'year' : 'month',
        value: this.selectMonth
      };
      _self.$nextTick(function() {
        _self.$refs['date'].show();
      });
    },
    //时间选择确认
    onConfirm(res) {
      let _self = this;
      _self.selectMonth = res.result;
      _self.search();
    },
    handleSwitchSameMonth() {
      let _self = this;
      const target = _self.tabIndex === 2 ? _self.sameYear : _self.sameMonth;
      _self.selectMonth = target;
      _self.search();
    },
    //搜索
    search() {
      let _self = this;
      _self.tabBars.forEach(item => {
        item.list = [];
        item.isInit = false;
      });
      _self.$nextTick(() => {
        _self.tabBars[_self.tabIndex]['isInit'] = true;
        _self.$refs[`mescroll${_self.tabIndex}`][0].downCallback();
      });
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  .swiper_head {
    position: relative;
    width: 100%;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    /* #ifndef APP-PLUS */
    white-space: nowrap;
    /* #endif */
    /* flex-wrap: nowrap; */
    /* border-color: #cccccc;
			border-bottom-style: solid;
			border-bottom-width: 1px; */
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
    .uni-tab-item {
      /* #ifndef APP-PLUS */
      display: inline-block;
      /* #endif */
      flex-wrap: nowrap;
      width: 33%;
      padding-left: 34rpx;
      padding-right: 34rpx;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title,
      .uni-tab-item-num {
        color: #666;
        font-size: 30rpx;
        height: 100%;
        line-height: 2.5;
        flex-wrap: nowrap;
        box-sizing: border-box;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
      }
      .uni-tab-item-title-active {
        color: $theme-color;
        border-bottom: 2px solid $theme-color;
      }
    }
  }
  .swiper_box {
    flex: 1;
    .swiper_item {
      flex: 1;
      flex-direction: row;
    }
  }
  .contact_list {
    height: 100%;
    overflow: scroll;
  }

  .select-month-container {
    height: 84rpx;
    line-height: 84rpx;
    background-color: #ffffff;
    border-bottom: 2rpx solid #f1f1f1;
    display: flex;
    align-items: center;
    position: relative;
    .date-container-left {
      position: absolute;
      left: 16rpx;
      height: 100%;
      display: flex;
      align-items: center;

      .the_same_month {
        height: 50rpx;
        display: flex;
        align-items: center;
        padding: 0rpx 30rpx;
        border-radius: 36rpx;
        border: 2rpx solid #a9a9a9;
        &.active {
          border: 2rpx solid $theme-color;
          color: $theme-color;
        }
      }
    }
    .select-month {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #2c2c2c;

      .select-date {
        width: 40rpx;
        height: 40rpx;
        margin-left: 4px;
      }
    }
  }
  .contact_item {
    padding: 22rpx 30rpx;
    background-color: #ffffff;
    position: relative;
    .contact_item_row {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .contact_item_title {
        font-size: 32rpx;
        color: #333333;
        font-weight: bold;
        overflow: hidden;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex: 1;
        &.c999 {
          font-size: 24rpx !important;
          color: #999 !important;
          font-weight: normal;
        }
        .title {
          width: 100%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
        }
      }
      .contact_item_time {
        font-size: 24rpx !important;
        color: #949494;
        overflow: hidden;
        &.active {
          color: #005bac;
        }
      }
    }
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      height: 1px;
      background-color: #eee;
      left: 30rpx;
      right: 0;
    }
    &:last-child::after {
      height: 0;
    }
  }
}
</style>
