<template>
  <view>
    <view class="person-status-container">
      <view class="person-number" v-show="index == 0">
        <view class="person-number-item">
          <view>{{ titleObj.onTheJobTitle }}</view>
          <view class="blue">{{ onTheJobTotal }}</view>
        </view>
        <view class="person-number-item">
          <view>{{ titleObj.onThePostTitle }}</view>
          <view class="org">{{ onThePostTotal }}</view>
        </view>
      </view>

      <view
        class="module-list-container"
        v-show="personnelDirectionList.length"
      >
        <view class="module-left">
          <view>{{ titleObj.personnelDirectionTitle }}</view>
          <view class="destination">{{ personnelDirectionTotal }}人</view>
        </view>
        <view class="module-right">
          <view
            class="module-item"
            v-for="(item, index) in personnelDirectionList"
            :key="index"
            @tap.stop="handlePersonnelDirectionClick(item)"
          >
            <text class="text-name">{{ item.name }} </text>
            <view class="more-container">
              <text class="text-value destination">
                {{ item.value }}
              </text>
              <img
                class="more-deans-daily-number"
                src="@/static/img/right-deansDaily-first.svg"
              />
            </view>
          </view>
        </view>
      </view>

      <view class="module-list-container" v-show="abnormalPersonList.length">
        <view class="module-left">
          <view>{{ titleObj.abnormalPersonTitle }}</view>
          <view class="green">{{ abnormalPersonTotal }}人</view>
        </view>
        <view class="module-right">
          <view
            class="module-item"
            v-for="(item, index) in abnormalPersonList"
            :key="index"
            @tap.stop="handleAbnormalPersonClick(item)"
          >
            <text class="text-name">{{ item.name }} </text>
            <view class="more-container">
              <text class="text-value green">
                {{ item.value }}
              </text>
              <img
                class="more-deans-daily-number"
                src="@/static/img/right-deansDaily-second.svg"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <component-person-details ref="ComponentPersonDetails" />
  </view>
</template>

<script>
import ComponentPersonDetails from './component-person-details';
export default {
  components: {
    ComponentPersonDetails
  },
  props: {
    index: {
      type: Number,
      default: () => 0
    },
    paramsDate: {
      type: Object,
      default: () => {}
    },
    onTheJobTotal: {
      type: Number,
      default: () => 0
    },
    onThePostTotal: {
      type: Number,
      default: () => 0
    },
    personnelDirectionTotal: {
      type: Number,
      default: () => 0
    },
    personnelDirectionList: {
      type: Array,
      default: () => []
    },
    abnormalPersonTotal: {
      type: Number,
      default: () => 0
    },
    abnormalPersonList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {};
  },

  computed: {
    titleObj() {
      let titleDic = {
        0: ['在职人员', '在岗人员', '人员去向', '异动人员'],
        1: ['平均在职人员', '平均在岗人员', '累计人员去向', '累计异动人员'],
        2: ['平均在职人员', '平均在岗人员', '累计人员去向', '累计异动人员']
      };

      return {
        onTheJobTitle: titleDic[this.index][0],
        onThePostTitle: titleDic[this.index][1],
        personnelDirectionTitle: titleDic[this.index][2],
        abnormalPersonTitle: titleDic[this.index][3]
      };
    }
  },

  mounted() {
    if (this.index != 0) {
      const dom = document.querySelector('.module-list-container');
      dom.style.borderTop = 'none';
    }
  },

  methods: {
    handlePersonnelDirectionClick(e) {
      let searchParams = {
        pageNo: 1,
        pageSize: 99999
      };
      let pageSearchType = undefined;

      if (String(e.type) !== '1') {
        searchParams.endStartTime = this.paramsDate.start_date;
        searchParams.endTime = this.paramsDate.end_date;
        searchParams.sidx = 'create_date';
        searchParams.sord = 'desc';

        let toPageActive = Number(e.type) - 1 + '';
        searchParams.outType =
          toPageActive === '1'
            ? '进修'
            : toPageActive === '2'
            ? '规培'
            : toPageActive === '3'
            ? '学习与会议与公务外出'
            : '';

        searchParams.searchOutType = e.name;
        pageSearchType = 999;
      } else {
        pageSearchType = 1;
        searchParams.searchStartDate = this.paramsDate.start_date;
        searchParams.searchEndDate = this.paramsDate.end_date;
        searchParams.leaveType = e.name;
      }

      this.$refs.ComponentPersonDetails.show({
        type: pageSearchType,
        searchParams
      });
    },
    handleAbnormalPersonClick({ name }) {
      this.$refs.ComponentPersonDetails.show({
        type: 2,
        searchParams: {
          pageNo: 1,
          pageSize: 99999,
          effectiveStartDate: this.paramsDate.start_date,
          effectiveEndDate: this.paramsDate.end_date,
          causes: name,
          sidx: 'create_date',
          sord: 'desc'
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
//人员动态
.person-status-container {
  width: 100%;
  border: 1px solid #e7e7e7;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  padding: 0 16rpx;
  .more-deans-daily-number {
    width: 28rpx;
    height: 28rpx;
  }

  .person-number {
    display: flex;
    .person-number-item {
      flex: 1;
      border-right: 2rpx dashed #e7e7e7;
      padding: 16rpx 0;
      text-align: center;
      > view {
        font-size: 32rpx;
      }
      &:last-child {
        border-right: 0rpx;
      }
    }
  }

  .module-list-container {
    background-color: #ffffff;
    width: 100%;
    display: flex;
    border-top: 2rpx dashed #e7e7e7;
    .module-left {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-right: 2rpx dashed #e7e7e7;
      > view {
        font-size: 32rpx;
      }
    }
    .module-right {
      flex: 2;
      display: flex;
      flex-wrap: wrap;
      .module-item {
        width: 50%;
        height: 70rpx;
        padding: 8rpx 16rpx;
        padding-right: 16rpx;
        display: flex;
        justify-content: space-between;
        border-right: 2rpx dashed #e7e7e7;
        border-top: 2rpx dashed #e7e7e7;
        .more-container {
          display: flex;
          align-items: center;
        }
        .text-name,
        .text-value {
          color: #333;
          font-size: 32rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .text-value {
          transform: translateX(-8rpx);
        }
        &:nth-child(2n) {
          border-right: none;
        }
        &:nth-child(-n + 2) {
          border-top: none;
        }
      }
    }
  }
}
</style>
