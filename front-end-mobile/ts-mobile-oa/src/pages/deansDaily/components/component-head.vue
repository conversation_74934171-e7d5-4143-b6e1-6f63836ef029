<template>
  <view>
    <view class="logo-banner">
      <img class="logo-img" src="@/static/img/smlogo.png" />
    </view>

    <view class="page-subject-title">{{ pageSubjectTitle }}</view>
    <view class="page-date-title">{{ dailyBeginDate }}</view>
  </view>
</template>

<script>
export default {
  props: {
    pageSubjectTitle: {
      type: String,
      default: () => ''
    },
    dailyBeginDate: {
      type: String,
      default: () => ''
    }
  }
};
</script>

<style lang="scss" scoped>
.logo-banner {
  width: 300rpx;
  height: 108rpx;
  .logo-img {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
  }
}

.page-subject-title {
  text-align: center;
  font-weight: bold;
  font-size: 40rpx;
}

.page-date-title {
  text-align: center;
  font-size: 32rpx;
}
</style>
