<template>
  <view
    class="fixed-table"
    :class="{
      fixedheader: fixed,
      fixedFirst: firstFixed,
      fixedFirstAndSecond: fixedFirstAndSecond,
      stripedTable: stripe
    }"
  >
    <table cellspacing="0" border="0" cellpadding="0">
      <thead>
        <tr>
          <th
            v-for="(item, index) in header"
            :key="index"
            :class="{ border: border }"
            :style="{
              width:
                index === 0
                  ? 'var(--first-col-width)'
                  : index === 1
                  ? 'var(--second-col-width)'
                  : item.width + 'rpx'
            }"
          >
            <view
              class="item"
              :class="[item.align, item.overflow ? 'overflowtext' : '']"
            >
              {{ item.title }}
            </view>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in data" :key="index" @click="clickRow(item)">
          <td
            v-for="(v, idx) in header"
            :key="idx"
            :class="[border, item.className ? item.className : '']"
            :style="{
              width:
                idx === 0
                  ? 'var(--first-col-width)'
                  : idx === 1
                  ? 'var(--second-col-width)'
                  : v.width + 'rpx'
            }"
            @click="v.click && v.click(item[v.prop])"
          >
            <view
              class="item"
              :class="[v.align, v.overflow ? 'overflowtext' : '']"
              v-html="
                v.formatter ? v.formatter(item[v.prop], item) : item[v.prop]
              "
            >
            </view>
          </td>
        </tr>
      </tbody>
    </table>
  </view>
</template>

<script>
export default {
  props: {
    // 列表数据
    data: {
      type: Array,
      default: () => [],
      require: true
    },
    // 表头列表
    header: {
      type: Array,
      default: () => [],
      require: true
    },
    border: {
      type: Boolean,
      default: true
    },
    fixed: {
      type: Boolean,
      default: false
    },
    firstFixed: {
      type: Boolean,
      default: false
    },
    fixedFirstAndSecond: {
      type: Boolean,
      default: false
    },
    stripe: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    clickRow(item) {
      this.$emit('row-click', item);
    }
  }
};
</script>

<style lang="scss">
.fixed-table {
  --first-col-width: 60rpx; /* 第一列宽度 */
  --second-col-width: 170rpx; /* 第二列宽度 */
  overflow: auto;
  height: auto;

  * {
    box-sizing: border-box;
  }

  tbody {
    tr {
      &:last-child {
        td {
          background-color: #eff4fc !important;
        }
      }
    }
  }

  .phone_img {
    width: 36rpx;
    height: 36rpx;
    margin-right: 8rpx;
  }

  &.stripedTable {
    table {
      tbody {
        tr {
          &:nth-child(odd) {
            td {
              // background: #f7faff;
            }
          }
        }
      }
    }
  }

  td,
  th {
    border-bottom: 2rpx solid #eff1f4;
    padding: 8rpx !important;
    background: #fff;
  }

  &.fixedheader {
    table {
      table-layout: fixed;
      width: 100%;
    }
    thead tr th {
      position: sticky;
      top: 0;
      z-index: 1;
      font-weight: normal;
      background: #eff4fc;
    }
  }

  &.fixedFirstAndSecond {
    table {
      table-layout: fixed;
      width: 100%;

      td:first-child,
      th:first-child {
        position: sticky;
        left: 0;
        z-index: 2;
      }

      td:nth-child(2),
      th:nth-child(2) {
        box-shadow: 8rpx 10rpx 30rpx rgba(0, 0, 0, 0.2); /* 右侧阴影 */
        position: sticky;
        left: var(--first-col-width);
        z-index: 2;
      }

      th:first-child,
      th:nth-child(2) {
        z-index: 3;
      }

      thead {
        td:nth-child(2),
        th:nth-child(2) {
          box-shadow: none;
        }
      }
    }
  }

  .border {
    border-right: 2rpx solid #ebeef5;
  }

  .item {
    // text-align: center;
    // display: flex;
    // align-items: center;
    display: inline-block;
    width: 100%;
    font-size: 32rpx;

    &.left {
      text-align: left !important;
    }
    &.center {
      text-align: center !important;
    }
    &.right {
      text-align: right !important;
    }

    &.overflowtext {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      -webkit-box-orient: vertical;
    }
  }
}
</style>
