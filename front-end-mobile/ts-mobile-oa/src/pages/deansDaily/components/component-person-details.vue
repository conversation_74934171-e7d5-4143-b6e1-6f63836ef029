<template>
  <u-popup
    class="component-person-details"
    v-model="visible"
    mode="bottom"
    height="auto"
  >
    <view class="content-container">
      <view class="head">
        <img @tap.stop="handleClick" src="@/static/img/delete.svg" alt="" />
        <view class="title">{{ headTitle }}</view>
        <view></view>
      </view>
      <scroll-view class="view-scroll-container" scroll-y="true">
        <view>
          <u-table>
            <u-tr>
              <u-th width="60rpx">序</u-th>
              <u-th width="140rpx">姓名</u-th>
              <u-th v-if="isUnusualChange" width="160rpx">
                {{ leaveTypeTitle }}
              </u-th>
              <template v-if="isTransfer">
                <u-th>调出科室</u-th>
                <u-th>调入科室</u-th>
              </template>
              <u-th v-else>科室</u-th>
            </u-tr>
            <u-tr v-for="(data, index) of tableList" :key="index">
              <u-td class="td-styles" width="60rpx">{{ index + 1 }}</u-td>
              <u-td class="td-styles" width="140rpx">
                {{ data.employeeName }}
              </u-td>
              <u-td v-if="isUnusualChange" class="td-styles" width="160rpx">
                {{ data.leaveDays }}
              </u-td>
              <template v-if="isTransfer">
                <u-td class="td-styles">{{ data.oldOrgName }}</u-td>
                <u-td class="td-styles">{{ data.newOrgName }}</u-td>
              </template>
              <u-td v-else class="td-styles">{{ data.employeeDeptName }}</u-td>
            </u-tr>
          </u-table>
        </view>
      </scroll-view>
    </view>
  </u-popup>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      visible: false,
      headTitle: '',
      leaveTypeTitle: '请假天数',
      filterNames: ['admin', 'ts'],

      row: {},
      tableList: []
    };
  },
  computed: {
    isUnusualChange() {
      return this.row.type !== 2;
    },
    isTransfer() {
      return (
        this.row.type === 2 &&
        ['调出', '调入'].includes(this.row.searchParams.causes)
      );
    }
  },
  methods: {
    async show(row) {
      let _self = this;
      _self.row = row;
      if (row.type === 1) {
        await _self.handleGetLeaveReportStatistics(row.searchParams);
      }

      if (row.type === 2) {
        await _self.handleGetHandleGetLeaderDailyDetails(row.searchParams);
      }

      if (row.type === 999) {
        _self.leaveTypeTitle = '外出天数';
        await _self.handleGetDeansDailySearchOutRecordList(row.searchParams);
      }

      _self.visible = true;
    },
    async handleGetLeaveReportStatistics(datas) {
      let _self = this;
      const res = await _self.ajax.getLeaveReportStatistics(datas);
      res.rows = res.rows.filter(
        item =>
          item.sumDays > item.cancelLeaveDays &&
          !this.filterNames.includes(item.employeeName)
      );

      _self.headTitle = datas.leaveType + ' ' + res.rows.length + ' 人';
      res.rows.forEach(f => {
        f.employeeDeptName = f.orgName || '-';
        f.leaveDays = f.sumDays || 0;
      });
      _self.tableList = res.rows || [];
    },

    async handleGetHandleGetLeaderDailyDetails(datas) {
      let _self = this;
      const res = await _self.ajax.personnelTransactionList(datas);
      res.rows = res.rows.filter(
        ({ employeeName }) => !this.filterNames.includes(employeeName)
      );

      _self.headTitle = datas.causes + ' ' + res.rows.length + ' 人';
      res.rows.forEach(f => {
        f.employeeDeptName = f.oldOrgName || '-';
      });
      _self.tableList = res.rows || [];
    },

    async handleGetDeansDailySearchOutRecordList(datas) {
      let _self = this;
      const res = await _self.ajax.deansDailySearchOutRecordList(datas);
      res.rows = res.rows.filter(
        item =>
          item.status === '1' && !this.filterNames.includes(item.applyUserName)
      );

      _self.headTitle = datas.searchOutType + ' ' + res.rows.length + ' 人';
      res.rows.forEach(f => {
        f.employeeName = f.applyUserName;
        f.employeeDeptName = f.applyDeptName || '-';
        f.leaveDays = f.outDays || 0;
      });
      _self.tableList = res.rows || [];
    },

    handleClick() {
      let _self = this;
      _self.visible = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.component-person-details {
  ::v-deep {
    .u-drawer-content {
      border-radius: 12rpx 12rpx 0 0;
      max-height: 100vh;
    }
  }

  .content-container {
    padding: 0rpx 16rpx 46rpx 16rpx;
    border-radius: 24rpx 24rpx 0 0;
    background: #f0f3fb;
    position: relative;
    padding-top: 98rpx;
    padding-bottom: 32rpx;
    .head {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 98rpx;
      line-height: 98rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #f0f3fb;
      padding: 0rpx 16rpx;
      padding-left: 32rpx;
      img {
        width: 40rpx;
        height: 40rpx;
      }
      .title {
        font-size: 40rpx;
      }
    }

    .view-scroll-container {
      border-radius: 12rpx;
      background: #fff;
      max-height: calc(100vh - 152rpx);
      min-height: auto;
      padding-bottom: 32rpx;

      .td-styles {
        overflow: hidden !important;
        white-space: nowrap !important;
        text-overflow: ellipsis !important;
        display: inline-block;
      }
    }
  }
}
</style>
