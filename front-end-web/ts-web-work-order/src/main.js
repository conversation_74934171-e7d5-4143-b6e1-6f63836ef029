import './public-path';
import Vue from 'vue';
import ElementUi from '@trasen/trasen-element-ui/lib';
import App from './App.vue';
import { getRouter } from './router';
import component from '@/components/index.js';
import store from './store';
Vue.prototype._$store = store;
import unit from './unit/index';
import './icons'; // icon
// import '@/iconfont/font/iconfont.css';
// import '@/iconfont/awesome/font-awesome.min.css';
// import '@/iconfont/layuiFont/iconfont.css';
import '@/iconfont/index.js';
// import '@/iconfont/oaIcon/iconfont.css';
import axios from 'axios';
import { $api } from '@/api/ajax.js';
import * as echarts from 'echarts';
import tsEmpty from '@/components/trasen-empty/index.vue';
import dayjs from 'dayjs';
import tsFormItem from '@/extends/ts-form-item/index.js';

import tsElement from '@trasen-oa/trasen-ui-web';
Vue.use(tsElement);

Vue.component('tsEmpty', tsEmpty);
// axios.defaults.baseUrl = '';
Vue.use(unit); //初始化要使用的各种工具
Vue.use(component); //初始化要使用的各种组件
Vue.use(tsFormItem);
Vue.prototype.$axios = axios;
Vue.prototype.$echarts = echarts;
Vue.prototype.$api = $api;
Vue.prototype.$dayjs = dayjs;
Vue.config.productionTip = false;
let instance = null;

//路由切换刷新
Vue.mixin({
  // activated() {
  //   // this.refresh && this.refresh();
  // }
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.refresh && typeof vm.refresh == 'function' && vm.refresh();
    });
  }
});

function render(props = {}) {
  const router = getRouter(props);

  instance = new Vue({
    router,
    store,
    render: h => h(App)
  }).$mount(document.getElementById(process.env.VUE_APP_CONTAINER));
}

if (!window.__POWERED_BY_QIANKUN__) {
  render();
}
export async function bootstrap(props) {}
export async function mount(props) {
  Vue.prototype.qiankunParentNode = document.getElementById(
    process.env.VUE_APP_CONTAINER
  ).parentNode;
  // 设置主应用下发的方法
  ElementUi.Select.directives.Clickoutside._onMouse(); //启动ui框架的鼠标按下和松开事件
  Object.keys(props.fn).forEach(method => {
    Vue.prototype[`$${method}`] = props.fn[method];
  });
  //监听主应用下发用户信息
  props.onGlobalStateChange((state, prevState) => {});
  // 设置通讯
  store.state.common.userInfo = props.fn.getUserInfo(); //主动获取用户信息
  store.commit('common/setData', { label: 'token', value: props.data.token });
  store.commit('common/setData', {
    label: 'hospitalCode',
    value: props.data.hospitalCode
  });
  store.commit('common/setData', {
    label: 'systemCustomCode',
    value: props.data.systemCustomCode
  });
  store.commit('common/setData', {
    label: 'personalSortData',
    value: props.data.personalSortData
  });
  Vue.prototype.$config = props.config; //配置信息
  Vue.prototype.$onGlobalStateChange = props.onGlobalStateChange;
  Vue.prototype.$setGlobalState = props.setGlobalState;
  render(props);
}
export async function update(props) {
  instance.$emit('updateDataQianKun', props);
}

export async function unmount() {
  ElementUi.Select.directives.Clickoutside._offMouse(); //卸载ui框架的鼠标按下和松开事件
  instance.$destroy();
}
