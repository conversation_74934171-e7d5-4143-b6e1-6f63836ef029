<template>
  <div class="content">
    <img :src="bgImage" class="empty" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      bgImage: require('@/assets/img/other/empty.png')
    };
  }
};
</script>
<style scoped lang="scss">
.empty {
  height: 100%;
  width: 100%;
}
.content {
  position: relative;
  &::before {
    content: '暂 无 数 据';
    position: absolute;
    bottom: 10%;
    left: 0px;
    right: 0px;
    text-align: center;
    color: rgb(51, 51, 51);
    opacity: 0.25;
    z-index: 9;
  }
}
</style>
