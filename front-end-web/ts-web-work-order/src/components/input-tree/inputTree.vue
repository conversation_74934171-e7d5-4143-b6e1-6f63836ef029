<template>
  <div ref="bodydiv">
    <el-popover
      ref="popoverRef"
      placement="bottom-start"
      :visible-arrow="false"
      :width="poppverWidth"
      :disabled="disabled"
    >
      <el-tree
        ref="elTree"
        :data="treeData"
        :node-key="nodeKey"
        :props="treeProp"
        class="treeInput"
        empty-text="暂无数据"
        :expand-on-click-node="false"
        highlight-current
        @node-click="handleNodeClick"
        :filter-node-method="filterNode"
      >
        <span class="custom-tree-node" slot-scope="{ node }">
          <span class="flex-col-center"
            ><i
              :class="
                node.level == 1
                  ? 'first-tree-icon'
                  : !node.isLeaf && (node.childNodes || []).length > 0
                  ? 'has-child-tree-icon'
                  : 'leaf-tree-icon'
              "
            ></i
            >{{ node.label }}</span
          >
        </span>
      </el-tree>
      <el-input
        slot="reference"
        v-model="fullPath"
        :placeholder="placeholder"
        @input="handleInputChange"
        @click.native="handleInputClick"
        class="addform-input"
        :disabled="disabled"
        @blur="handleBlur"
      >
        <i
          slot="suffix"
          class="el-input__icon el-icon-circle-close el-input__clear input-clear-icon"
          :class="{ 'has-value-input': fullPath }"
          @click.stop="handleInputClear"
        ></i>
      </el-input>
    </el-popover>
  </div>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'valuechange'
  },
  props: {
    nodeKey: {
      type: String,
      default: () => {
        return 'id';
      }
    },
    treeData: {
      type: Array,
      default: () => {
        return [];
      }
    },
    treeProp: {
      type: Object,
      default: () => {
        return {
          label: 'name',
          children: 'children'
        };
      }
    },
    textName: {
      type: String,
      default: () => {
        return 'name';
      }
    },
    value: {
      default: () => {
        return null;
      }
    },
    //input的placeholder
    placeholder: {
      type: String,
      default: () => {
        return '请输入';
      }
    },
    disabled: {
      type: Boolean,
      default: () => false
    },
    dbClickQuitSelect: {
      type: Boolean,
      default: () => false
    }
  },
  mounted() {
    this.poppverWidth = this.$refs.bodydiv.clientWidth;
    this.poppverWidth > 300 ? null : (this.poppverWidth = 300);
    window.addEventListener('click', this.handleWindowClick);
  },
  data() {
    return {
      fullPath: '', //input全路径展示
      oldFullPath: '', //input之前的数据
      inputTimer: null, //输入防抖
      poppverWidth: null //气泡框的宽度
    };
  },
  watch: {
    value: {
      handler(val) {
        if (!val) {
          this.$refs.elTree && this.$refs.elTree.setCurrentKey(null);
        }
        this.$refs.elTree && this.$refs.elTree.setCurrentKey(val);
        this.findNode(this.treeData);
      },
      immediate: true
    },
    treeData: {
      handler() {
        if (!this.fullPath) {
          this.findNode(this.treeData);
        }
      },
      immediate: true
    }
  },
  methods: {
    /**@desc 处理点击事件 判断是否在组件内 */
    handleWindowClick(e) {
      let content = this.$refs.bodydiv;
      if (!content.contains(e.target)) {
        this.value && this.findNode();
        this.$refs.elTree && this.$refs.elTree.filter();
      }
    },
    findNode(treeCol) {
      for (let item of treeCol) {
        if (item[this.nodeKey] == this.value) {
          this.fullPath = item[this.textName];
        }

        if (item[this.treeProp.children]) {
          this.findNode(item[this.treeProp.children]);
        }
      }
    },
    //处理节点点击事件
    handleNodeClick(val, node, tree) {
      if (this.value == val[this.nodeKey]) {
        if (this.dbClickQuitSelect) {
          this.$refs.elTree.setCurrentKey(null);
          this.$emit('valuechange', null);
          this.$emit('change', null, null, tree);
        }
      } else {
        this.$emit('valuechange', val[this.nodeKey]);
        this.$emit('change', val, node, tree);
      }
      this.$refs.popoverRef.doClose();
      this.$nextTick(() => {
        this.$refs.elTree.filter();
      });
    },
    //处理input失焦事件
    handleBlur() {
      this.findNode(this.treeData);
    },
    //节点过滤
    filterNode(value, data, node) {
      if (!value) {
        return true;
      }
      let parentNode = node.parent,
        labels = [node.label],
        level = 1;
      while (level < node.level) {
        labels = [...labels, parentNode.label];
        parentNode = parentNode.parent;
        level++;
      }
      return labels.some(label => label.indexOf(value) !== -1);
    },
    // 处理输入框清空操作
    handleInputClear() {
      this.$emit('valuechange', null);
      this.$emit('change', null, null, this.$refs.elTree);
      this.$refs.popoverRef.doClose();
    },
    //处理input 改变，搜索下拉框
    handleInputChange() {
      this.inputTimer && clearTimeout(this.inputTimer);
      this.inputTimer = setTimeout(() => {
        this.$refs.elTree.filter(this.fullPath);
      }, 500);
    },
    handleInputClick() {
      this.$refs.elTree.setCurrentKey(this.value);
    }
  },
  beforeDestroy() {
    window.removeEventListener('click', this.handleWindowClick);
  }
};
</script>

<style lang="scss" scoped>
.popover {
  width: 100%;
}
.treeInput {
  max-height: 250px;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &:hover::-webkit-scrollbar-thumb {
    border-radius: 6px;
    height: 50px;
    background: rgba(153, 153, 153, 0.4);
    &:hover {
      background-color: rgba(153, 153, 153, 0.8);
    }
  }
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    background: #fff;
  }
  &.el-tree {
    /deep/.el-tree-node__content {
      // display: block !important;
    }
    /deep/.el-tree-node__children {
      // overflow: visible !important;
    }
  }
}
.first-tree-icon {
  background: url(../../assets/img/other/ztree_all.png) no-repeat;
  display: inline-block;
  height: 16px;
  width: 16px;
  margin-right: 2px;
}
.has-child-tree-icon {
  background: url(../../assets/img/other/ztree_folder.png) no-repeat;
  display: inline-block;
  height: 16px;
  width: 16px;
  margin-right: 2px;
}
.leaf-tree-icon {
  background: url(../../assets/img/other/ztree_file.png) no-repeat;
  display: inline-block;
  height: 16px;
  width: 16px;
  margin-right: 2px;
}
/deep/ {
  .addform-input .input-clear-icon {
    display: none;
  }
  .addform-input:hover .has-value-input {
    display: inline-block;
  }
}
</style>
