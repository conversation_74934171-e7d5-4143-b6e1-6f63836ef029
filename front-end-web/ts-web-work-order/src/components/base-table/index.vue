<script>
import scrollJS from './scroll-event';

export default {
  name: 'BaseTable',
  mixins: [scrollJS],
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    pager: {
      type: Boolean,
      default: () => true
    },
    pageSizes: {
      type: Array,
      default: () => [20, 40, 60, 80, 100, 200, 500, 1000, 2000]
    },
    layout: {
      type: String,
      default: () => 'total, sizes, prev, pager, next, jumper'
    }
  },
  data() {
    return {
      pageNo: 1,
      pageSize: 100,
      total: 0,
      tableData: []
    };
  },
  computed: {
    hasIndex() {
      return this.columns.some(item => item.type == 'index');
    }
  },
  render() {
    let tableAttrs = { ...this.$attrs, ...this.$props },
      { pageSizes, layout } = this.$props,
      pagerAttrs = {
        pageSizes,
        layout,
        currentPage: this.pageNo,
        pageSize: this.pageSize,
        total: this.total
      };
    delete tableAttrs.pager;
    delete tableAttrs.pageSizes;
    delete tableAttrs.layout;
    tableAttrs.data = this.tableData;

    let childNodeList = [
      <div class="table-content" ref="tableContainer">
        <ElScrollbar
          ref="scroll"
          style="width: 100%; height: 100%"
          attrs={{
            viewClass: 'scroll-table-container',
            wrapClass: 'scroll-wrap-class'
          }}>
          <TsTable
            ref="tsTable"
            class={'table-head' + (this.hasIndex ? ' has-index-col' : '')}
            attrs={tableAttrs}
            on={{
              ...this.$listeners,
              'header-dragend': this.handleDragend
            }}
            scopedSlots={this.$scopedSlots}></TsTable>
        </ElScrollbar>
      </div>
    ];

    if (this.pager) {
      childNodeList.push(
        <TsPagination
          class="pager-content"
          attrs={pagerAttrs}
          on={{
            'current-change': this.handleCurrentChange,
            'size-change': this.handlePageSizeChange,
            'update:pageSize': $event => (this.pageSize = $event),
            'update:currentPage': $event => (this.pageNo = $event)
          }}></TsPagination>
      );
    }

    return <div class="base-table">{childNodeList}</div>;
  },
  methods: {
    refresh(data = {}) {
      let { totalCount = 0, rows = [] } = data;
      this.tableData = rows;
      this.total = totalCount;
    },
    handleDragend() {
      if (this.$listeners.headerDragend) {
        this.$listeners.headerDragend(...arguments);
      }
      if (this.$listeners['header-dragend']) {
        this.$listeners['header-dragend'](...arguments);
      }
      this.computedTableWidth();
    },
    handleCurrentChange() {
      this.$emit('refresh');
    },
    handlePageSizeChange() {
      this.pageNo = 1;
      this.$emit('refresh');
    }
  }
};
</script>

<style lang="scss" scoped>
.base-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.table-content {
  flex: 1;
  overflow: hidden;
  transform: scale(1);
}
.table-head {
  border: none;
}
.table-head ::v-deep .el-table__header-wrapper {
  position: fixed;
  top: 0;
  z-index: 2;
  max-width: unset;
  width: inherit;
  overflow: visible;
}
::v-deep {
  .el-scrollbar__bar {
    z-index: 8;
  }
  .el-table__fixed-header-wrapper {
    z-index: 4;
    .el-table__header th {
      border-bottom: none;
    }
  }
  .el-table__fixed-right {
    z-index: 2;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 12%);
    height: 100% !important;
  }
  .scroll-wrap-class {
    height: calc(100% + 17px);
    border-left: 1px solid $theme-border-color;
  }
}
::v-deep .scroll-table-container .el-table {
  position: static;
  .el-table__fixed-body-wrapper .el-table__cell {
    padding: 5px 0;
  }
  thead {
    color: #333333;
  }
}
.pager-content {
  margin-top: $theme-interval;
  text-align: right;
}
.has-index-col
  ::v-deep
  .el-table__body-wrapper
  .el-table__row
  td:first-child
  .cell {
  padding: 0;
  min-width: unset;
}
</style>
