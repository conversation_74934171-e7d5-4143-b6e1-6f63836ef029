import { DisplayObject } from 'pixi.js';

/**
 * 屏幕上呈现的所有对象的基类。
 * 这是一个抽象类，不能单独使用。而是应该扩展它。
 */

export default {
  props: {
    alpha: Number, // 透明度
    // buttonMode: Number,
    // cacheAsBitmap: Number,
    cursor: String, // 鼠标光标悬停在displayObject上时使用的光标模式。
    // filterArea: Number,
    // filters: Number,
    // hitArea: Number,
    // interactive: Number,
    // localTransform: Number,
    // mask: Number,
    name: String,
    // parent: Number,
    pivotX: Number, // pivot:此显示对象在其本地空间中的旋转，缩放和倾斜中心。默认情况下，枢轴是原点（0，0）。
    pivotY: Number,
    // renderable: Number,
    rotation: Number, // 物体的旋转度（以弧度为单位）。“旋转”和“角度”对显示对象具有相同的作用；旋转单位为弧度，角度单位为度。
    scaleX: Number, // 缩放
    scaleY: Number,
    skewX: Number, // 倾斜
    skewY: Number,
    visible: Boolean, // 可见
    x: Number, // x坐标相对于父元素
    y: Number // y坐标相对于父元素
  },
  computed: {
    instance: () => new DisplayObject()
  },
  inject: ['app', 'pixiObjects', 'charm'],
  provide() {
    const vm = this;
    return {
      pixiObject: {
        get inst() {
          return vm.instance;
        }
      }
    };
  },
  // created () { this.vglNamespace.update() },
  // beforeUpdate () { this.vglNamespace.update() },
  beforeDestroy() {
    // 离开页面时，移除所有子类
    const { pixiObjects, instance, name } = this;
    if (instance.parent) instance.parent.removeChild(instance);
    if (pixiObjects[name] === instance) delete pixiObjects[name];
    // vglNamespace.update();
  },
  watch: {
    instance: {
      handler(newInstance, oldInstance) {
        if (oldInstance && oldInstance.parent && newInstance !== oldInstance) {
          // 如果之前的实例存在，并且父类存在，则移除
          oldInstance.parent.removeChild(oldInstance);
        }
        if (this.$parent.instance) {
          // 再重新添加新的实例
          this.$parent.instance.addChild(newInstance);
        }
        if (this.x) newInstance.x = this.x;
        if (this.y) newInstance.y = this.y;
        if (this.alpha) newInstance.alpha = this.alpha;
        if (this.cursor) newInstance.cursor = this.cursor;
        if (this.rotation)
          newInstance.rotation = (Math.PI / 180) * this.rotation;
        if (this.visible) newInstance.visible = this.visible;
        if (this.skewX || this.skewY)
          newInstance.skew.set(this.skewX || 1, this.skewY || 1);
        if (this.scaleX != undefined || this.scaleY != undefined)
          newInstance.scale.set(this.scaleX, this.scaleY);
        if (this.pivotX || this.pivotY)
          newInstance.pivot.set(this.pivotX || 1, this.pivotY || 1);
        if (this.name !== undefined) this.pixiObjects[this.name] = newInstance;
      },
      immediate: true
    },
    '$parent.instance': function parentInstance(instance) {
      // 父类实例发生变化，重新设置父类
      this.instance.setParent(instance);
    },
    x: function(x) {
      this.instance.x = x;
    },
    y: function(y) {
      this.instance.y = y;
    },
    alpha: function(alpha) {
      this.instance.alpha = alpha;
    },
    cursor: function(cursor) {
      this.instance.cursor = cursor;
    },
    rotation: function(rotation) {
      this.instance.rotation = rotation;
    },
    visible: function(visible) {
      this.instance.visible = visible;
    },
    skewX: function(skewX) {
      this.instance.skew.x = skewX;
    },
    skewY: function(skewY) {
      this.instance.skew.y = skewY;
    },
    scaleX: function(scaleX) {
      this.instance.scale.x = scaleX;
    },
    scaleY: function(scaleY) {
      this.instance.scale.y = scaleY;
    },
    pivotX: function(pivotX) {
      this.instance.pivot.x = pivotX;
    },
    pivotY: function(pivotY) {
      this.instance.pivot.y = pivotY;
    },
    name(newName, oldName) {
      const { pixiObjects, instance } = this;
      if (pixiObjects[oldName] === instance) delete pixiObjects[oldName];
      pixiObjects[newName] = instance;
    }
  }
};
