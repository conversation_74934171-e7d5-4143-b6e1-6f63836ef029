<template>
  <div class="pixi-renderer">
    <!-- 所有的子<模板>元素都被添加到这里 -->
    <slot></slot>
  </div>
</template>

<script>
/**
 * 画布组件：所有包含在画布组件下的子模版元素都会渲染出来
 * 根据pixi application api设置props中属性，将canvas标签插入到dom中，设置游戏循环update charm中动画效果并回调到页面
 */
import PIXI, { Application } from 'pixi.js';
import Charm from '../animation/charm';

export default {
  props: {
    height: Number, // 画布高度
    width: Number, // 画布宽度
    autoResize: Boolean, // 确认宽高的格式正确
    backgroundColor: Number, // 背景颜色
    clearBeforeRender: Boolean, // 新的渲染过程之前清除画布
    preserveDrawingBuffer: Boolean, // 渲染后是否保留模板缓冲区的内容
    resolution: Number, // 渲染器的分辨率/设备像素比。
    // 如果为true，则在渲染时，PixiJS将使用Math.floor（）x / y值，从而停止像素插值。
    // 优点包括更清晰的图像质量（如文本）和在画布上更快的渲染。主要缺点是物体的运动可能看起来不太顺畅。
    // 要设置全局默认值，请更改PIXI.settings.ROUND_PIXELS
    roundPixels: Boolean,
    // 将整个Canvas标签的透明度进行设置
    transparent: Boolean
  },
  data() {
    const options = { width: this.width, height: this.height };
    if (this.autoResize) options.autoResize = this.autoResize;
    if (this.backgroundColor) {
      options.backgroundColor = this.backgroundColor;
    }
    if (this.clearBeforeRender) {
      options.clearBeforeRender = this.clearBeforeRender;
    }
    if (this.preserveDrawingBuffer) {
      options.preserveDrawingBuffer = this.preserveDrawingBuffer;
    }
    if (this.resolution) options.resolution = this.resolution;
    if (this.roundPixels) options.roundPixels = this.roundPixels;
    if (this.transparent) options.transparent = this.transparent;
    // 使用canvas渲染
    // options.forceCanvas = true
    return {
      app: new Application(options),
      charm: new Charm(PIXI),
      pixiObjects: {}
    };
  },
  provide() {
    return {
      app: this.app,
      pixiObjects: this.pixiObjects,
      charm: this.charm
    };
  },
  computed: {
    stage() {
      return this.app.stage;
    },
    instance() {
      return this.app.stage;
    },
    renderer() {
      return this.app.renderer;
    }
  },
  mounted() {
    // 将canvas标签加入到dom节点中
    this.$el.appendChild(this.app.view);
    // 创建游戏循环，每秒执行60帧。更新charm动画库中的动画
    this.app.ticker.add(delta => {
      this.charm.update();
      // 回调到页面
      this.$emit('tick', delta);
    });
  },
  watch: {
    autoResize: function(autoResize) {
      this.renderer.autoResize = autoResize;
    },
    backgroundColor: function(backgroundColor) {
      this.renderer.backgroundColor = backgroundColor;
    },
    clearBeforeRender: function(clearBeforeRender) {
      this.renderer.clearBeforeRender = clearBeforeRender;
    },
    preserveDrawingBuffer: function(preserveDrawingBuffer) {
      this.renderer.preserveDrawingBuffer = preserveDrawingBuffer;
    },
    resolution: function(resolution) {
      this.renderer.resolution = resolution;
    },
    roundPixels: function(roundPixels) {
      this.renderer.roundPixels = roundPixels;
    },
    transparent: function(transparent) {
      this.renderer.transparent = transparent;
    },
    height: function(height) {
      this.renderer.resize(this.width, height);
    },
    width: function(width) {
      this.renderer.resize(width, this.height);
    }
  }
};
</script>

<style>
.pixi-renderer {
  display: inline-block;
  line-height: 0;
}

.pixi-renderer div {
  display: none;
}
</style>
