const { name } = require('./package.json');
const CKEditorWebpackPlugin = require('@ckeditor/ckeditor5-dev-webpack-plugin');
const { styles } = require('@ckeditor/ckeditor5-dev-utils');
const path = require('path');
// const webpack = require('webpack');
// const CompressionWebpackPlugin = require('compression-webpack-plugin');
// const productionGzipExtensions = require('')
function resolve(dir) {
  return path.join(__dirname, dir);
}
process.env.VUE_APP_PORT = 6001; //设置端口号
process.env.VUE_APP_BASE_URL = `/${name}/`;
process.env.VUE_APP_NAME = name; //项目名称 很重要 要写
let containerId = `${name}-${Math.random(15)}${new Date().getTime()}`;
process.env.VUE_APP_CONTAINER = containerId;

module.exports = {
  productionSourceMap: false,
  publicPath: `${process.env.VUE_APP_BASE_URL}`,
  transpileDependencies: [/ckeditor5-[^/\\]+[/\\]src[/\\].+\.js$/],
  chainWebpack: config => {
    config.output.filename('[name].[hash].js').end();
    config.plugins.delete('preload');
    config.plugins.delete('prefetch');
    config.resolve.symlinks(false);
    const svgRule = config.module.rule('svg');
    svgRule.exclude.add(path.join(__dirname, 'node_modules', '@ckeditor'));

    config.module
      .rule('cke-svg')
      .test(/ckeditor5-[^/\\]+[/\\]theme[/\\]icons[/\\][^/\\]+\.svg$/)
      .use('raw-loader')
      .loader('raw-loader');

    config.module
      .rule('images')
      .use('url-loader')
      .loader('url-loader')
      .tap(options => Object.assign(options, { limit: 5000 }));

    config.module
      .rule('cke-css')
      .test(/ckeditor5-[^/\\]+[/\\].+\.css$/)
      .use('postcss-loader')
      .loader('postcss-loader')
      .tap(() => {
        return styles.getPostCssConfig({
          themeImporter: {
            themePath: require.resolve('@ckeditor/ckeditor5-theme-lark')
          },
          minify: true
        });
      });
    config.module
      .rule('images')
      .use('url-loader')
      .loader('url-loader')
      .options({})
      .end();
    config.module
      .rule('fonts')
      .test(/.(ttf|otf|eot|woff|woff2)$/)
      .use('url-loader')
      .loader('url-loader')
      .tap(options => {
        options = {
          // limit: 10000,
          name: '/static/fonts/[name].[ext]'
        };
        return options;
      })
      .end();
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end();
  },
  configureWebpack: {
    output: {
      library: `${name}`,
      libraryTarget: 'umd',
      jsonpFunction: `webpackJsonp_${name}`
    }
    // plugins: [
    //     new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),

    //     // 下面是下载的插件的配置
    //     new CompressionWebpackPlugin({
    //         algorithm: 'gzip',
    //         test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),
    //         threshold: 10240,
    //         minRatio: 0.8
    //     }),
    //     new webpack.optimize.LimitChunkCountPlugin({
    //         maxChunks: 5,
    //         minChunkSize: 100
    //     })
    // ]
  },
  css: {
    loaderOptions: {
      scss: {
        prependData: `@import "@/assets/css/var.scss";`
      }
    }
  },
  devServer: {
    port: process.env.VUE_APP_PORT,
    headers: {
      'Access-Control-Allow-Origin': '*' //开发模式下给微前端服务使用
    },
    proxy: {
      '/ts-worksheet': {
        target: 'http://**************:9835',
        // target: 'http://*************:9088/', //测试环境
        secure: false,
        changeOrigin: true
      },
      '/ts-document': {
        target: 'http://*************:9088',
        secure: false,
        changeOrigin: true
      },
      '/ts-basics-bottom': {
        target: 'http://*************:9088',
        secure: false,
        changeOrigin: true
      }
    }
  }
};
